# Student Subscription Management with Refunds & Cancellation

This document describes the enhanced student subscription management system including viewing subscriptions, requesting refunds, and canceling subscriptions with reasons.

## 📚 **Overview**

Students can now:
1. **View detailed subscription information** with lesson statistics and billing details
2. **Cancel subscriptions** with mandatory reasons and optional refund requests
3. **Request refunds** through the integrated refund system
4. **Track refund status** and view refund history

## 🔧 **Enhanced Features**

### **1. Detailed Subscription Viewing**
- Comprehensive subscription information with lesson statistics
- Billing details and next payment dates
- Pending refund requests status
- Available actions (cancel, pause, refund)

### **2. Subscription Cancellation with Reasons**
- Mandatory cancellation reason requirement
- Optional detailed description
- Integrated refund request during cancellation
- Audit trail for all cancellations

### **3. Refund Request System**
- Request refunds for subscriptions or individual lessons
- Track refund status (pending, approved, rejected, processed)
- Admin approval workflow
- Transparent refund history

## 📋 **API Endpoints**

### **1. Get Student Subscriptions (Enhanced)**
**GET** `/api/subscription/students/:studentId/subscriptions`

#### **Query Parameters:**
- `status` (optional): Filter by status (`active`, `paused`, `cancelled`, `pending_transfer`, `incomplete`)
- `page` (optional): Page number (default: 1)
- `limit` (optional): Results per page (default: 10)

#### **Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "subscription_id",
      "studentId": "student_id",
      "tutorId": {
        "id": "tutor_id",
        "firstname": "John",
        "lastname": "Smith",
        "basePrice": 25,
        "teachingSubjects": ["Mathematics", "Physics"],
        "rating": 4.8
      },
      "planType": "3_lessons_weekly",
      "lessonsPerWeek": 3,
      "monthlyPrice": 330,
      "status": "active",
      "currentPeriodStart": "2024-01-01T00:00:00.000Z",
      "currentPeriodEnd": "2024-01-31T23:59:59.000Z",
      "autoRenew": true,
      "lessonStats": {
        "totalLessons": 12,
        "completedLessons": 8,
        "scheduledLessons": 3,
        "cancelledLessons": 1
      },
      "pendingRefunds": [
        {
          "id": "refund_id",
          "amount": 100,
          "reason": "Tutor unavailable",
          "requestedAt": "2024-01-15T10:00:00.000Z"
        }
      ],
      "billing": {
        "nextBillingDate": "2024-01-31T23:59:59.000Z",
        "daysUntilBilling": 15,
        "canCancel": true,
        "canRequestRefund": false
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 3,
    "pages": 1
  },
  "summary": {
    "totalSubscriptions": 3,
    "activeSubscriptions": 2,
    "cancelledSubscriptions": 1,
    "pausedSubscriptions": 0
  }
}
```

### **2. Get Detailed Subscription Information**
**GET** `/api/subscription/:subscriptionId/details`

#### **Response:**
```json
{
  "success": true,
  "data": {
    "subscription": {
      "id": "subscription_id",
      "planType": "3_lessons_weekly",
      "lessonsPerWeek": 3,
      "monthlyPrice": 330,
      "status": "active",
      "lessonStats": {
        "totalLessons": 12,
        "completedLessons": 8,
        "scheduledLessons": 3,
        "cancelledLessons": 1,
        "noShowLessons": 0
      },
      "billing": {
        "nextBillingDate": "2024-01-31T23:59:59.000Z",
        "daysUntilBilling": 15,
        "currentPeriodStart": "2024-01-01T00:00:00.000Z",
        "currentPeriodEnd": "2024-01-31T23:59:59.000Z",
        "autoRenew": true
      },
      "pricing": {
        "basePricePerLesson": 25,
        "lessonsPerWeek": 3,
        "baseMonthlyPrice": 300,
        "processingFee": 30,
        "totalMonthlyPrice": 330,
        "currency": "USD"
      },
      "actions": {
        "canCancel": true,
        "canPause": true,
        "canRequestRefund": true,
        "canSwitchTutor": true
      }
    },
    "recentLessons": [
      {
        "id": "lesson_id",
        "title": "Math Tutoring",
        "scheduledTime": "2024-01-15T14:00:00.000Z",
        "status": "completed",
        "duration": 60,
        "isFreeTrial": false
      }
    ],
    "refundRequests": [
      {
        "id": "refund_id",
        "amount": 100,
        "reason": "Tutor unavailable",
        "status": "pending",
        "createdAt": "2024-01-15T10:00:00.000Z",
        "processedAt": null,
        "rejectionReason": null,
        "adminNotes": null,
        "processedBy": null
      }
    ]
  }
}
```

### **3. Cancel Subscription with Reason**
**DELETE** `/api/subscription/:subscriptionId/cancel`

#### **Request Body:**
```json
{
  "reason": "Found a better tutor",
  "description": "The tutor's schedule doesn't match mine anymore",
  "requestRefund": true,
  "refundAmount": 165.00
}
```

#### **Required Fields:**
- `reason` (string): Mandatory cancellation reason
- `description` (string, optional): Additional details
- `requestRefund` (boolean, optional): Whether to request a refund
- `refundAmount` (number, optional): Amount to refund (required if requestRefund is true)

#### **Response:**
```json
{
  "success": true,
  "message": "Subscription cancelled successfully and refund request submitted. You can continue using your remaining lessons until the end of the current billing period.",
  "data": {
    "subscription": {
      "id": "subscription_id",
      "status": "cancelled",
      "cancellationReason": "Found a better tutor",
      "cancellationDescription": "The tutor's schedule doesn't match mine anymore",
      "cancelledAt": "2024-01-15T15:30:00.000Z"
    },
    "cancellation": {
      "reason": "Found a better tutor",
      "description": "The tutor's schedule doesn't match mine anymore",
      "cancelledAt": "2024-01-15T15:30:00.000Z",
      "refundRequest": {
        "id": "refund_request_id",
        "amount": 165.00,
        "status": "pending",
        "reason": "Subscription cancellation: Found a better tutor",
        "createdAt": "2024-01-15T15:30:00.000Z"
      }
    }
  }
}
```

### **4. Create Refund Request**
**POST** `/api/withdrawal/refund-requests`

#### **Request Body:**
```json
{
  "requestedAmount": 100.00,
  "refundType": "subscription",
  "reason": "Tutor frequently cancels lessons",
  "description": "The tutor has cancelled 3 out of 5 scheduled lessons this month",
  "subscriptionId": "subscription_id"
}
```

#### **Response:**
```json
{
  "success": true,
  "message": "Refund request submitted successfully",
  "data": {
    "id": "refund_request_id",
    "studentId": "student_id",
    "subscriptionId": "subscription_id",
    "requestedAmount": 100.00,
    "refundType": "subscription",
    "reason": "Tutor frequently cancels lessons",
    "description": "The tutor has cancelled 3 out of 5 scheduled lessons this month",
    "status": "pending",
    "createdAt": "2024-01-15T10:00:00.000Z"
  }
}
```

### **5. Get Student Refund Requests**
**GET** `/api/withdrawal/refund-requests`

#### **Query Parameters:**
- `status` (optional): Filter by status (`pending`, `approved`, `rejected`, `processed`, `cancelled`)
- `page` (optional): Page number (default: 1)
- `limit` (optional): Results per page (default: 10)

#### **Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "refund_request_id",
      "requestedAmount": 100.00,
      "refundType": "subscription",
      "reason": "Tutor frequently cancels lessons",
      "status": "pending",
      "createdAt": "2024-01-15T10:00:00.000Z",
      "subscription": {
        "id": "subscription_id",
        "planType": "3_lessons_weekly",
        "monthlyPrice": 330,
        "status": "active"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "pages": 1
  }
}
```

### **6. Cancel Refund Request**
**DELETE** `/api/withdrawal/refund-requests/:requestId/cancel`

#### **Response:**
```json
{
  "success": true,
  "message": "Refund request cancelled successfully",
  "data": {
    "id": "refund_request_id",
    "status": "cancelled",
    "cancelledAt": "2024-01-15T16:00:00.000Z"
  }
}
```

## 🔍 **Common Cancellation Reasons**

### **Predefined Reason Categories:**
1. **Schedule Conflicts**
   - "Tutor's schedule doesn't match mine"
   - "Need different time slots"
   - "Timezone issues"

2. **Quality Issues**
   - "Teaching style doesn't suit me"
   - "Tutor frequently cancels lessons"
   - "Not satisfied with lesson quality"

3. **Personal Circumstances**
   - "Financial constraints"
   - "No longer need tutoring"
   - "Found alternative learning method"

4. **Platform Issues**
   - "Technical difficulties"
   - "Platform not user-friendly"
   - "Billing issues"

5. **Other**
   - "Found a better tutor"
   - "Switching to different subject"
   - "Custom reason..."

## 💰 **Refund Policy Integration**

### **Refund Eligibility:**
- **Full Refund**: Within 24 hours of subscription start
- **Partial Refund**: Based on unused lessons and time remaining
- **No Refund**: After 80% of billing period has passed

### **Refund Processing:**
1. **Student Request**: Submit refund request with reason
2. **Admin Review**: Admin reviews and approves/rejects
3. **Processing**: Approved refunds are processed via Stripe
4. **Notification**: Student receives email confirmation

## 🚀 **Usage Examples**

### **View Student Subscriptions:**
```bash
curl -X GET "http://localhost:3000/api/subscription/students/student_id/subscriptions?status=active" \
  -H "Authorization: Bearer student-jwt-token"
```

### **Get Detailed Subscription Info:**
```bash
curl -X GET "http://localhost:3000/api/subscription/subscription_id/details" \
  -H "Authorization: Bearer student-jwt-token"
```

### **Cancel Subscription with Refund:**
```bash
curl -X DELETE "http://localhost:3000/api/subscription/subscription_id/cancel" \
  -H "Authorization: Bearer student-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "reason": "Found a better tutor",
    "description": "Schedule conflicts with current tutor",
    "requestRefund": true,
    "refundAmount": 165.00
  }'
```

### **Request Standalone Refund:**
```bash
curl -X POST "http://localhost:3000/api/withdrawal/refund-requests" \
  -H "Authorization: Bearer student-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "requestedAmount": 100.00,
    "refundType": "subscription",
    "reason": "Tutor frequently cancels lessons",
    "subscriptionId": "subscription_id"
  }'
```

---

**✅ Students now have complete control over their subscriptions with transparent refund processes and detailed tracking!**
