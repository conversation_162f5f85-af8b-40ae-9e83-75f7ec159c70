import axios from "axios";
import { generateAgoraChatToken } from "../utils/hashing";
import { getErrorResponse } from "../middlewares/errorHandler";
import { TChatGroup } from "../models/classroom";

type TCreateChatUser = { username: string };

const BASE_URL = "https://a41.chat.agora.io";

const BASE_API_URL = `${BASE_URL}/${process.env.AGORA_ORG_NAME}/${process.env.AGORA_APP_NAME}`;

export type TChatUser = {
  id: string;
  username: string;
  updatedAt: number;
  createdAt: number;
  activated: boolean;
  uuid: string;
};

export const chatToken = generateAgoraChatToken();

const agoraChatAxios = axios.create({
  baseURL: BASE_API_URL,
  headers: {
    "Content-Type": "application/json",
    Authorization: `Bearer ${chatToken}`,
  },
});

export const serializeChatResponse = (response: any): TChatUser => {
  const user = response.data?.entities?.[0];

  return {
    activated: user.activated,
    id: user.username,
    uuid: user.uuid,
    username: user.username,
    createdAt: user.created,
    updatedAt: user.modified,
  };
};

export const createChatUser = async (
  payload: TCreateChatUser
): Promise<TChatUser> => {
  try {
    const response = await agoraChatAxios.post(`/users`, payload);

    return serializeChatResponse(response);
  } catch (err: any) {
    const data = err.response?.data || {};

    switch (data.exception) {
      case "DuplicateUniquePropertyExistsException":
        throw getErrorResponse("User exists.", 400);
      default:
        throw err;
    }
  }
};

export const getChatUser = async (
  username: TCreateChatUser["username"]
): Promise<TChatUser | null> => {
  try {
    const response = await agoraChatAxios.get(`/users/${username}`);

    return serializeChatResponse(response);
  } catch (err: any) {
    const data = err.response.data;

    switch (data.exception) {
      case "UserNotFoundException":
        return null;
      default:
        throw err;
    }
  }
};

export const getAndCreateChatUser = async (
  payload: TCreateChatUser
): Promise<TChatUser> => {
  const user = await getChatUser(payload.username);

  if (user) return user;
  else return await createChatUser(payload);
};

export type TCreateChatGrupPayload = {
  groupname: string;
  desc: string;
  owner: string;
  members: string[];
  public?: boolean;
  maxusers?: number;
};

export const createChatGroup = async (
  payload: TCreateChatGrupPayload
): Promise<TChatGroup> => {
  const response = await agoraChatAxios.post("/chatgroups", {
    public: false,
    maxusers: 300,
    ...payload,
  });

  const data = response.data;

  return {
    id: data.data.groupid,
  };
};
