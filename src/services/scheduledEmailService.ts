import cron from "node-cron";
import { Types } from "mongoose";
import LessonModel from "../models/lesson.model";
import Student from "../models/student";
import SubscriptionModel from "../models/subscription.model";
import { EmailNotificationService } from "./emailNotificationService";
import { logError } from "../utils/logger";

export class ScheduledEmailService {
  // Initialize all scheduled email jobs
  static initializeScheduledJobs(): void {
    console.log("Initializing scheduled email jobs...");

    // Lesson reminders - runs every hour
    this.scheduleLessonReminders();

    // Low lesson balance alerts - runs daily at 9 AM
    this.scheduleLowLessonBalanceAlerts();

    // Subscription expiry reminders - runs daily at 10 AM
    this.scheduleSubscriptionReminders();

    // Inactive student reminders - runs weekly on Mondays at 11 AM
    this.scheduleInactiveStudentReminders();

    console.log("All scheduled email jobs initialized successfully");
  }

  // Send lesson reminders (24h and 1h before)
  private static scheduleLessonReminders(): void {
    // Run every hour to check for upcoming lessons
    cron.schedule("0 * * * *", async () => {
      try {
        console.log("Running lesson reminder check...");

        const now = new Date();
        const in24Hours = new Date(now.getTime() + 24 * 60 * 60 * 1000);
        const in1Hour = new Date(now.getTime() + 60 * 60 * 1000);

        // Find lessons scheduled in 24 hours (±30 minutes window)
        const lessons24h = await LessonModel.find({
          scheduledTime: {
            $gte: new Date(in24Hours.getTime() - 30 * 60 * 1000),
            $lte: new Date(in24Hours.getTime() + 30 * 60 * 1000),
          },
          status: { $in: ["scheduled", "confirmed"] },
        })
          .populate("studentId")
          .populate("tutorId");

        // Find lessons scheduled in 1 hour (±15 minutes window)
        const lessons1h = await LessonModel.find({
          scheduledTime: {
            $gte: new Date(in1Hour.getTime() - 15 * 60 * 1000),
            $lte: new Date(in1Hour.getTime() + 15 * 60 * 1000),
          },
          status: { $in: ["scheduled", "confirmed"] },
        })
          .populate("studentId")
          .populate("tutorId");

        // Send 24-hour reminders
        for (const lesson of lessons24h) {
          const student = lesson.studentId as any;
          const tutor = lesson.tutorId as any;

          if (student && tutor) {
            // Send to student
            await EmailNotificationService.sendStudentLessonReminder(
              student,
              tutor,
              {
                id: (lesson._id as Types.ObjectId).toString(),
                title: lesson.title || "Tutoring Session",
                scheduledTime: lesson.scheduledTime,
                duration: lesson.duration,
                joinLink: `${process.env.FRONTEND_URL}/classroom/${lesson._id}`,
                status: lesson.status,
              }
            );

            // Send to tutor
            await EmailNotificationService.sendTutorLessonReminder(
              tutor,
              student,
              {
                id: (lesson._id as Types.ObjectId).toString(),
                title: lesson.title || "Tutoring Session",
                scheduledTime: lesson.scheduledTime,
                duration: lesson.duration,
                status: lesson.status,
              }
            );
          }
        }

        // Send 1-hour reminders
        for (const lesson of lessons1h) {
          const student = lesson.studentId as any;
          const tutor = lesson.tutorId as any;

          if (student && tutor) {
            // Send to student
            await EmailNotificationService.sendStudentLessonReminder(
              student,
              tutor,
              {
                id: (lesson._id as Types.ObjectId).toString(),
                title: lesson.title || "Tutoring Session",
                scheduledTime: lesson.scheduledTime,
                duration: lesson.duration,
                joinLink: `${process.env.FRONTEND_URL}/classroom/${lesson._id}`,
                status: lesson.status,
              }
            );

            // Send to tutor
            await EmailNotificationService.sendTutorLessonReminder(
              tutor,
              student,
              {
                id: (lesson._id as Types.ObjectId).toString(),
                title: lesson.title || "Tutoring Session",
                scheduledTime: lesson.scheduledTime,
                duration: lesson.duration,
                status: lesson.status,
              }
            );
          }
        }

        console.log(
          `Sent ${lessons24h.length} 24-hour reminders and ${lessons1h.length} 1-hour reminders`
        );
      } catch (error) {
        logError("Error in lesson reminder job", "mail");
      }
    });
  }

  // Send low lesson balance alerts
  private static scheduleLowLessonBalanceAlerts(): void {
    // Run daily at 9 AM
    cron.schedule("0 9 * * *", async () => {
      try {
        console.log("Running low lesson balance check...");

        // Find subscriptions with 1 or fewer remaining lessons
        const lowBalanceSubscriptions = await SubscriptionModel.find({
          status: "active",
          remainingLessons: { $lte: 1 },
        }).populate("studentId");

        for (const subscription of lowBalanceSubscriptions) {
          const student = subscription.studentId as any;

          if (student) {
            await EmailNotificationService.sendLowLessonBalance(
              student,
              subscription.remainingLessons,
              ""
            );
          }
        }

        console.log(
          `Sent ${lowBalanceSubscriptions.length} low lesson balance alerts`
        );
      } catch (error) {
        logError("Error in low lesson balance job", "mail");
      }
    });
  }

  // Send subscription expiry reminders
  private static scheduleSubscriptionReminders(): void {
    // Run daily at 10 AM
    cron.schedule("0 10 * * *", async () => {
      try {
        console.log("Running subscription expiry check...");

        const in7Days = new Date();
        in7Days.setDate(in7Days.getDate() + 7);

        // Find subscriptions expiring in 7 days
        const expiringSubscriptions = await SubscriptionModel.find({
          status: "active",
          endDate: {
            $gte: new Date(),
            $lte: in7Days,
          },
        }).populate("studentId");

        for (const subscription of expiringSubscriptions) {
          const student = subscription.studentId as any;

          if (student) {
            await EmailNotificationService.sendSubscriptionReminder(
              student,
              {
                id: subscription._id.toString(),
                planType: subscription.planType,
                monthlyPrice: subscription.monthlyPrice,
                remainingLessons: subscription.remainingLessons,
                endDate: subscription.endDate || subscription.currentPeriodEnd,
                status: subscription.status,
              },
              ""
            );
          }
        }

        console.log(
          `Sent ${expiringSubscriptions.length} subscription expiry reminders`
        );
      } catch (error) {
        logError("Error in subscription reminder job", "mail");
      }
    });
  }

  // Send inactive student reminders
  private static scheduleInactiveStudentReminders(): void {
    // Run weekly on Mondays at 11 AM
    cron.schedule("0 11 * * 1", async () => {
      try {
        console.log("Running inactive student check...");

        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        // Find students who haven't had a lesson in 30 days
        const recentLessonStudents = await LessonModel.distinct("studentId", {
          scheduledTime: { $gte: thirtyDaysAgo },
        });

        // Find students with active subscriptions but no recent lessons
        const inactiveStudents = await Student.find({
          _id: { $nin: recentLessonStudents },
        });

        // Check if they have active subscriptions
        for (const student of inactiveStudents) {
          const activeSubscription = await SubscriptionModel.findOne({
            studentId: student._id,
            status: "active",
          });

          if (activeSubscription) {
            await EmailNotificationService.sendInactiveStudentReminder(
              student,
              ""
            );
          }
        }

        console.log(
          `Sent ${inactiveStudents.length} inactive student reminders`
        );
      } catch (error) {
        logError("Error in inactive student reminder job", "mail");
      }
    });
  }

  // Manual trigger for testing purposes
  static async triggerLessonReminders(): Promise<void> {
    console.log("Manually triggering lesson reminders...");
    // This would run the same logic as the scheduled job
    // Useful for testing or manual execution
  }

  static async triggerLowBalanceAlerts(): Promise<void> {
    console.log("Manually triggering low balance alerts...");
    // This would run the same logic as the scheduled job
  }

  static async triggerSubscriptionReminders(): Promise<void> {
    console.log("Manually triggering subscription reminders...");
    // This would run the same logic as the scheduled job
  }

  static async triggerInactiveStudentReminders(): Promise<void> {
    console.log("Manually triggering inactive student reminders...");
    // This would run the same logic as the scheduled job
  }
}
