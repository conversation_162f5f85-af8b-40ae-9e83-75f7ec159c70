import { sendMail } from "../utils/mailer";
import {
  lessonConfirmationTemplate,
  studentLessonReminderTemplate,
  studentLessonStatusTemplate,
  paymentSuccessTemplate,
  subscriptionPausedTemplate,
  subscriptionReminderTemplate,
  lowLessonBalanceTemplate,
  inActiveStudentReminderTemplate,
  tutorApprovalStatusTemplate,
  tutorLessonReminder,
  lessonCancelledByStudent,
  payoutConfirmationTemplate,
  lessonBookingTemplate,
  approveTutorTemplate,
} from "../utils/emails-templates/email-templates";
import { logError } from "../utils/logger";
import { IStudent } from "../models/student";
import { ITutor } from "../models/tutor";

// Simplified interfaces for email notifications
export interface EmailStudentData {
  id: string;
  firstname: string;
  lastname: string;
  email: string;
}

export interface EmailTutorData {
  id: string;
  firstname: string;
  lastname: string;
  email: string;
}
import { toCurrency } from "../utils/misc";
import Admin from "../models/admin";

// Types for email notification data
export interface LessonData {
  id: string;
  title?: string;
  scheduledTime: Date;
  duration: number;
  joinLink?: string;
  status: string;
}

// export interface StudentData {
//   id: string;
//   firstname: string;
//   lastname: string;
//   email: string;
// }

// export interface TutorData {
//   id: string;
//   firstname: string;
//   lastname: string;
//   email: string;
// }

export interface SubscriptionData {
  id: string;
  planType: string;
  monthlyPrice: number;
  remainingLessons: number;
  endDate: Date;
  status: string;
}

export interface PaymentData {
  amount: number;
  currency: string;
  transactionId: string;
  receiptUrl?: string;
}

export interface PayoutData {
  amount: number;
  currency: string;
  payoutDate: Date;
  method: string;
  transactionId: string;
}

// Email Notification Service Class
export class EmailNotificationService {
  // 1. Lesson Confirmation Email
  static async sendLessonConfirmation(
    student: EmailStudentData | IStudent,
    tutor: EmailTutorData | ITutor,
    lesson: LessonData
  ): Promise<void> {
    try {
      const template = lessonConfirmationTemplate(
        student,
        tutor,
        lesson.scheduledTime,
        lesson.joinLink!
      );

      await sendMail(student.email, template);
    } catch (error) {
      logError("Failed to send lesson confirmation email", "mail");
      throw error;
    }
  }

  // 2. Student Lesson Reminder Email
  static async sendStudentLessonReminder(
    student: IStudent,
    tutor: ITutor,
    lesson: LessonData
  ): Promise<void> {
    try {
      const template = studentLessonReminderTemplate(
        student,
        tutor,
        lesson.scheduledTime,
        lesson.joinLink!
      );

      await sendMail(student.email, template);
    } catch (error) {
      logError("Failed to send student lesson reminder", "mail");
      throw error;
    }
  }

  // 3. Student Lesson Status Update Email
  static async sendStudentLessonStatus(
    student: EmailStudentData | IStudent,
    tutor: EmailTutorData | ITutor,
    statusType: "rescheduled" | "cancelled",
    newDateTime?: Date
  ): Promise<void> {
    try {
      const template = studentLessonStatusTemplate(
        student,
        tutor,
        statusType,
        newDateTime
      );

      await sendMail(student.email, template);
    } catch (error) {
      logError("Failed to send student lesson status email", "mail");
      throw error;
    }
  }

  // 4. Payment Success Email
  static async sendPaymentSuccess(
    student: EmailStudentData | IStudent,
    payment: PaymentData
  ): Promise<void> {
    try {
      const template = paymentSuccessTemplate(
        student,
        toCurrency(payment),
        payment.receiptUrl!
      );

      const html = template.html
        .replace(/\[First Name\]/g, student.firstname)
        .replace(
          /€\[Amount\]/g,
          `${payment.currency.toUpperCase()} ${(payment.amount / 100).toFixed(
            2
          )}`
        )
        .replace(/\[View Receipt\]/g, payment.receiptUrl || "#");

      await sendMail(student.email, {
        subject: template.subject,
        html,
        from: "no-reply",
      });

      console.log(`Payment success email sent to ${student.email}`);
    } catch (error) {
      logError("Failed to send payment success email", "mail");
      throw error;
    }
  }

  // 5. Subscription Paused/Cancelled Email
  static async sendSubscriptionPaused(
    student: IStudent,
    action: "paused" | "cancelled"
  ): Promise<void> {
    try {
      const template = subscriptionPausedTemplate(student, action);

      await sendMail(student.email, template);
    } catch (error) {
      logError("Failed to send subscription status email", "mail");
      throw error;
    }
  }

  // 6. Subscription Reminder Email
  static async sendSubscriptionReminder(
    student: IStudent,
    subscription: SubscriptionData,
    renewalLink: string
  ): Promise<void> {
    try {
      const template = subscriptionReminderTemplate(
        student,
        subscription.endDate,
        renewalLink
      );

      await sendMail(student.email, template);
    } catch (error) {
      logError("Failed to send subscription reminder email", "mail");
      throw error;
    }
  }

  // 7. Low Lesson Balance Email
  static async sendLowLessonBalance(
    student: IStudent,
    lessonLeft: number,
    buyLessonLink: string
  ): Promise<void> {
    try {
      const template = lowLessonBalanceTemplate(
        student,
        lessonLeft,
        buyLessonLink
      );

      await sendMail(student.email, template);
    } catch (error) {
      logError("Failed to send low lesson balance email", "mail");
      throw error;
    }
  }

  // 8. Inactive Student Email
  static async sendInactiveStudentReminder(
    student: IStudent,
    bookLessonLink: string
  ): Promise<void> {
    try {
      const template = inActiveStudentReminderTemplate(student, bookLessonLink);

      await sendMail(student.email, template);
    } catch (error) {
      logError("Failed to send inactive student reminder email", "mail");
      throw error;
    }
  }

  // 9. Tutor Approval Status Email
  static async sendTutorApprovalStatus(
    tutor: EmailTutorData | ITutor,
    status: "approved" | "rejected",
    feedback?: string,
    onBoardingLink?: string
  ): Promise<void> {
    try {
      const template = tutorApprovalStatusTemplate(
        tutor,
        status,
        feedback,
        onBoardingLink
      );

      await sendMail(tutor.email, template);
    } catch (error) {
      logError("Failed to send tutor approval status email", "mail");
      throw error;
    }
  }

  // 10. Lesson Booking Notification (to Tutor)
  static async sendLessonBookingNotification(
    tutor: EmailTutorData | ITutor,
    student: EmailStudentData | IStudent,
    lesson: LessonData
  ): Promise<void> {
    try {
      const template = lessonBookingTemplate(
        tutor,
        student,
        lesson.scheduledTime,
        lesson.joinLink!
      );

      await sendMail(tutor.email, template);
    } catch (error) {
      logError("Failed to send lesson booking notification", "mail");
      throw error;
    }
  }

  // 11. Tutor Lesson Reminder Email
  static async sendTutorLessonReminder(
    tutor: ITutor,
    student: IStudent,
    lesson: LessonData
  ): Promise<void> {
    try {
      const template = tutorLessonReminder(
        tutor,
        student,
        lesson.scheduledTime
      );

      await sendMail(tutor.email, template);
    } catch (error) {
      logError("Failed to send tutor lesson reminder", "mail");
      throw error;
    }
  }

  // 12. Lesson Cancelled by Student (to Tutor)
  static async sendLessonCancelledByStudent(
    tutor: ITutor,
    student: IStudent,
    action: "cancelled" | "rescheduled",
    newDateTime?: Date
  ): Promise<void> {
    try {
      const template = lessonCancelledByStudent(
        tutor,
        student,
        action,
        newDateTime
      );

      await sendMail(tutor.email, template);
    } catch (error) {
      logError("Failed to send lesson cancellation notification", "mail");
      throw error;
    }
  }

  // 13. Payout Confirmation Email
  static async sendPayoutConfirmation(
    tutor: EmailTutorData | ITutor,
    payout: PayoutData
  ): Promise<void> {
    try {
      const template = payoutConfirmationTemplate(
        tutor,
        toCurrency(payout),
        payout.payoutDate,
        payout.method
      );

      await sendMail(tutor.email, template);
    } catch (error) {
      logError("Failed to send payout confirmation email", "mail");
      throw error;
    }
  }

  // Utility method to send bulk emails
  static async sendBulkEmails(
    recipients: string[],
    emailData: { subject: string; html: string },
    from: "no-reply" | "admin" | "marketing" | "support" = "no-reply"
  ): Promise<void> {
    try {
      const emailPromises = recipients.map((email) =>
        sendMail(email, {
          subject: emailData.subject,
          html: emailData.html,
          from,
        })
      );

      await Promise.all(emailPromises);
    } catch (error) {
      logError("Failed to send bulk emails", "mail");
      throw error;
    }
  }

  // Method to validate email data before sending
  static validateEmailData(data: any): boolean {
    if (!data.email || !data.firstname) {
      console.warn("Invalid email data: missing email or firstname");
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      console.warn(`Invalid email format: ${data.email}`);
      return false;
    }

    return true;
  }

  // Method to format currency
  static formatCurrency(amount: number, currency: string = "USD"): string {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  }

  // Method to format date and time
  static formatDateTime(date: Date, timezone?: string): string {
    return new Intl.DateTimeFormat("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      timeZone: timezone || "UTC",
    }).format(date);
  }
}

export const sendTutorAwaitingApprovalMail = async (
  tutor: ITutor,
  isOnboarding = true
) => {
  try {
    const admin = await Admin.findOne({
      role: "admin",
      isSuperAdmin: true,
    });

    if (admin)
      await sendMail(
        admin.email,
        approveTutorTemplate(admin, tutor, isOnboarding)
      );
  } catch (err: any) {
    logError(
      `Failed to send tutor ${
        isOnboarding ? "onboarding" : "profile update"
      } approval mail`,
      "admin",
      {
        email: tutor.email,
      }
    );
  }
};
