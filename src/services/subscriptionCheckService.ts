import { Types } from 'mongoose';
import Subscription from '../models/subscription.model';
import Student from '../models/student';
import LessonModel from '../models/lesson.model';

export interface SubscriptionCheckResult {
  canBook: boolean;
  reason?: string;
  subscriptionStatus?: string;
  remainingLessons?: number;
  isFreeTrial?: boolean;
  freeTrialUsed?: boolean;
  freeTrialLessonsRemaining?: number;
}

/**
 * Check if a student can book a session with a tutor
 */
export const canStudentBookSession = async (
  studentId: Types.ObjectId,
  tutorId: Types.ObjectId
): Promise<SubscriptionCheckResult> => {
  try {
    // Get student details
    const student = await Student.findById(studentId);
    if (!student) {
      return {
        canBook: false,
        reason: 'Student not found'
      };
    }

    // Check for active subscription with this tutor
    const activeSubscription = await Subscription.findOne({
      studentId,
      tutorId,
      status: 'active'
    });

    if (activeSubscription) {
      // Student has active subscription
      if (activeSubscription.canScheduleLesson()) {
        return {
          canBook: true,
          subscriptionStatus: 'active',
          remainingLessons: activeSubscription.remainingLessons
        };
      } else {
        return {
          canBook: false,
          reason: 'No remaining lessons in current subscription period',
          subscriptionStatus: 'active',
          remainingLessons: activeSubscription.remainingLessons
        };
      }
    }

    // Check if student has used free trial
    if (student.hasUsedFreeTrial) {
      return {
        canBook: false,
        reason: 'Free trial already used. Please subscribe to book sessions.',
        freeTrialUsed: true
      };
    }

    // Check if student is eligible for free trial
    const freeTrialLessonsLimit = 2; // Allow 2 free trial lessons
    const completedFreeTrialLessons = await LessonModel.countDocuments({
      studentId,
      tutorId,
      isFreeTrial: true,
      status: { $in: ['completed', 'confirmed'] }
    });

    const freeTrialLessonsRemaining = freeTrialLessonsLimit - completedFreeTrialLessons;

    if (freeTrialLessonsRemaining > 0) {
      return {
        canBook: true,
        isFreeTrial: true,
        freeTrialLessonsRemaining,
        reason: `Free trial: ${freeTrialLessonsRemaining} lesson(s) remaining`
      };
    } else {
      // Mark student as having used free trial
      await Student.findByIdAndUpdate(studentId, { hasUsedFreeTrial: true });
      
      return {
        canBook: false,
        reason: 'Free trial lessons exhausted. Please subscribe to continue booking sessions.',
        freeTrialUsed: true,
        freeTrialLessonsRemaining: 0
      };
    }

  } catch (error: any) {
    console.error('Error checking subscription status:', error);
    return {
      canBook: false,
      reason: 'Error checking subscription status'
    };
  }
};

/**
 * Get student's subscription summary
 */
export const getStudentSubscriptionSummary = async (studentId: Types.ObjectId) => {
  try {
    const student = await Student.findById(studentId);
    if (!student) {
      throw new Error('Student not found');
    }

    // Get all active subscriptions
    const activeSubscriptions = await Subscription.find({
      studentId,
      status: 'active'
    }).populate('tutorId', 'firstname lastname teachingSubjects basePrice');

    // Get free trial status
    const freeTrialLessonsLimit = 2;
    const completedFreeTrialLessons = await LessonModel.countDocuments({
      studentId,
      isFreeTrial: true,
      status: { $in: ['completed', 'confirmed'] }
    });

    const freeTrialLessonsRemaining = Math.max(0, freeTrialLessonsLimit - completedFreeTrialLessons);

    return {
      student: {
        id: student._id,
        name: `${student.firstname} ${student.lastname}`,
        email: student.email,
        hasUsedFreeTrial: student.hasUsedFreeTrial
      },
      activeSubscriptions: activeSubscriptions.map(sub => ({
        id: sub._id,
        tutor: sub.tutorId,
        planType: sub.planType,
        lessonsPerWeek: sub.lessonsPerWeek,
        remainingLessons: sub.remainingLessons,
        currentPeriodEnd: sub.currentPeriodEnd,
        canScheduleLesson: sub.canScheduleLesson()
      })),
      freeTrial: {
        available: !student.hasUsedFreeTrial && freeTrialLessonsRemaining > 0,
        lessonsRemaining: freeTrialLessonsRemaining,
        lessonsLimit: freeTrialLessonsLimit,
        used: student.hasUsedFreeTrial
      },
      totalActiveSubscriptions: activeSubscriptions.length
    };
  } catch (error: any) {
    console.error('Error getting subscription summary:', error);
    throw error;
  }
};

/**
 * Check if student can book with any tutor (for general booking validation)
 */
export const canStudentBookWithAnyTutor = async (studentId: Types.ObjectId): Promise<boolean> => {
  try {
    const student = await Student.findById(studentId);
    if (!student) {
      return false;
    }

    // Check for any active subscription
    const hasActiveSubscription = await Subscription.exists({
      studentId,
      status: 'active',
      remainingLessons: { $gt: 0 }
    });

    if (hasActiveSubscription) {
      return true;
    }

    // Check free trial eligibility
    if (!student.hasUsedFreeTrial) {
      const freeTrialLessonsLimit = 2;
      const completedFreeTrialLessons = await LessonModel.countDocuments({
        studentId,
        isFreeTrial: true,
        status: { $in: ['completed', 'confirmed'] }
      });

      return completedFreeTrialLessons < freeTrialLessonsLimit;
    }

    return false;
  } catch (error: any) {
    console.error('Error checking general booking eligibility:', error);
    return false;
  }
};

/**
 * Validate and consume a lesson from subscription or free trial
 */
export const consumeLesson = async (
  studentId: Types.ObjectId,
  tutorId: Types.ObjectId,
  isFreeTrial: boolean = false
): Promise<{ success: boolean; message: string }> => {
  try {
    if (isFreeTrial) {
      // For free trial, just verify eligibility (actual consumption happens when lesson is completed)
      const checkResult = await canStudentBookSession(studentId, tutorId);
      if (!checkResult.canBook || !checkResult.isFreeTrial) {
        return {
          success: false,
          message: checkResult.reason || 'Not eligible for free trial'
        };
      }
      return {
        success: true,
        message: 'Free trial lesson booked successfully'
      };
    } else {
      // For paid subscription, consume a lesson
      const subscription = await Subscription.findOne({
        studentId,
        tutorId,
        status: 'active',
        remainingLessons: { $gt: 0 }
      });

      if (!subscription) {
        return {
          success: false,
          message: 'No active subscription with remaining lessons found'
        };
      }

      // Decrease remaining lessons
      subscription.remainingLessons -= 1;
      await subscription.save();

      return {
        success: true,
        message: `Lesson consumed. ${subscription.remainingLessons} lessons remaining.`
      };
    }
  } catch (error: any) {
    console.error('Error consuming lesson:', error);
    return {
      success: false,
      message: 'Error processing lesson consumption'
    };
  }
};
