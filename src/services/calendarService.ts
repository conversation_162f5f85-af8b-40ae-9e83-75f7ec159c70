import { Calendar } from '../models/calender';
import { Types } from 'mongoose';

export interface CreateCalendarOptions {
  ownerUserId: Types.ObjectId;
  ownerType: 'Tutor' | 'Student';
  name?: string;
  description?: string;
  color?: string;
  isShared?: boolean;
}

/**
 * Creates a default calendar for a user (tutor or student)
 */
export const createDefaultCalendar = async (options: CreateCalendarOptions) => {
  const {
    ownerUserId,
    ownerType,
    name,
    description,
    color,
    isShared
  } = options;

  // Set default values based on user type
  const defaultName = name || `${ownerType === 'Tutor' ? 'Teaching' : 'Learning'} Schedule`;
  const defaultDescription = description || `Default calendar for ${ownerType.toLowerCase()}`;
  const defaultColor = color || (ownerType === 'Tutor' ? '#4CAF50' : '#2196F3'); // Green for tutors, blue for students
  const defaultIsShared = isShared !== undefined ? isShared : (ownerType === 'Tutor'); // Tutors share by default, students don't

  try {
    // Check if user already has a default calendar
    const existingCalendar = await Calendar.findOne({
      ownerUserId,
      ownerType,
      name: defaultName
    });

    if (existingCalendar) {
      return existingCalendar;
    }

    // Create new calendar
    const calendar = new Calendar({
      ownerUserId,
      ownerType,
      name: defaultName,
      description: defaultDescription,
      color: defaultColor,
      isShared: defaultIsShared
    });

    await calendar.save();
    return calendar;
  } catch (error) {
    console.error('Error creating default calendar:', error);
    throw new Error('Failed to create default calendar');
  }
};

/**
 * Creates multiple calendars for a tutor (e.g., different subjects or purposes)
 */
export const createTutorCalendars = async (tutorId: Types.ObjectId, subjects?: string[]) => {
  const calendars = [];

  // Create main teaching calendar
  const mainCalendar = await createDefaultCalendar({
    ownerUserId: tutorId,
    ownerType: 'Tutor',
    name: 'Teaching Schedule',
    description: 'Main teaching calendar',
    color: '#4CAF50',
    isShared: true
  });
  calendars.push(mainCalendar);

  // Create subject-specific calendars if subjects are provided
  if (subjects && subjects.length > 0) {
    const colors = ['#FF9800', '#9C27B0', '#F44336', '#00BCD4', '#FFEB3B'];
    
    for (let i = 0; i < subjects.length; i++) {
      const subject = subjects[i];
      const subjectCalendar = await createDefaultCalendar({
        ownerUserId: tutorId,
        ownerType: 'Tutor',
        name: `${subject} Classes`,
        description: `Calendar for ${subject} teaching sessions`,
        color: colors[i % colors.length],
        isShared: true
      });
      calendars.push(subjectCalendar);
    }
  }

  // Create personal calendar (not shared)
  const personalCalendar = await createDefaultCalendar({
    ownerUserId: tutorId,
    ownerType: 'Tutor',
    name: 'Personal Schedule',
    description: 'Personal appointments and events',
    color: '#607D8B',
    isShared: false
  });
  calendars.push(personalCalendar);

  return calendars;
};

/**
 * Creates calendars for a student
 */
export const createStudentCalendars = async (studentId: Types.ObjectId) => {
  const calendars = [];

  // Create main learning calendar
  const mainCalendar = await createDefaultCalendar({
    ownerUserId: studentId,
    ownerType: 'Student',
    name: 'Learning Schedule',
    description: 'Main learning and lesson calendar',
    color: '#2196F3',
    isShared: false
  });
  calendars.push(mainCalendar);

  // Create personal calendar
  const personalCalendar = await createDefaultCalendar({
    ownerUserId: studentId,
    ownerType: 'Student',
    name: 'Personal Schedule',
    description: 'Personal appointments and events',
    color: '#607D8B',
    isShared: false
  });
  calendars.push(personalCalendar);

  return calendars;
};

/**
 * Get all calendars for a user
 */
export const getUserCalendars = async (userId: Types.ObjectId, userType: 'Tutor' | 'Student') => {
  try {
    const calendars = await Calendar.find({
      ownerUserId: userId,
      ownerType: userType
    }).sort({ createdAt: 1 });

    return calendars;
  } catch (error) {
    console.error('Error fetching user calendars:', error);
    throw new Error('Failed to fetch user calendars');
  }
};

/**
 * Get shared calendars (for students to view tutor calendars)
 */
export const getSharedCalendars = async (tutorId?: Types.ObjectId) => {
  try {
    const query: any = { isShared: true, ownerType: 'Tutor' };
    if (tutorId) {
      query.ownerUserId = tutorId;
    }

    const calendars = await Calendar.find(query)
      .populate('ownerUserId', 'firstname lastname email')
      .sort({ createdAt: 1 });

    return calendars;
  } catch (error) {
    console.error('Error fetching shared calendars:', error);
    throw new Error('Failed to fetch shared calendars');
  }
};
