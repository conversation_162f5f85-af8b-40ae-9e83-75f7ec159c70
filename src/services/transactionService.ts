import { Types } from 'mongoose';
import TransactionModel, { Transaction } from '../models/transaction.model';
import Subscription from '../models/subscription.model';
import LessonModel from '../models/lesson.model';
import Tu<PERSON> from '../models/tutor';
import Student from '../models/student';

export interface CreateTransactionData {
  userId: Types.ObjectId;
  relatedUserId?: Types.ObjectId;
  amount: number; // in cents
  currency?: string;
  type: string;
  category?: string;
  status?: string;
  paymentMethod?: string;
  subscriptionId?: Types.ObjectId;
  lessonId?: Types.ObjectId;
  withdrawalRequestId?: Types.ObjectId;
  refundRequestId?: Types.ObjectId;
  stripeTransactionId?: string;
  stripePaymentIntentId?: string;
  stripeInvoiceId?: string;
  stripeRefundId?: string;
  paypalTransactionId?: string;
  description?: string;
  adminNotes?: string;
  metadata?: Record<string, any>;
  platformFeeAmount?: number;
  processingFeeAmount?: number;
}

export class TransactionService {
  /**
   * Create a new transaction record
   */
  static async createTransaction(data: CreateTransactionData): Promise<Transaction> {
    try {
      const transaction = new TransactionModel(data);
      await transaction.save();
      
      // Populate related data for response
      await transaction.populate([
        { path: 'userId', select: 'firstname lastname email role' },
        { path: 'relatedUserId', select: 'firstname lastname email role' },
        { path: 'subscription', select: 'planType monthlyPrice status' },
        { path: 'lesson', select: 'title scheduledAt status' }
      ]);
      
      return transaction;
    } catch (error) {
      console.error('Error creating transaction:', error);
      throw new Error('Failed to create transaction');
    }
  }

  /**
   * Record subscription payment
   */
  static async recordSubscriptionPayment(
    subscriptionId: Types.ObjectId,
    studentId: Types.ObjectId,
    tutorId: Types.ObjectId,
    amount: number,
    stripeData: {
      paymentIntentId?: string;
      invoiceId?: string;
      transactionId?: string;
    }
  ): Promise<Transaction> {
    const subscription = await Subscription.findById(subscriptionId);
    if (!subscription) {
      throw new Error('Subscription not found');
    }

    // Calculate platform fee (20%)
    const platformFeeAmount = Math.round(amount * 0.20);
    const tutorEarnings = amount - platformFeeAmount;

    // Create payment transaction for student
    const paymentTransaction = await this.createTransaction({
      userId: studentId,
      relatedUserId: tutorId,
      amount,
      type: 'subscription_payment',
      category: 'payment',
      status: 'completed',
      paymentMethod: 'stripe',
      subscriptionId,
      stripePaymentIntentId: stripeData.paymentIntentId,
      stripeInvoiceId: stripeData.invoiceId,
      stripeTransactionId: stripeData.transactionId,
      description: `Subscription payment for ${subscription.planType}`,
      platformFeeAmount,
      processingFeeAmount: 0
    });

    // Create platform fee transaction
    await this.createTransaction({
      userId: studentId,
      relatedUserId: tutorId,
      amount: platformFeeAmount,
      type: 'platform_fee',
      category: 'fee',
      status: 'completed',
      subscriptionId,
      description: `Platform fee (20%) for subscription ${subscription.planType}`,
      metadata: { 
        originalTransactionId: paymentTransaction._id,
        feePercentage: 20
      }
    });

    // Update tutor's available balance
    await Tutor.findByIdAndUpdate(tutorId, {
      $inc: { availableBalance: tutorEarnings }
    });

    return paymentTransaction;
  }

  /**
   * Record lesson payment and payout
   */
  static async recordLessonPayment(
    lessonId: Types.ObjectId,
    studentId: Types.ObjectId,
    tutorId: Types.ObjectId,
    amount: number,
    isFreeTrial: boolean = false
  ): Promise<{ paymentTransaction?: Transaction; payoutTransaction: Transaction }> {
    const lesson = await LessonModel.findById(lessonId);
    if (!lesson) {
      throw new Error('Lesson not found');
    }

    let paymentTransaction: Transaction | undefined;
    
    if (!isFreeTrial) {
      // Create payment transaction for paid lessons
      paymentTransaction = await this.createTransaction({
        userId: studentId,
        relatedUserId: tutorId,
        amount,
        type: 'lesson_payment',
        category: 'payment',
        status: 'completed',
        lessonId,
        description: `Payment for lesson: ${lesson.title}`,
        platformFeeAmount: Math.round(amount * 0.20),
        processingFeeAmount: 0
      });
    }

    // Calculate tutor earnings (80% for paid lessons, 0 for free trials)
    const tutorEarnings = isFreeTrial ? 0 : Math.round(amount * 0.80);

    // Create payout transaction for tutor
    const payoutTransaction = await this.createTransaction({
      userId: tutorId,
      relatedUserId: studentId,
      amount: tutorEarnings,
      type: 'lesson_payout',
      category: 'payout',
      status: 'completed',
      lessonId,
      description: `${isFreeTrial ? 'Free trial' : 'Paid'} lesson payout: ${lesson.title}`,
      metadata: {
        isFreeTrial,
        originalAmount: amount,
        platformFeeAmount: isFreeTrial ? 0 : Math.round(amount * 0.20)
      }
    });

    // Update tutor's available balance
    if (tutorEarnings > 0) {
      await Tutor.findByIdAndUpdate(tutorId, {
        $inc: { availableBalance: tutorEarnings }
      });
    }

    return { paymentTransaction, payoutTransaction };
  }

  /**
   * Record refund transaction
   */
  static async recordRefund(
    originalTransactionId: Types.ObjectId,
    refundAmount: number,
    reason: string,
    stripeRefundId?: string,
    refundRequestId?: Types.ObjectId
  ): Promise<Transaction> {
    const originalTransaction = await TransactionModel.findById(originalTransactionId);
    if (!originalTransaction) {
      throw new Error('Original transaction not found');
    }

    const refundTransaction = await this.createTransaction({
      userId: originalTransaction.userId,
      relatedUserId: originalTransaction.relatedUserId,
      amount: refundAmount,
      type: 'refund',
      category: 'refund',
      status: 'completed',
      paymentMethod: originalTransaction.paymentMethod,
      subscriptionId: originalTransaction.subscriptionId,
      lessonId: originalTransaction.lessonId,
      refundRequestId,
      stripeRefundId,
      description: `Refund: ${reason}`,
      metadata: {
        originalTransactionId: originalTransactionId,
        refundReason: reason
      }
    });

    // If this was a lesson payout, reduce tutor's balance
    if (originalTransaction.type === 'lesson_payout' && originalTransaction.relatedUserId) {
      await Tutor.findByIdAndUpdate(originalTransaction.relatedUserId, {
        $inc: { availableBalance: -refundAmount }
      });
    }

    return refundTransaction;
  }

  /**
   * Record withdrawal transaction
   */
  static async recordWithdrawal(
    tutorId: Types.ObjectId,
    amount: number,
    withdrawalRequestId: Types.ObjectId,
    stripeTransferId?: string
  ): Promise<Transaction> {
    const transaction = await this.createTransaction({
      userId: tutorId,
      amount,
      type: 'withdrawal',
      category: 'payout',
      status: 'completed',
      paymentMethod: 'stripe',
      withdrawalRequestId,
      stripeTransactionId: stripeTransferId,
      description: `Withdrawal payout - Request #${withdrawalRequestId}`
    });

    // Deduct from tutor's available balance
    await Tutor.findByIdAndUpdate(tutorId, {
      $inc: { availableBalance: -amount }
    });

    return transaction;
  }

  /**
   * Get transaction history for a user
   */
  static async getUserTransactions(
    userId: Types.ObjectId,
    options: {
      page?: number;
      limit?: number;
      type?: string;
      category?: string;
      status?: string;
      startDate?: Date;
      endDate?: Date;
    } = {}
  ): Promise<{
    transactions: Transaction[];
    total: number;
    summary: any;
  }> {
    const {
      page = 1,
      limit = 20,
      type,
      category,
      status,
      startDate,
      endDate
    } = options;

    const query: any = { userId };
    if (type) query.type = type;
    if (category) query.category = category;
    if (status) query.status = status;
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = startDate;
      if (endDate) query.createdAt.$lte = endDate;
    }

    const [transactions, total, summary] = await Promise.all([
      TransactionModel.find(query)
        .populate('relatedUserId', 'firstname lastname email role')
        .populate('subscription', 'planType monthlyPrice status')
        .populate('lesson', 'title scheduledAt status')
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit),
      
      TransactionModel.countDocuments(query),
      
      TransactionModel.aggregate([
        { $match: query },
        {
          $group: {
            _id: '$category',
            totalAmount: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    return {
      transactions,
      total,
      summary: summary.reduce((acc, item) => {
        acc[item._id] = {
          totalAmount: item.totalAmount / 100,
          count: item.count
        };
        return acc;
      }, {})
    };
  }

  /**
   * Get financial analytics
   */
  static async getFinancialAnalytics(startDate?: Date, endDate?: Date) {
    const matchStage: any = {};
    if (startDate || endDate) {
      matchStage.createdAt = {};
      if (startDate) matchStage.createdAt.$gte = startDate;
      if (endDate) matchStage.createdAt.$lte = endDate;
    }

    const [revenueStats, categoryBreakdown, dailyTrends] = await Promise.all([
      // Revenue statistics
      TransactionModel.aggregate([
        { 
          $match: { 
            ...matchStage, 
            status: 'completed',
            category: { $in: ['payment', 'fee'] }
          } 
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$amount' },
            totalTransactions: { $sum: 1 },
            avgTransactionAmount: { $avg: '$amount' },
            totalPlatformFees: { $sum: '$platformFeeAmount' }
          }
        }
      ]),

      // Category breakdown
      TransactionModel.aggregate([
        { $match: { ...matchStage, status: 'completed' } },
        {
          $group: {
            _id: '$category',
            totalAmount: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ]),

      // Daily trends
      TransactionModel.aggregate([
        { $match: { ...matchStage, status: 'completed' } },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
              category: '$category'
            },
            totalAmount: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.date': -1 } },
        { $limit: 30 }
      ])
    ]);

    return {
      revenue: revenueStats[0] || {
        totalRevenue: 0,
        totalTransactions: 0,
        avgTransactionAmount: 0,
        totalPlatformFees: 0
      },
      categoryBreakdown,
      dailyTrends
    };
  }
}
