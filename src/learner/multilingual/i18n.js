const i18next = require('i18next');
const Backend = require('i18next-fs-backend');
const middleware = require('i18next-http-middleware');
const path = require('path');

i18next
  .use(Backend)
  .use(middleware.LanguageDetector)
  .init({
    fallbackLng: 'en',
    preload: ['en', 'fr', 'es'],           
    backend: {
      loadPath: path.join(__dirname, 'locales/{{lng}}/{{ns}}.json')
    },
    ns: ['common', 'dashboard'],  
    defaultNS: 'common',
    detection: {
      order: ['header', 'querystring', 'cookie'],
      caches: ['cookie']
    },
    debug: false
  });

module.exports = i18next;
