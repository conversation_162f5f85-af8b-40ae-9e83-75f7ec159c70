"use client";

import * as React from "react";
import * as AvatarPrimitive from "@radix-ui/react-avatar";
import { cn } from "@/utils";
import { AVATAR_SIZE } from "@/constants";

const COLORS = [
  {
    bg: "linear-gradient(135deg, #FF6B6B, #FFD93D)", // red to yellow
    text: "#2E2E2E", // dark for contrast
  },
  {
    bg: "linear-gradient(135deg, #6A11CB, #2575FC)", // violet to blue
    text: "#FFFFFF",
  },
  {
    bg: "linear-gradient(135deg, #11998e, #38ef7d)", // green to lime
    text: "#FFFFFF",
  },
  {
    bg: "linear-gradient(135deg, #FC5C7D, #6A82FB)", // pink to purple
    text: "#FFFFFF",
  },
  {
    bg: "linear-gradient(135deg, #F7971E, #FFD200)", // orange to yellow
    text: "#2E2E2E", // darker text for light bg
  },
  {
    bg: "linear-gradient(135deg, #00F260, #0575E6)", // green to blue
    text: "#FFFFFF",
  },
  {
    bg: "linear-gradient(135deg, #fc4a1a, #f7b733)", // sunset orange to peach
    text: "#2E2E2E",
  },
  {
    bg: "linear-gradient(135deg, #43cea2, #185a9d)", // sea green to ocean blue
    text: "#FFFFFF",
  },
  {
    bg: "linear-gradient(135deg, #DA4453, #89216B)", // raspberry to plum
    text: "#FFFFFF",
  },
  {
    bg: "linear-gradient(135deg, #4AC29A, #BDFFF3)", // mint to ice
    text: "#2E2E2E",
  },
  {
    bg: "linear-gradient(135deg, #FF4E50, #F9D423)", // coral to sunshine
    text: "#2E2E2E",
  },
];

const getInitials = (fullName) => {
  if (!fullName || typeof fullName !== "string") return "";
  const [first = "", last = ""] = fullName.trim().split(" ");
  return `${first[0] || ""}${last[0] || ""}`.toUpperCase();
};

const getColorFromName = (name) => {
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  const index = Math.abs(hash) % COLORS.length;
  return COLORS[index] || COLORS[0];
};

const RootAvatar = React.forwardRef(({ className, ...props }, ref) => (
  <AvatarPrimitive.Root
    ref={ref}
    className={cn(
      "relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",
      className
    )}
    {...props}
  />
));

RootAvatar.displayName = AvatarPrimitive.Root.displayName;

const AvatarImage = React.forwardRef(({ className, ...props }, ref) => (
  <AvatarPrimitive.Image
    ref={ref}
    className={cn("aspect-square h-full w-full", className)}
    {...props}
  />
));
AvatarImage.displayName = AvatarPrimitive.Image.displayName;

const AvatarFallback = React.forwardRef(({ className, ...props }, ref) => (
  <AvatarPrimitive.Fallback
    ref={ref}
    className={cn(
      "flex h-full w-full items-center justify-center rounded-full",
      className
    )}
    {...props}
  />
));
AvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;

const Avatar = React.forwardRef(({ username = "", size = "md", src }, ref) => {
  const { bg, text } = getColorFromName(username);

  return (
    <RootAvatar className={cn(AVATAR_SIZE[size])}>
      <AvatarImage ref={ref} alt="" src={src} />
      <AvatarFallback
        className={cn("text-[14px]")}
        style={{
          background: bg,
          color: text,
        }}
      >
        {getInitials(username)}
      </AvatarFallback>
    </RootAvatar>
  );
});

export { Avatar, AvatarImage, AvatarFallback };
