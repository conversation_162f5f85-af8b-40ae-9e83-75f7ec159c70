import React from "react";

const TableComponent = ({ columns, headerActions, data, title }) => {
  const getValueFromAccessor = (row, accessor) => {
    return accessor.split(".").reduce((obj, key) => obj?.[key], row);
  };

  return (
    <div className="w-full overflow-auto">
      <div className="flex justify-between items-center mb-4"></div>

      <div className="flex flex-col lg:flex-row gap-4">
        <table className="w-full border-collapse flex-1">
          <thead>
            <th
              colSpan={columns.length}
              className="py-3 text-left text-sm font-medium text-gray-700"
            >
              <div className="sm:flex flex-row items-center justify-between gap-4 w-full">
                <h2 className="text-xl font-semibold text-gray-900 shrink-0">
                  {title}
                </h2>

                <div className="flex items-center gap-4 w-full">
                  {/* <input
                    type="text"
                    placeholder="Search..."
                    className="border border-gray-300 rounded-md ml-auto py-2 px-4 pr-10 focus:outline-none w-full max-w-[150px]"
                  /> */}
                  <div className="shrink-0 ml-auto">{headerActions}</div>
                </div>
              </div>
            </th>

            <tr className="bg-gray-100">
              {columns.map(({ header, accessor }, index) => (
                <th
                  key={index}
                  className="py-3 px-4 text-left text-sm font-medium text-gray-700"
                  style={{
                    width: `${100 / columns.length}%`,

                    whiteSpace: "nowrap"
                  }}
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>

          <tbody>
            {data.length > 0 ? (
              data.map((row, rowIndex) => (
                <tr key={rowIndex} className="border-t border-gray-200">
                  {columns.map(({ accessor, render }, colIndex) => (
                    <td
                      key={colIndex}
                      className="py-3 px-4 text-sm text-gray-900"
                      style={{
                        width: `${100 / columns.length}%`,
                        whiteSpace: "nowrap"
                        // maxWidth: "150px",
                        // overflow: "hidden",
                        // textOverflow: "ellipsis"
                      }}
                    >
                      {render
                        ? render(getValueFromAccessor(row, accessor), row)
                        : getValueFromAccessor(row, accessor) || "N/A"}
                    </td>
                  ))}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={columns.length}
                  className="text-center py-6 text-sm text-gray-500"
                >
                  No data found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TableComponent;
