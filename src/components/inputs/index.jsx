import React from "react";
import TextInput from "./textInput";
import PasswordInput from "./passwordInput";
import NumberInput from "./numberInput";
import CheckboxInput from "./checkboxInput";

const InputField = ({
  label,
  register,
  fieldName,
  isRequired,
  placeHolder,
  error,
  fieldType = "text",
  autoComplete,
  defaultValue,
  disabled = false,
  registerOptions,
  validate = false,
}) => {
  const defaultRegisterOptions = validate
    ? {
        required: "This field is required",
        pattern:
          fieldType === "password"
            ? {
                value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*[^A-Za-z0-9]).{8,}$/,
                message:
                  "Password must be at least 8 characters, include uppercase, lowercase, and a special character"
              }
            : undefined
      }
    : {};

  const sharedProps = {
    register,
    fieldName,
    placeHolder,
    defaultValue,
    autoComplete,
    disabled,
    isRequired,
    validate,
    registerOptions: registerOptions || defaultRegisterOptions
  };

  const renderInput = () => {
    switch (fieldType) {
      case "password":
        return <PasswordInput {...sharedProps}  />;
      case "number":
        return <NumberInput {...sharedProps}  />;
      case "checkbox":
        return <CheckboxInput {...sharedProps}  />;

      default:
        return <TextInput {...sharedProps}  />;
    }
  };


  return (
    <div className={`${fieldType !== "checkbox" ? "mb-7 w-full" : ""}`}>
      {label && (
        <p className="mb-3 satoshi text-[#1A1A40] text-sm sm:text-base">
          {label} &nbsp;
          {isRequired ? (
            // <span style={{ color: "red" }}>*</span>
            <span></span>
          ) : (
            <span>(Optional)</span>
          )}
        </p>
      )}
      {renderInput()}
      {error && <div className="text-red-600 text-sm italic">{error}</div>}
    </div>
  );
};

export default InputField;
