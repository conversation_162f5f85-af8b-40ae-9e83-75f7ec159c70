import { cn } from "@/utils";
import React, { forwardRef } from "react";

export const Textarea = forwardRef(
  ({ onEnter, onKeyDown, className = "", ...props }, ref) => {
    return (
      <textarea
        ref={ref}
        {...props}
        className={cn("resize-none", className)}
        onKeyDown={(e) => {
          if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            onEnter?.(e);
          }

          onKeyDown?.(e);
        }}
      />
    );
  }
);

const TextInput = ({
  register,
  fieldName,
  placeHolder,
  defaultValue,
  autoComplete,
  disabled,
  registerOptions,
}) => {
  return (
    <input
      className={`text-sm sm:text-base border border-[#E8E8E8] bg-white p-3 px-4 rounded-lg w-full focus:outline-none ${
        disabled ? "cursor-not-allowed" : ""
      }`}
      type="text"
      placeholder={placeHolder}
      defaultValue={defaultValue}
      {...register(fieldName, registerOptions)}
      autoComplete={autoComplete}
      disabled={disabled}
    />
  );
};

export default TextInput;
