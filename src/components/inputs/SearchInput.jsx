import SearchIcon from "@/assets/svgs/SearchIcon";
import { useDebounce } from "@/hooks/useDebounce";
import React, { useState } from "react";

const SearchInput = ({ onSearch }) => {
  const [search, setSearch] = useState("");

  useDebounce(search, 500, onSearch);

  return (
    <div
      className="
  border border-[#EBEDF0] rounded-[8px]
  flex items-center bg-white p-[10px]
  gap-2
  "
    >
      <SearchIcon />

      <input
        value={search}
        placeholder="Search"
        className="
      !border-none !outline-none font-medium 
      text-[18px] font-fig-tree 
      text-[#A4A4A4] !bg-transparent
      "
        onChange={(e) => setSearch(e.target.value)}
      />
    </div>
  );
};

export default SearchInput;
