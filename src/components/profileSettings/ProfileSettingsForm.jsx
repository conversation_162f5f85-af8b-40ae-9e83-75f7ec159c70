import React, { useState, useRef } from "react";
import { useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import { Trash2, Upload, User } from "lucide-react";

// API hooks
import { useUpdateStudentProfileSettingsMutation } from "@/redux/slices/student/studentSettingsApiSlice";
import { useUpdateTutorProfileSettingsMutation } from "@/redux/slices/tutor/tutorSettingsApiSlice";
import { useUploadProfileImageMutation, useGetProfileImageQuery, useDeleteProfileImageMutation } from "@/redux/slices/student/profileImageApiSlice";

// Components
import { Button } from "@/components/button/button";
import Loader from "@/components/loader/loader";
import PhoneInputWithCountry from "@/components/inputs/phoneInputWithCountry";
import { CustomSelect } from "@/components/select/select";

// Utils
import { convertToBase64 } from "@/utils/utils";
import { setUserInfo } from "@/redux/appSlice";

// Default avatar
import userImage from "@/assets/images/tutor2.png";

const ProfileSettingsForm = () => {
  const dispatch = useDispatch();
  const user = useSelector((state) => state?.app?.userInfo?.user);
  const userInfo = useSelector((state) => state?.app?.userInfo);
  const fileInputRef = useRef(null);

  // Local state
  const [previewImage, setPreviewImage] = useState(null);

  // API hooks
  const [updateStudentProfile, { isLoading: isUpdatingStudent }] = useUpdateStudentProfileSettingsMutation();
  const [updateTutorProfile, { isLoading: isUpdatingTutor }] = useUpdateTutorProfileSettingsMutation();
  const [uploadProfileImage, { isLoading: isUploading }] = useUploadProfileImageMutation();
  const { data: profileImageData, refetch: refetchProfileImage } = useGetProfileImageQuery();
  const [deleteProfileImage, { isLoading: isDeleting }] = useDeleteProfileImageMutation();

  // Form handling
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      firstname: user?.firstname || "",
      lastname: user?.lastname || "",
      phone: user?.phone || "",
      timeZone: user?.timeZone || "",
      countryOfBirth: user?.countryOfBirth || "",
    },
  });

  const isStudent = user?.role === "student";
  const isTutor = user?.role === "tutor";
  const isUpdating = isUpdatingStudent || isUpdatingTutor;

  // Handle profile form submission
  const onSubmit = async (formData) => {
    try {
      let response;
      
      if (isStudent) {
        response = await updateStudentProfile({
          studentId: user?.id,
          ...formData,
        }).unwrap();
      } else if (isTutor) {
        response = await updateTutorProfile({
          tutorId: user?.id,
          ...formData,
        }).unwrap();
      }

      if (response?.success) {
        // Update Redux store with new user data
        dispatch(setUserInfo({
          ...userInfo,
          user: {
            ...user,
            ...response.data,
          }
        }));
        
        toast.success("Profile updated successfully!");
        
        // Refetch profile image to ensure it's up to date
        refetchProfileImage();
      }
    } catch (error) {
      console.error("Profile update error:", error);
      toast.error(error?.data?.message || "Failed to update profile");
    }
  };

  // Handle image file selection
  const handleImageChange = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      toast.error('Please select a valid image file (JPEG, PNG, or WebP)');
      return;
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      toast.error('Image size must be less than 5MB');
      return;
    }

    try {
      // Convert to base64
      const base64Image = await convertToBase64(file);
      
      // Set preview
      setPreviewImage(base64Image);

      // Upload immediately
      const response = await uploadProfileImage({
        imageBase64: base64Image
      }).unwrap();

      if (response.success) {
        // Update Redux store with new image URL
        dispatch(setUserInfo({
          ...userInfo,
          user: {
            ...user,
            image: response.data.imageUrl,
          }
        }));
        
        toast.success('Profile image uploaded successfully!');
        refetchProfileImage();
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error(error?.data?.message || 'Failed to upload image');
      setPreviewImage(null);
    }

    // Clear the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle image deletion
  const handleDeleteImage = async () => {
    if (!window.confirm('Are you sure you want to delete your profile image?')) {
      return;
    }

    try {
      const response = await deleteProfileImage().unwrap();
      
      if (response.success) {
        // Update Redux store to remove image
        dispatch(setUserInfo({
          ...userInfo,
          user: {
            ...user,
            image: null,
          }
        }));
        
        setPreviewImage(null);
        toast.success('Profile image deleted successfully!');
        refetchProfileImage();
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      toast.error(error?.data?.message || 'Failed to delete image');
    }
  };

  // Get current image URL
  const getCurrentImageUrl = () => {
    if (previewImage) return previewImage;
    if (user?.image) return user.image;
    if (profileImageData?.data?.imageUrl) return profileImageData.data.imageUrl;
    return userImage;
  };

  const hasProfileImage = user?.image || profileImageData?.data?.imageUrl;

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-sm">
      <h2 className="text-2xl font-bold mb-6">Profile Settings</h2>

      {/* Profile Image Section */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Profile Picture</h3>
        <div className="flex items-center space-x-6">
          <div className="relative">
            <img
              src={getCurrentImageUrl()}
              alt="Profile"
              className="w-24 h-24 rounded-full object-cover border-4 border-gray-200"
            />
            {(isUploading || isDeleting) && (
              <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                <Loader size="sm" />
              </div>
            )}
          </div>
          
          <div className="flex flex-col space-y-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading || isDeleting}
              className="flex items-center space-x-2"
            >
              <Upload size={16} />
              <span>Upload New Photo</span>
            </Button>
            
            {hasProfileImage && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleDeleteImage}
                disabled={isUploading || isDeleting}
                className="flex items-center space-x-2 text-red-600 hover:text-red-700"
              >
                <Trash2 size={16} />
                <span>Remove Photo</span>
              </Button>
            )}
          </div>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/jpg,image/png,image/webp"
          onChange={handleImageChange}
          className="hidden"
        />
        
        <p className="text-sm text-gray-500 mt-2">
          Recommended: Square image, at least 200x200px. Max file size: 5MB.
        </p>
      </div>

      {/* Profile Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              First Name *
            </label>
            <input
              type="text"
              {...register("firstname", { required: "First name is required" })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {errors.firstname && (
              <p className="text-red-500 text-sm mt-1">{errors.firstname.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Last Name *
            </label>
            <input
              type="text"
              {...register("lastname", { required: "Last name is required" })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {errors.lastname && (
              <p className="text-red-500 text-sm mt-1">{errors.lastname.message}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Phone Number
          </label>
          <PhoneInputWithCountry
            value={watch("phone")}
            onChange={(value) => setValue("phone", value)}
            placeholder="Enter phone number"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Time Zone
          </label>
          <CustomSelect
            value={watch("timeZone")}
            onValueChange={(value) => setValue("timeZone", value)}
            placeholder="Select your time zone"
          >
            <option value="America/New_York">Eastern Time (ET)</option>
            <option value="America/Chicago">Central Time (CT)</option>
            <option value="America/Denver">Mountain Time (MT)</option>
            <option value="America/Los_Angeles">Pacific Time (PT)</option>
            <option value="Europe/London">Greenwich Mean Time (GMT)</option>
            <option value="Europe/Paris">Central European Time (CET)</option>
            <option value="Asia/Tokyo">Japan Standard Time (JST)</option>
            <option value="Australia/Sydney">Australian Eastern Time (AET)</option>
          </CustomSelect>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Country of Birth
          </label>
          <input
            type="text"
            {...register("countryOfBirth")}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter your country of birth"
          />
        </div>

        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isUpdating}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
          >
            {isUpdating ? (
              <>
                <Loader size="sm" />
                <span>Updating...</span>
              </>
            ) : (
              <>
                <User size={16} />
                <span>Save Changes</span>
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ProfileSettingsForm;
