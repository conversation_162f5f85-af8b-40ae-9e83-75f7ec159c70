import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "swiper/css";

const cards = [
	{ id: 1, title: "Card 1", description: "First card content" },
	{ id: 2, title: "Card 2", description: "Second card content" },
	{ id: 3, title: "Card 3", description: "Third card content" },
	{ id: 4, title: "Card 4", description: "Fourth card content" },
	{ id: 5, title: "Card 5", description: "Fifth card content" },
	{ id: 6, title: "Card 6", description: "Sixth card content" },
	{ id: 7, title: "Card 7", description: "Seventh card content" },
];

const AutoCarousel = () => {
	return (
		<div className="w-full max-w-6xl mx-auto">
			<Swiper
				modules={[Autoplay]}
				slidesPerView={4}
				spaceBetween={20}
				loop={true}
				speed={20055}
				autoplay={{
					delay: 0, // no delay between slides
					disableOnInteraction: false,
				}}
				freeMode={true} // smooth continuous scrolling
			>
				{cards.map((card) => (
					<SwiperSlide key={card.id}>
						<div className="bg-white p-4 rounded-xl shadow-md h-48 flex flex-col justify-center items-center text-center">
							<h2 className="font-bold text-lg">{card.title}</h2>
							<p className="text-sm text-gray-600">{card.description}</p>
						</div>
					</SwiperSlide>
				))}
			</Swiper>
		</div>
	);
};

export default AutoCarousel;
