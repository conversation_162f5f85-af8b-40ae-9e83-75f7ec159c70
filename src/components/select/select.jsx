import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "../ui/select";
import { Controller } from "react-hook-form";

export function CustomSelect({
  name,
  control,
  label,
  placeholder = "Select an option",
  options = [],
  className,
  parentClassName,
  isRequired = false,
  error,
  onChange: externalOnChange
}) {
  return (
    <div className={`space-y-1 w-full ${parentClassName}`}>
      {label && <p className="text-secondary mb-3 max-sm:text-sm">{label}</p>}

      <Controller
        name={name}
        control={control}
        rules={{ required: isRequired }}
        render={({ field }) => (
          <Select
            value={field.value}
            onValueChange={(val) => {
              field.onChange(val);
              if (externalOnChange) externalOnChange({ value: val });
            }}
          >
            <SelectTrigger className={className}>
              <SelectValue
                placeholder={placeholder}
                className="max-sm:text-sm"
              />
            </SelectTrigger>

            <SelectContent>
              {options.map((option) => (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  className="max-sm:text-sm"
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      />

      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );
}
