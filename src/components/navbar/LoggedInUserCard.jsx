import React, { useEffect, useRef, useState } from "react";
import img from "@/assets/images/tutor1.png";
import { ChevronDown, LanguagesIcon } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

// Custom SVG icon imports
import LessonsIcon from "@/assets/svgs/sidebar/lessonsIcon";
import MessageIcon from "@/assets/svgs/sidebar/messageIcon";
import ReviewsIcon from "@/assets/svgs/sidebar/reviewsIcon";
import SettingsIcon from "@/assets/svgs/sidebar/settingsIcon";
import LogoutIcon from "@/assets/svgs/sidebar/logoutIcon";
import DashboardIcon from "@/assets/svgs/sidebar/dashboardIcon";
import StudentsIcon from "@/assets/svgs/sidebar/studentsIcon";
import TutorsIcon from "@/assets/svgs/sidebar/tutorsIcon";
import LanguageSelector from "./components/LanguageSelector";
import LogoutModal from "@/pages/auth/LogoutModal";

// User menu modal component
const UserMenuModal = ({ closeModal, userRole, onLogoutClick }) => {
	const navigate = useNavigate();

	const getRoleBasedRoute = (route) => {
		if (userRole === "tutor") {
			return `/tutor${route}`;
		} else if (userRole === "student") {
			return `/student${route}`;
		}
		return route; // for admin or other roles
	};

	const baseMenuItems = [
		{
			icon: <DashboardIcon className="w-5 h-5" />,
			label: "Dashboard",
			action: () => navigate(getRoleBasedRoute("/dashboard")),
			roles: ["admin", "tutor", "student"],
		},
		{
			icon: <MessageIcon className="w-5 h-5" />,
			label: "Messages",
			action: () => navigate(getRoleBasedRoute("/messages")),
			roles: ["admin", "tutor", "student"],
		},
	];

	const roleSpecificItems = [
		{
			icon: <LessonsIcon className="w-5 h-5" />,
			label: "My Lessons",
			action: () => navigate(getRoleBasedRoute("/my-lessons")),
			roles: ["tutor", "student"],
		},
		{
			icon: <ReviewsIcon className="w-5 h-5" />,
			label: "Reviews",
			action: () => navigate(getRoleBasedRoute("/reviews")),
			roles: ["tutor"],
		},
		{
			icon: <StudentsIcon className="w-5 h-5" />,
			label: "Students",
			action: () => navigate("/admin/students"),
			roles: ["admin"],
		},
		{
			icon: <TutorsIcon className="w-5 h-5" />,
			label: "Tutors",
			action: () => navigate("/admin/tutors"),
			roles: ["admin"],
		},
	];

	const commonItems = [
		{
			icon: <SettingsIcon className="w-5 h-5" />,
			label: "Settings",
			action: () => navigate(getRoleBasedRoute("/settings")),
			roles: ["admin", "tutor", "student"],
		},
		{
			icon: <LogoutIcon className="w-5 h-5" />,
			label: "Logout",
			action: () => {
				closeModal();
				onLogoutClick();
			},
			roles: ["admin", "tutor", "student"],
		},
	];

	const menuItems = [
		...baseMenuItems,
		...roleSpecificItems,
		...commonItems,
	].filter((item) => item.roles.includes(userRole));

	return (
		<div className="absolute right-0 top-full mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50 py-1">
			<div className="flex flex-col">
				{menuItems.map((item, index) => (
					<button
						key={index}
						className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150"
						onClick={() => {
							item.action();
							if (item.label !== "Logout") {
								closeModal();
							}
						}}
					>
						<span className="flex-shrink-0 mr-3 text-gray-400">
							{item.icon}
						</span>
						<span>{item.label}</span>
					</button>
				))}
			</div>
		</div>
	);
};

// Main user card component
const LoggedInUserCard = () => {
	const user = useSelector((state) => state?.app?.userInfo?.user);
	const [isMenuOpen, setIsMenuOpen] = useState(false);
	const [showLogoutModal, setShowLogoutModal] = useState(false);

	const userMenuRef = useRef(null);
	const userBtnRef = useRef(null);
	const langMenuRef = useRef(null);
	const langBtnRef = useRef(null);

	const toggleUserMenu = () => setIsMenuOpen(!isMenuOpen);
	const closeMenus = () => {
		setIsMenuOpen(false);
	};

	const handleLogoutClick = () => {
		setShowLogoutModal(true);
	};

	useEffect(() => {
		const handleClickOutside = (e) => {
			if (
				!userMenuRef.current?.contains(e.target) &&
				!userBtnRef.current?.contains(e.target) &&
				!langMenuRef.current?.contains(e.target) &&
				!langBtnRef.current?.contains(e.target)
			) {
				closeMenus();
			}
		};
		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, []);

	return (
		<div className="relative flex items-center gap-4">
			{/* User Profile & Dropdown */}
			<div className="flex items-center gap-2">
				<button
					ref={userBtnRef}
					className="flex items-center p-2 hover:bg-gray-100 rounded-full transition-colors duration-150"
					onClick={toggleUserMenu}
					aria-label="User menu"
					aria-expanded={isMenuOpen}
				>
					<img
						src={user?.image || img}
						className="object-cover rounded-full w-9 h-9"
						alt="User profile"
					/>
					<span className="hidden sm:block ml-2 font-medium text-sm">
						{user?.fullname}
					</span>
					<ChevronDown
						size={18}
						className={`ml-1 transition-transform duration-200 ${
							isMenuOpen ? "rotate-180" : ""
						}`}
					/>
				</button>

				{isMenuOpen && (
					<div ref={userMenuRef}>
						<UserMenuModal
							closeModal={() => setIsMenuOpen(false)}
							userRole={user?.role?.toLowerCase()}
							onLogoutClick={handleLogoutClick}
						/>
					</div>
				)}
			</div>
			{/* Logout Modal - rendered at the root level */}
			{showLogoutModal && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100]">
					<LogoutModal
						onCancel={() => setShowLogoutModal(false)}
						onConfirm={() => {
							setShowLogoutModal(false);
							// Add your logout logic here
							console.log("Logging out...");
							window.location.href = "/logout";
						}}
					/>
				</div>
			)}
		</div>
	);
};

export default LoggedInUserCard;
