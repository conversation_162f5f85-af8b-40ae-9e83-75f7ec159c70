import React, { useState } from "react";
import { But<PERSON> } from "../button/button";
import { ChevronDownIcon, Menu } from "lucide-react";
import logo from "../../assets/svgs/logo.svg";
import { useNavigate, useLocation } from "react-router-dom";
import { usePostHog } from "@/hooks/usePosthog";
import LoggedInUserCard from "./LoggedInUserCard";
import { useSelector } from "react-redux";

const Navbar = ({ toggleSidebar }) => {
	const navigate = useNavigate();
	const location = useLocation();
	const { capture } = usePostHog();
	const user = useSelector((state) => state?.app?.userInfo?.user);
	console.log("logged in user", user);

	// Check if current path is one of the dashboard routes
	const isDashboardRoute = ["/admin", "/student", "/tutor"].some((route) =>
		location.pathname.startsWith(route)
	);

	const handleSignup = () => {
		capture("signup_clicked", { location: "navbar signup" });
		navigate("/signup/student");
	};

	const navItems =
		location.pathname !== "/"
			? [{ label: "Find Tutors", href: "/find-tutors" }]
			: [
					{ label: "Find Tutors", href: "/find-tutors" },
					{ label: "How It Works", href: "#" },
					{ label: "Become a Tutor", href: "/signup/tutor" },
			  ];

	return (
		<header className="flex w-full items-center justify-between lg:px-8 px-1 py-5 border-b border-[#1B1C1D0A]">
			{/* Logo and sidebar toggle */}
			<div className="flex items-center gap-2">
				{isDashboardRoute && (
					<button
						onClick={toggleSidebar}
						className="lg:hidden p-1 rounded-md hover:bg-gray-100"
					>
						<Menu className="text-2xl text-gray-600" />
					</button>
				)}
				<a className="relative lg:w-[190px] w-[100px] h-[50px]" href="/">
					<span className="sr-only">Go to Convolly homepage</span>
					<img
						src={logo}
						alt="Convolly logo"
						className="lg:w-full md:w-54 md:h-54 md:mt-3 mt-3 lg:h-full cursor-pointer"
					/>
				</a>
			</div>

			{/* Navigation links */}
			<div className="hidden lg:flex flex-1 justify-center mx-8">
				<div className="flex justify-center gap-6">
					{navItems.map((item, index) => (
						<a
							key={index}
							href={item.href}
							className="font-medium text-black sm:text-lg text-base text-center tracking-normal leading-6 whitespace-nowrap"
						>
							{item.label}
						</a>
					))}
				</div>
			</div>

			{/* Desktop Actions */}
			<div className="hidden lg:flex items-center gap-6">
				{user ? (
					<LoggedInUserCard user={user} />
				) : (
					<>
						<Button
							variant="ghost"
							className="p-0 font-bold text-black text-xl"
							onClick={() => navigate("/signin")}
						>
							Login
						</Button>
						<Button
							className="w-[132px] h-[45px] text-[20px]"
							onClick={handleSignup}
						>
							Sign Up
						</Button>
					</>
				)}
			</div>

			{/* Mobile Actions */}
			<div className="flex lg:hidden">
				{user ? (
					<LoggedInUserCard user={user} />
				) : (
					<>
						<Button
							variant="ghost"
							className="p-0 text-black text-xl text-center w-full mr-4"
							onClick={() => navigate("/signin")}
						>
							Login
						</Button>
						<Button className="flex-1 w-full" onClick={handleSignup}>
							Sign Up
						</Button>
					</>
				)}
			</div>
		</header>
	);
};

export default Navbar;
