import React from "react";
import { Link, useNavigate } from "react-router-dom";

const NotFoundPage = () => {
	const navigate = useNavigate();

	return (
		<div className="min-h-screen flex items-center justify-center px-4">
			<div className="max-w-lg w-full text-center">
				{/* 404 Illustration */}
				<div className="mb-8">
					<div className="inline-flex items-center justify-center w-32 h-32 bg-white rounded-full shadow-lg mb-6">
						<svg
							className="w-16 h-16 text-primary"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={1.5}
								d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
							/>
						</svg>
					</div>
					<h1 className="text-8xl font-bold text-primary mb-4">404</h1>
				</div>

				{/* Content */}
				<div className="mb-8">
					<h2 className="text-3xl font-bold text-gray-900 mb-4">
						Page Not Found
					</h2>
					<p className="text-lg text-gray-600 mb-6">
						Oops! The page you're looking for doesn't exist. It might have been
						moved, deleted, or you entered the wrong URL.
					</p>
				</div>

				{/* Action Buttons */}
				<div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
					<button
						onClick={() => navigate(-1)}
						className="w-full sm:w-auto px-6 py-3 bg-primary text-white font-semibold rounded-lg shadow-md hover:bg-green-700 transition duration-300 ease-in-out transform hover:scale-105"
					>
						Go Back
					</button>
					<Link
						to="/"
						className="w-full sm:w-auto inline-block px-6 py-3 bg-white text-green-600 font-semibold rounded-lg shadow-md border-2 border-green-600 hover:bg-green-50 transition duration-300 ease-in-out transform hover:scale-105"
					>
						Go Home
					</Link>
				</div>

				{/* Help Links */}
				<div className="mt-12 pt-8 border-t border-gray-200">
					<p className="text-sm text-gray-500 mb-4">Need help? Try these:</p>
					<div className="flex flex-wrap justify-center gap-4 text-sm">
						<Link
							to="/contact"
							className="text-green-600 hover:text-green-800 underline"
						>
							Contact Support
						</Link>
						<Link
							to="/help"
							className="text-green-600 hover:text-green-800 underline"
						>
							Help Center
						</Link>
					</div>
				</div>
			</div>
		</div>
	);
};

export default NotFoundPage;
