import { useRouteError } from "react-router-dom";

const GlobalErrorPage = () => {
	const error = useRouteError();

	console.error("Route Error:", error);

	return (
		<div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 px-4 text-center">
			<h1 className="text-4xl font-bold text-red-600 mb-2">
				Something went wrong
			</h1>

			<p className="text-gray-700 mb-2">
				<strong>Error:</strong>{" "}
				{error?.statusText || error?.message || "Unexpected error occurred."}
			</p>

			{import.meta.env.DEV && error?.stack && (
				<pre className="bg-red-100 text-left text-sm p-4 mt-4 rounded overflow-x-auto max-w-full max-h-[300px]">
					{error.stack}
				</pre>
			)}

			<button
				onClick={() => window.location.reload()}
				className="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-green-700"
			>
				Reload Page
			</button>
		</div>
	);
};

export default GlobalErrorPage;
