import React from "react";
import { useRouteError } from "react-router-dom";

const ErrorPage = () => {
	const error = useRouteError();

	return (
		<div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 px-4 text-center">
			<h1 className="text-4xl font-bold text-red-600 mb-2">
				Oops! Something went wrong.
			</h1>
			<p className="text-lg text-gray-700 mb-4">
				{error?.statusText || error?.message || "An unexpected error occurred."}
			</p>
			<button
				onClick={() => window.location.reload()}
				className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
			>
				Reload Page
			</button>
		</div>
	);
};

export default ErrorPage;
