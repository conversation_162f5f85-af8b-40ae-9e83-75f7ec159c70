import { cva } from "class-variance-authority";
import * as React from "react";

import { cn } from "../../utils";

export const BadgeCount = ({ count, max = 1000, className }) => {
  if (!count || count <= 0) return null;

  const displayCount = count > max ? max + "+" : count;

  return (
    <span
      className={cn(
        `
        inline-flex items-center justify-center 
        bg-primary text-white text-xs font-normal 
        px-2 py-0.5 rounded-full min-w-[20px]
        `,
        className
      )}
    >
      {displayCount}
    </span>
  );
};

const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",
        outline: "text-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

function Badge({ className, variant = "default", ...props }) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  );
}

export { Badge, badgeVariants };
