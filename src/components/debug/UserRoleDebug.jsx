import React from "react";
import { useSelector } from "react-redux";

const UserRoleDebug = () => {
  const userInfo = useSelector((state) => state?.app?.userInfo);
  const user = userInfo?.user;

  // Only show in development
  if (process.env.NODE_ENV === "production") {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-3 rounded-lg text-xs z-50 max-w-xs">
      <div className="font-bold mb-2">Debug Info:</div>
      <div>Role: {user?.role || "undefined"}</div>
      <div>ID: {user?.id || "undefined"}</div>
      <div>Email: {user?.email || "undefined"}</div>
      <div>Token: {userInfo?.accessToken ? "Present" : "Missing"}</div>
      <div>OnBoarding: {user?.onBoardingStatus || "undefined"}</div>
      <div>Approval: {user?.approvalStatus || "undefined"}</div>
    </div>
  );
};

export default UserRoleDebug;
