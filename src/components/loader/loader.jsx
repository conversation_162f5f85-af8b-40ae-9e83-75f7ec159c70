import { cn } from "@/utils";
import React from "react";

const Loader = ({
  fullscreen = false,
  spinnerOnly = false,
  size = "lg",
  className,
}) => {
  const spinner = (
    <div
      className={cn(
        "w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin",
        { sm: "w-6 h-6 border-2", xs: "w-5 h-5 border-2" }[size],
        className
      )}
    ></div>
  );

  const loader = (
    <div className="absolute inset-0 z-50 flex items-center justify-center bg-black/2 backdrop-blur-sm">
      {spinner}
    </div>
  );

  return fullscreen ? (
    <div className="h-full flex-1 flex items-center justify-center">
      {spinnerOnly ? spinner : loader}
    </div>
  ) : spinnerOnly ? (
    spinner
  ) : (
    loader
  );
};

export default Loader;
