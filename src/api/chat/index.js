import { getConversationMessages, getUserConversations } from "./apiFunction";
import useInfiniteQuery from "@/hooks/useInfiniteQuery";

export const useGetUserConversations = (userId, searchTerm) => {
  return useInfiniteQuery({
    searchTerm,
    queryKey: ["get-user-conversations", searchTerm],
    queryFn: (pagination, searchTerm) => {
      return userId
        ? getUserConversations(userId, {
            cursor: pagination?.nextCursor,
            searchTerm,
          })
        : new Promise(() => undefined);
    },
    enabled: !!userId,
  });
};

export const useGetConversationMessages = (conversationId, userId) => {
  return useInfiniteQuery({
    backward: true,
    queryKey: ["conversation-messages", conversationId],
    queryFn: (pagination) => {
      return conversationId && userId
        ? getConversationMessages(conversationId, userId, {
            cursor: pagination?.nextCursor,
          })
        : new Promise(() => undefined);
    },
    enabled: !!(conversationId && userId),
  });
};
