import axiosInstance from "../axiosInstance";

export const getUserConversations = async (userId, payload) => {
  const res = await axiosInstance.post(
    `/chat/user-conversations/${userId}`,
    payload
  );

  return res.data;
};

export const getConversationMessages = async (
  conversationId,
  userId,
  payload
) => {
  const res = await axiosInstance.post(
    `/chat/conversation-messages/${conversationId}/${userId}`,
    payload
  );

  return res.data;
};
