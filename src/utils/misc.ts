import mongoose from "mongoose";
import { JsonResponseProps, KeyValuePair } from "../types/misc";
import { Response } from "express";
import { PopulateOptions } from "mongoose";
import { getErrorResponse } from "../middlewares/errorHandler";
import { IConversation } from "../models/conversation";
import { PaymentData } from "../services/emailNotificationService";

type JSONResDataProps =
  | {
      message?: string;
      data: JsonResponseProps["data"];
      success?: boolean;
      details?: KeyValuePair | null;
    }
  | string;

export interface PaginateOptions<T> {
  model: mongoose.Model<T>;
  query?: mongoose.FilterQuery<T>;
  limit?: number;
  page?: number; // for offset
  cursor?: string; // for infinite scroll, should be _id or date
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  select?: string | Record<string, 0 | 1>;
  populate?: string | PopulateOptions | (string | PopulateOptions)[];
}

export interface PaginatedResult<T> {
  data: T[];
  details: {
    pagination: {
      type: "cursor" | "page";
      hasMore: boolean;
      nextCursor?: string;
      total?: number; // only for offset
    };
  };
}

export const getJsonResponse = (data: JSONResDataProps, code = "OK") => {
  const response: JsonResponseProps = {
    success: true,
    status: 200,
    code,
    message: "OK",
    data: null,
    details: null,
    datetime: new Date(),
  };

  if (typeof data === "string") {
    response.message = data;
    response.data = null;
  } else {
    response.message = data.message || "OK";
    response.data = data.data || null;

    if (typeof data.success === "boolean") response.success = data.success;

    if (data.details && Object.keys(data.details).length)
      response.details = data.details;
  }

  response.datetime = new Date();

  return response;
};

export const createOkResponse = (
  res: Response,
  data: JSONResDataProps,
  code?: string
) => {
  return res.status(200).json(getJsonResponse(data, code));
};

export const capitalize = (str: string) => {
  if (!str) return "";

  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const getIP = (req: any) => {
  const forwarded = req.headers["x-forwarded-for"];
  const ip =
    typeof forwarded === "string"
      ? forwarded.split(",")[0].trim()
      : req.socket.remoteAddress;

  return ip;
};

export const getReqSessionId = (req: any) => {
  return req.user?.id || getIP(req);
};

export const paginate = async <T>({
  model,
  query = {},
  limit = 20,
  page,
  cursor,
  select,
  populate,
  sortBy = "_id",
  sortOrder = "desc",
}: PaginateOptions<T>): Promise<PaginatedResult<T>> => {
  const isPageMode = typeof page === "number";

  const sort: Record<string, 1 | -1> = {
    [sortBy]: sortOrder === "asc" ? 1 : -1,
  };

  let findQuery = { ...query };

  if (cursor) {
    const cursorQuery =
      sortOrder === "asc"
        ? { [sortBy]: { $gt: cursor } }
        : { [sortBy]: { $lt: cursor } };
    findQuery = { ...findQuery, ...cursorQuery };
  }

  let dbQuery = model
    .find(findQuery)
    .sort(sort)
    .limit(limit + 1);

  if (select) {
    dbQuery = dbQuery.select(select);
  }

  if (populate) {
    if (typeof populate === "string") {
      dbQuery = dbQuery.populate(populate as string);
    } else if (Array.isArray(populate)) {
      dbQuery = dbQuery.populate(populate as PopulateOptions[]);
    } else {
      dbQuery = dbQuery.populate(populate as PopulateOptions);
    }
  }

  const docs = await dbQuery;

  const hasMore = docs.length > limit;
  const data = hasMore ? docs.slice(0, -1) : docs;

  const nextCursor = hasMore
    ? (data[data.length - 1] as any)[sortBy]
    : undefined;

  if (isPageMode) {
    const total = await model.countDocuments(query);
    return {
      data,
      details: {
        pagination: { hasMore, total, type: "page" },
      },
    };
  }

  return {
    data,
    details: {
      pagination: {
        hasMore,
        nextCursor,
        type: "cursor",
      },
    },
  };
};

export const ensureUnder1MB = (data?: any): void => {
  try {
    const MAX_SIZE = 1024 * 1024; // 1MB

    let sizeInBytes: number;

    try {
      if (!data) return;

      if (Buffer.isBuffer(data)) {
        sizeInBytes = data.length;
      } else {
        const json = JSON.stringify(data, (key, value) =>
          Buffer.isBuffer(value) ? value.toString("base64") : value
        );

        sizeInBytes = Buffer.byteLength(json, "utf8");
      }
    } catch (err: any) {
      throw "Failed to serialize data";
    }

    if (sizeInBytes > MAX_SIZE)
      throw `Payload exceeds 1MB: ${sizeInBytes} bytes`;
  } catch (err: any) {
    throw getErrorResponse(err, 400);
  }
};

export const serializeConversation = (conversation: IConversation) => {
  const data: any = { ...conversation.toObject() };

  const obj: KeyValuePair = {};

  for (const doc of data.participants) {
    obj[doc.user.id] = doc.user;
  }

  data.participants = obj;

  return data;
};

export const replaceLastOf = (
  input: string,
  lastOf: string,
  replacement: string
): string => {
  if (!input) return "";

  const lastOfIndex: number = input.lastIndexOf(lastOf);

  if (lastOfIndex === -1) return input;

  return (
    input.substring(0, lastOfIndex) +
    replacement +
    input.substring(lastOfIndex + 1)
  );
};

export const toCurrency = (payment: PaymentData) =>
  `${payment.currency}${payment.amount}`;

export const mapToIdRecords = (obj: KeyValuePair, key: string) => {
  if (Array.isArray(obj[key])) {
    obj[key] = obj[key].map((doc) => {
      delete doc.createdAt;

      delete doc.updatedAt;

      doc = { ...doc };

      delete doc._id;

      if (doc.id) {
        doc.id = doc._id = doc.id;
        delete doc.id;
      }

      return doc;
    });
  }
};

// type AggregatePaginateOptions = {
//   model: any; // Mongoose model
//   pipeline: any[];
//   limit?: number;
//   page?: number;
//   cursor?: string | number;
//   sortBy?: string;
//   sortOrder?: "asc" | "desc";
// };

// type AggregatePaginatedResult<T> = {
//   data: T[];
//   details: {
//     pagination: {
//       hasMore: boolean;
//       nextCursor?: any;
//       total?: number;
//       type: "cursor" | "page";
//     };
//   };
// };

// export const aggregatePaginate = async <T>({
//   model,
//   pipeline,
//   limit = 20,
//   page,
//   cursor,
//   sortOrder = "desc",
// }: AggregatePaginateOptions): Promise<AggregatePaginatedResult<T>> => {
//   limit = Number(limit) || 20;

//   const sortBy = "_id";

//   const isPageMode = typeof page === "number";
//   const sortDirection = sortOrder === "asc" ? 1 : -1;
//   const sortStage = { [sortBy]: sortDirection };

//   const paginatedPipeline = [...pipeline];

//   if (!isPageMode) {
//     // Cursor-based

//     if (cursor) {
//       let parsedCursor: any = cursor;

//       // not sure if this part will be needed yet.
//       // for now only sorting by _id;
//       // Convert string cursors to ObjectId or Date if necessary

//       if (sortBy === "_id" && typeof cursor === "string") {
//         parsedCursor = new mongoose.Types.ObjectId(cursor);
//       } else if (sortBy.includes("At") && typeof cursor === "string") {
//         parsedCursor = new Date(cursor);
//       }

//       paginatedPipeline.unshift({
//         $match: {
//           [sortBy]:
//             sortOrder === "asc" ? { $gt: parsedCursor } : { $lt: parsedCursor },
//         },
//       });
//     }

//     paginatedPipeline.push({ $sort: sortStage });
//     paginatedPipeline.push({ $limit: limit + 1 });

//     const result = await model.aggregate(paginatedPipeline);
//     const hasMore = result.length > limit;
//     const data = hasMore ? result.slice(0, -1) : result;

//     const nextCursor = hasMore
//       ? (data[data.length - 1] as any)?.[sortBy]
//       : undefined;

//     return {
//       data,
//       details: {
//         pagination: {
//           type: "cursor",
//           hasMore,
//           nextCursor,
//         },
//       },
//     };
//   }

//   // Fallback: Page-based

//   const skip = (page - 1) * limit;

//   const pagePipeline = [
//     ...paginatedPipeline.slice(0, -1), // remove old limit
//     { $skip: skip },
//     { $limit: limit },
//   ];

//   const [result, countResult] = await Promise.all([
//     model.aggregate(pagePipeline),
//     model.aggregate([...pipeline, { $count: "total" }]),
//   ]);

//   const total = countResult[0]?.total || 0;

//   return {
//     data: result,
//     details: {
//       pagination: {
//         type: "page",
//         total,
//         hasMore: page * limit < total,
//       },
//     },
//   };
// };
