import mongoose from "mongoose";
import { getErrorResponse } from "../middlewares/errorHandler";
import { PROFILE_MODEL, SINGLE_PROFILE_MODEL } from "../hooks/profile";
import Student from "../models/student";
import <PERSON><PERSON> from "../models/tutor";
import Admin from "../models/admin";

const hasNestedKey = (obj: any, path: string): boolean => {
  return path.split(".").every((key) => {
    if (obj && Object.prototype.hasOwnProperty.call(obj, key)) {
      obj = obj[key];
      return true;
    }
    return false;
  });
};

export const updateMissingFieldForUsers = async (
  profileModel: PROFILE_MODEL,
  fieldPath: string,
  defaultValue: any
): Promise<void> => {
  try {
    const model = profileModel as SINGLE_PROFILE_MODEL;

    const modelName = model.modelName;

    if (!model) {
      throw new Error(`Model "${modelName}" is not defined`);
    }

    const users = await model.find({});

    const bulkOps = users
      .filter((user: any) => !hasNestedKey(user.toObject(), fieldPath))
      .map((user: any) => ({
        updateOne: {
          filter: { _id: user._id },
          update: { $set: { [fieldPath]: defaultValue } },
        },
      }));

    if (bulkOps.length > 0) {
      const result = await model.bulkWrite(bulkOps);
      console.log(
        `${
          result.modifiedCount
        } ${modelName.toLowerCase()}s updated where "${fieldPath}" was missing.`
      );
    } else {
      console.log(
        `All ${modelName.toLowerCase()}s already have "${fieldPath}".`
      );
    }
  } catch (err: any) {
    console.log(getErrorResponse(err));
  }
};

export const updateMissingFieldForAllUsers = async (
  fieldPath: string,
  defaultValue: string
) => {
  await updateMissingFieldForUsers(Student, fieldPath, defaultValue);

  await updateMissingFieldForUsers(Tutor, fieldPath, defaultValue);

  await updateMissingFieldForUsers(Admin, fieldPath, defaultValue);
};
