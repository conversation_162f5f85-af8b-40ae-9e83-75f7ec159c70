export const normalizeDatetime = (datetime?: Date | string | null) => {
  if (!datetime) return new Date();
  else return new Date(datetime);
};

export const startOfPeriod = (days: number | string): Date => {
  const _days = parseInt(days.toString(), 10);

  const date = new Date();
  date.setDate(date.getDate() - _days);
  return date;
};

export const endOfPeriod = (): Date => {
  return new Date(); // Now
};
