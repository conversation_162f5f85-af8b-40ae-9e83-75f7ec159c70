import axios from "axios";
import jwt from "jsonwebtoken";
import { USER_PROFILE_TYPE } from "./profile";

const ZENDESK_SUBDOMAIN = process.env.ZENDESK_SUBDOMAIN!;
const ZENDESK_EMAIL = process.env.ZENDESK_EMAIL!;
const ZENDESK_API_TOKEN = process.env.ZENDESK_API_TOKEN!;

export const createZendeskTicket = async (
  subject: string,
  description: string,
  requester: USER_PROFILE_TYPE
) => {
  const url = `https://${ZENDESK_SUBDOMAIN}.zendesk.com/api/v2/tickets.json`;

  const ticketData = {
    ticket: {
      subject,
      comment: { body: description },
      requester: {
        name: requester.fullname || requester.email,
        email: requester.email,
        external_id: requester.id,
      },
    },
  };

  const auth = {
    username: `${ZENDESK_EMAIL}/token`,
    password: ZENDESK_API_TOKEN,
  };

  const response = await axios.post(url, ticketData, { auth });

  return response.data;
};

export const generateZendeskJWT = (user: {
  id: string;
  email: string;
  name?: string;
}) => {
  const payload = {
    iat: Math.floor(Date.now() / 1000),
    jti: Math.random().toString(36).substring(7),
    name: user.name || user.email,
    email: user.email,
    external_id: user.id,
  };

  const zendeskSecret = process.env.JWT_SECRET!;

  return jwt.sign(payload, zendeskSecret, {
    algorithm: "HS256",
    expiresIn: "5m",
  });
};
