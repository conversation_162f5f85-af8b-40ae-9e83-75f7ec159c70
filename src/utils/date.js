import { DateTime } from "luxon";

export const sortByTimestamp = (items, modify = false) => {
  return (modify ? [...items] : items).sort((a, b) => {
    const aDate = a.sortDate ?? a.updatedAt ?? a.createdAt;
    const bDate = b.sortDate ?? b.updatedAt ?? b.createdAt;

    if (aDate && bDate) return new Date(bDate) - new Date(aDate); // Descending

    // If only a has a date, a comes first
    if (aDate && !bDate) return -1;

    // If only b has a date, b comes first
    if (!aDate && bDate) return 1;

    // Neither has a date, maintain original order (sort returns 0 = no change)
    return 0;
  });
};

export const getLocalTime = (timezone, datetime) => {
  const dt = DateTime.fromISO(
    (datetime ? new Date(datetime) : new Date()).toISOString(),
    {
      zone: "utc",
    }
  ).setZone(timezone);

  return {
    time: dt.toFormat("hh:mm a"), // "03:21 PM"
    fullTime: dt.toFormat("ccc, LLL dd - hh:mm a"), // "Sat, Jul 24 - 03:21 PM"
    raw: dt,
  };
};

/**
 * Returns a human-readable time ago string using Luxon and user timezone.
 *
 * @param {string|Date} datetime - ISO string or Date object
 * @param {string} timezone - User's IANA timezone (e.g. "Africa/Lagos")
 * @param {Object} opts - Optional formatting flags
 * @param {boolean} opts.formatToday - If true, shows time like "03:12 PM"
 * @param {boolean} opts.formatYesterday - If true, shows "1 day ago", otherwise "Yesterday"
 * @returns {string}
 */

export const getTimeAgo = (
  datetime,
  timezone,
  opts = {
    formatToday: true,
    formatYesterday: false,
  }
) => {
  const { formatToday = true, formatYesterday = false } = opts;

  // Get current and passed time in user's timezone
  const now = DateTime.now().setZone(timezone);
  const date = DateTime.fromISO(datetime, { zone: "utc" }).setZone(timezone);

  if (!date.isValid) return "";

  const diffInDays = now
    .startOf("day")
    .diff(date.startOf("day"), "days")
    .toObject().days;

  if (diffInDays === 0) {
    // Today
    return formatToday ? date.toFormat("hh:mm a") : "Today";
  }

  if (diffInDays === 1) {
    // Yesterday
    return formatYesterday
      ? now.toRelative({ base: date }) // "1 day ago"
      : "Yesterday";
  }

  // Older than yesterday
  return date.toFormat("M/d/yyyy"); // Fallback like "7/18/2025"
};
