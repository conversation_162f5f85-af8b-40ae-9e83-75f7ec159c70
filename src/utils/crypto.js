import E2EE from "@chatereum/react-e2ee";
import { isArrayBuffer } from "./validators";
import { createError } from ".";

export const base64ToArrayBuffer = (base64) => {
  // Remove any metadata prefix (like "data:application/pdf;base64,")
  const base64Data = base64.split(",").pop();

  const binaryString = atob(base64Data); // decode base64 to binary string
  const len = binaryString.length;
  const bytes = new Uint8Array(len);

  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  return bytes.buffer; // return ArrayBuffer
};

export const parseToArrayBuffer = (value) => {
  if (isArrayBuffer(value)) return value;

  return base64ToArrayBuffer(value);
};

export const generateKeyPair = async () => {
  const pairs = await E2EE.getKeys();

  return {
    privateKey: pairs.private_key,
    publicKey: pairs.public_key,
  };
};

export const encryptText = async (text, publicKey) => {
  if (!text) return "";

  try {
    return await E2EE.encryptPlaintext({
      public_key: publicKey,
      plain_text: text,
    });
  } catch (err) {
    throw createError(
      "Oops! The file is too big, or your browser may be outdated."
    );
  }
};

export const decryptText = async (encryptedText, privateKey) => {
  if (!encryptedText) return "";

  return await E2EE.decryptForPlaintext({
    encrypted_text: encryptedText,
    private_key: privateKey,
  });
};

export const fileToArrayBuffer = async (file) => {
  const arrayBuffer = await file.arrayBuffer();
  return arrayBuffer;
};

export const encryptFile = async (file, recipientPublicKey) => {
  try {
    return await E2EE.encryptFileBuffer({
      public_key: recipientPublicKey,
      file_buffer: await fileToArrayBuffer(file),
    });
  } catch (err) {
    throw createError(
      `Oops! This file is too large, or your browser may be outdated.`
    );
  }
};

export const decryptFile = async (encryptedFile, privateKey) => {
  return await E2EE.decryptFileBuffer({
    encrypted_buffer: {
      ...encryptedFile,
      cipher_buffer: parseToArrayBuffer(encryptedFile.cipher_buffer),
    },
    private_key: privateKey,
  });
};
