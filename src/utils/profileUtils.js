import { setUserInfo } from "@/redux/appSlice";

/**
 * Updates the Redux store with new user profile data
 * @param {Function} dispatch - Redux dispatch function
 * @param {Object} currentUser - Current user data from Redux store
 * @param {Object} updatedData - New user data from API response
 */
export const updateUserInStore = (dispatch, currentUser, updatedData) => {
  if (updatedData) {
    dispatch(setUserInfo({
      user: {
        ...currentUser,
        ...updatedData,
      }
    }));
  }
};

/**
 * Handles profile update response and updates Redux store
 * @param {Function} dispatch - Redux dispatch function
 * @param {Object} currentUser - Current user data from Redux store
 * @param {Object} response - API response object
 * @param {Function} onSuccess - Optional success callback
 * @param {Function} onError - Optional error callback
 */
export const handleProfileUpdateResponse = (
  dispatch, 
  currentUser, 
  response, 
  onSuccess = null, 
  onError = null
) => {
  try {
    if (response?.data) {
      updateUserInStore(dispatch, currentUser, response.data);
      if (onSuccess) onSuccess(response);
    }
  } catch (error) {
    console.error("Error updating user in store:", error);
    if (onError) onError(error);
  }
};
