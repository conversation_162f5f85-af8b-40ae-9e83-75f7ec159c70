import { clsx } from "clsx";
import { toast } from "react-toastify";
import { twMerge } from "tailwind-merge";

export const cn = (...inputs) => {
  return twMerge(clsx(inputs));
};

export const capitalizeWords = (str) => {
  const words = str?.split(" ");

  const capitalizedWords = words?.map((word) => {
    if (word.length === 0) {
      return "";
    }
    return word[0].toUpperCase() + word.slice(1);
  });

  return capitalizedWords?.join(" ");
};

export const convertToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};

export const removeDuplicate = (arr = []) => {
  const seen = new Set();
  return arr.filter((item) => {
    const key = item.tempId || item.id;

    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
};

export function debounce(fn, delay) {
  let timer = null;

  const debounced = (...args) => {
    if (timer) clearTimeout(timer);

    timer = setTimeout(() => {
      fn(...args);
      timer = null;
    }, delay);
  };

  // Optional cancel method
  debounced.cancel = () => {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  };

  return debounced;
}

export const timezones = [
  { value: "Africa/Lagos", label: "Lagos (UTC+1)" },
  { value: "America/New_York", label: "New York (UTC-5/-4)" },
  { value: "America/Los_Angeles", label: "Los Angeles (UTC-8/-7)" },
  { value: "Europe/London", label: "London (UTC+0/+1)" },
  { value: "Asia/Kolkata", label: "Kolkata (UTC+5:30)" },
  { value: "Asia/Tokyo", label: "Tokyo (UTC+9)" },
  { value: "Australia/Sydney", label: "Sydney (UTC+10/+11)" },
  { value: "Europe/Paris", label: "Paris (UTC+1/+2)" },
  { value: "Europe/Berlin", label: "Berlin (UTC+1/+2)" },
  { value: "Africa/Cairo", label: "Cairo (UTC+2)" },
  { value: "America/Chicago", label: "Chicago (UTC-6/-5)" },
  { value: "America/Denver", label: "Denver (UTC-7/-6)" },
  { value: "America/Sao_Paulo", label: "São Paulo (UTC-3)" },
  { value: "Asia/Dubai", label: "Dubai (UTC+4)" },
  { value: "Asia/Shanghai", label: "Shanghai (UTC+8)" },
  { value: "Asia/Singapore", label: "Singapore (UTC+8)" },
  { value: "Europe/Moscow", label: "Moscow (UTC+3)" },
  { value: "Africa/Johannesburg", label: "Johannesburg (UTC+2)" },
  { value: "Pacific/Auckland", label: "Auckland (UTC+12/+13)" },
  { value: "America/Toronto", label: "Toronto (UTC-5/-4)" },
];

export const getFileType = (file) => {
  const mime = file.type;
  const name = file.name.toLowerCase();

  if (mime.startsWith("image/"))
    return {
      kind: "Image",
      label: "Image File",
    };

  if (mime === "application/pdf")
    return {
      kind: "Document",
      label: "PDF Document",
    };

  if (
    mime === "application/msword" ||
    mime ===
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
    name.endsWith(".doc") ||
    name.endsWith(".docx")
  )
    return {
      kind: "Document",
      label: "Word Document",
    };

  if (
    mime === "application/vnd.ms-excel" ||
    mime ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
    name.endsWith(".xls") ||
    name.endsWith(".xlsx")
  )
    return {
      kind: "Spreedsheet",
      label: "Excel Spreadsheet",
    };

  if (
    mime === "application/vnd.ms-powerpoint" ||
    mime ===
      "application/vnd.openxmlformats-officedocument.presentationml.presentation" ||
    name.endsWith(".ppt") ||
    name.endsWith(".pptx")
  )
    return {
      kind: "Presentation",
      label: "PowerPoint Presentation",
    };

  return null;
};

const FILE_SIZE_UNITS = ["Bytes", "KB", "MB", "GB", "TB"];

export const getSizeLabel = (sizeInBytes) => {
  let index = 0;
  let size = sizeInBytes;

  while (size >= 1024 && index < FILE_SIZE_UNITS.length - 1) {
    size /= 1024;
    index++;
  }

  return `${size.toFixed(size < 10 ? 1 : 0)} ${FILE_SIZE_UNITS[index]}`;
};

export const getFileMeta = (file) => {
  const type = getFileType(file);

  if (!type) return null;

  return {
    label: type.label,
    kind: type.kind,
    sizeLabel: getSizeLabel(file.size),
  };
};

/**
 * Download a file or blob with optional callbacks
 *
 * @param {File|Blob} file - The file or blob to download
 * @param {Object} options - Optional callbacks
 * @param {function(percent: number)} [options.onProgress] - Simulated progress callback
 * @param {function()} [options.onSuccess] - Called on successful download
 * @param {function(error: any)} [options.onError] - Called on error
 */

export const downloadFile = (file, options = {}) => {
  const { onProgress, onSuccess, onError } = options;

  try {
    const blob = new Blob([file], { type: file.type });
    const url = URL.createObjectURL(blob);

    let progress = 0;
    const step = 10;
    const interval = 100;

    const progressInterval = setInterval(() => {
      progress += step;
      onProgress?.(Math.min(progress, 100));

      if (progress >= 100) {
        clearInterval(progressInterval);

        const a = document.createElement("a");
        a.href = url;
        a.download = file.name || "download";
        document.body.appendChild(a);
        a.click();
        a.remove();

        URL.revokeObjectURL(url);

        onSuccess?.();
      }
    }, interval);
  } catch (error) {
    onError?.(error);
    toast.error("Something went wrong, while downloading file.");
  }
};

export const formatTutorName = (fullName) => {
  if (!fullName) return "";

  const names = fullName.trim().split(" ");

  if (names.length === 0) return "";

  // First name - keep as is (assuming it's already properly capitalized)
  const firstName = names[0][0].toUpperCase() + names[0].slice(1).toLowerCase();

  // Last name - just first letter capitalized followed by dot
  let lastNameInitial = "";
  if (names.length > 1) {
    lastNameInitial = names[names.length - 1][0]?.toUpperCase() + ".";
  }

  return [firstName, lastNameInitial].filter(Boolean).join(" ");
};

export const createError = (message) => {
  return {
    response: {
      message,
    },
  };
};
