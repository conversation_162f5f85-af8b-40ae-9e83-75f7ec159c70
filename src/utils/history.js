import { router } from "@/_config/inAppUrl";

export const getCurrentFromPath = (encode = true) => {
  const search = window.location.search;

  const url = `${window.location.pathname}${
    search ? (search.startsWith("?") ? search : `?${search}`) : ""
  }${window.location.hash}`;

  return encode ? encodeURIComponent(url) : url;
};

/**
 * Updates the current URL search params with new values.
 * - Overrides existing params.
 * - Prevents duplicates automatically.
 *
 * @param {Object} newParams - An object of new query parameters.
 * @param {boolean} [pushState=false] - Whether to navigate. push if true, replace if false else ignore.
 * @returns {string} - The updated search string (e.g., ?page=2&sort=asc).
 */

export const updateSearchParams = (newParams = {}, pushState) => {
  const url = new URL(window.location.href);
  const searchParams = new URLSearchParams(url.search);

  Object.keys(newParams).forEach((key) => {
    const value = newParams[key];
    if (value === null || value === undefined || value === "") {
      searchParams.delete(key); // remove param if value is null/empty
    } else {
      searchParams.set(key, value); // override or set new
    }
  });

  const newSearch = `?${searchParams.toString()}`;

  if (pushState !== undefined) {
    const newUrl = `${url.pathname}${newSearch}${url.hash}`;

    if (pushState) router.navigate(newUrl);
    else router.navigate(newUrl, { replace: true });
  }

  return newSearch;
};
