import mongoose from "mongoose";

export const passwordMatchCondition: [RegExp, string] = [
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/,
  "Password must be at least 8 characters and contain at least one uppercase letter, one lowercase letter, one number, and one special character.",
];

export const isValidObjectId = (
  id?: string | mongoose.Types.ObjectId
): boolean => {
  if (!id) return false;

  id = id.toString();
  return (
    mongoose.Types.ObjectId.isValid(id) &&
    new mongoose.Types.ObjectId(id).toString() === id
  );
};

export const isObject = (value: any) => {
  return (
    typeof value === "object" &&
    value !== null &&
    !Array.isArray(value) &&
    Object.prototype.toString.call(value) === "[object Object]"
  );
};
