import { getFileMeta } from ".";

export const isGBSize = (size) => size >= 1024 * 1024 * 1024;

export const isValidFile = (file) => {
  if (isGBSize(file.size))
    return { error: "One or more files exceed the 1GB limit." };

  const meta = getFileMeta(file);

  if (meta) return { meta };

  return { error: "Unsupported File" };
};

export const isArrayBuffer = (value) => {
  if (value instanceof ArrayBuffer) return true;

  return false;
};
