import jwt, { JwtPayload } from "jsonwebtoken";
import bcrypt from "bcryptjs";
import crypto from "crypto";
import { JwtSignPayload } from "../middlewares/auth";
import { ChatTokenBuilder, RtcTokenBuilder, Rtc<PERSON>ole } from "agora-token";
import { custom<PERSON>lphabet } from "nanoid";
import Classroom from "../models/classroom";
import { getErrorResponse } from "../middlewares/errorHandler";

const appID = process.env.AGORA_APP_ID!;
const appCertificate = process.env.AGORA_APP_CERTIFICATE!;

export const generateToken = (
  payload: JwtSignPayload,
  opts?: Partial<JwtPayload> & Partial<{ rememberMe?: boolean }>
) => {
  const { rememberMe, ...jwtOpts } = opts || {};

  return jwt.sign(payload, process.env.JWT_SECRET!, {
    ...jwtOpts,
    expiresIn: jwtOpts?.expiresIn || (rememberMe ? "30d" : "5h"),
  });
};

export const hashPassword = async (value: string) =>
  await bcrypt.hash(value, 12);

export const comparePassword = async (value: string, hash: string) =>
  await bcrypt.compare(value, hash);

// Generate encryption data for user profiles
export const generateEncryptionData = () => {
  // Generate a random private key
  const privateKey = crypto.randomBytes(32).toString('hex');

  // Generate a public key (for demo purposes, using a simple derivation)
  // In a real application, you might use proper key pair generation
  const publicKey = crypto.createHash('sha256').update(privateKey).digest('hex');

  // Generate IV (Initialization Vector) - 16 bytes for AES
  const iv = crypto.randomBytes(16);

  // Generate salt
  const salt = crypto.randomBytes(32).toString('hex');

  // Encrypt the private key using a master key (from environment)
  const masterKey = process.env.ENCRYPTION_MASTER_KEY || 'default-master-key-change-in-production';

  // Create a key from the master key using PBKDF2
  const key = crypto.pbkdf2Sync(masterKey, salt, 10000, 32, 'sha256');

  // Create cipher with the key and IV
  const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
  let encryptedPrivateKey = cipher.update(privateKey, 'utf8', 'hex');
  encryptedPrivateKey += cipher.final('hex');

  return {
    encryptedPrivateKey,
    publicKey,
    iv: iv.toString('hex'),
    salt
  };
};

export const generateRTCToken = (channelName: string, userId: string) => {
  const role = RtcRole.PUBLISHER;

  const expirationTimeInSeconds = 86400; // Token valid for 1 day

  const currentTimestamp = Math.floor(Date.now() / 1000);

  const privilegeExpire = currentTimestamp + expirationTimeInSeconds;
  const tokenExpire = privilegeExpire;

  return RtcTokenBuilder.buildTokenWithUid(
    appID,
    appCertificate,
    channelName,
    userId,
    role,
    tokenExpire,
    privilegeExpire
  );
};

export const generateAgoraChatToken = (expire = 86400) => {
  return ChatTokenBuilder.buildAppToken(
    process.env.AGORA_APP_ID!,
    process.env.AGORA_APP_CERTIFICATE!,
    expire
    
  );
};
console.log(process.env.AGORA_APP_ID)
export const generateAgoraChatUserToken = (userId: string, expire = 86400) => {
  return ChatTokenBuilder.buildUserToken(
    process.env.AGORA_APP_ID!,
    process.env.AGORA_APP_CERTIFICATE!,
    userId,
    expire
  );
};

const generateUniqLetters = () => {
  const alphabet = "abcdefghijklmnopqrstuvwxyz";

  const generate3 = customAlphabet(alphabet, 3);
  const generate4 = customAlphabet(alphabet, 4);

  const part1 = generate3();
  const part2 = generate4();
  const part3 = generate3();

  return `${part1}-${part2}-${part3}`;
};

export const generateChannelId = async (): Promise<string> => {
  let attempts = 0;

  const maxAttempts = 1000;

  let channelId: string;

  while (attempts < maxAttempts) {
    channelId = generateUniqLetters();

    const exists = await Classroom.exists({ channelId });

    if (!exists) {
      return channelId;
    }

    attempts++;
  }

  throw getErrorResponse(`Unable to generate channel id, please try again`);
};
