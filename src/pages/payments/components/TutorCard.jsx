import tutor from "../../../assets/images/tutor2.png";
import country from "../../../assets/svgs/usa.svg";
import checked from "../../../assets/svgs/greencheck.svg";
import star from "../../../assets/svgs/payments/star.svg";

const TutorCard = () => {
	return (
		<div className="flex items-center gap-4">
			{/* Profile Image */}
			<img
				src={tutor}
				alt="Tutor img"
				className="w-[78px] h-[90px] border-[#E8E8E8] rounded-md object-cover"
			/>

			{/* Info Section */}
			<div className="flex flex-col">
				<div className="flex items-center gap-2 text-lg font-semibold text-gray-800">
					<p className="font-bold text-[26px] text-[#1A1A40]">
						<PERSON>.
					</p>
					<img src={checked} alt="check icon" />
					<img src={country} alt="country icon" />
				</div>

				{/* Reviews */}
				<div className="flex items-center h-[39px] mt-2 pt-2">
					<div className="text-[26px] flex">
						<img src={star} alt="rating icon" className="pb-1 pr-2" />
						<p className="gap-2 pr-2 font-bold">4.5</p>
						<span className="text-md text-[#4B5563] font-bold">
							(60 reviews)
						</span>
					</div>
				</div>
			</div>
		</div>
	);
};

export default TutorCard;
