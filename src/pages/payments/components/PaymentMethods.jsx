import { useState } from "react";
import { CardElement, useStripe, useElements } from "@stripe/react-stripe-js";
import { Button } from "@/components/button/button"; // Adjust import as needed
import visa from "../../../assets/svgs/payments/Visa-logo.svg";
import mastercard from "../../../assets/svgs/payments/mastercard.svg";
import { useSelector } from "react-redux";

const PaymentMethods = ({ tutor, onCompletePayment, lessonsPerWeek }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [selectedMethod] = useState("credit"); // Only credit card option
  const [error, setError] = useState(null);
  const [processing, setProcessing] = useState(false);
  const [cardholderName, setCardholderName] = useState(""); // State for cardholder name
  const token = useSelector((state) => state?.app?.userInfo?.accessToken);
  const studentEmail = useSelector(
    (state) => state?.app?.userInfo?.user?.email
  );

  const RadioButton = ({ checked }) => (
    <div
      className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all ${
        checked ? "border-primary bg-primary" : "border-gray-300 bg-white"
      }`}
    >
      {checked && <div className="w-2 h-2 rounded-full bg-white"></div>}
    </div>
  );

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements) {
      setError("Stripe has not loaded correctly. Please try again.");
      return;
    }

    if (!tutor || !tutor.basePrice) {
      setError("Tutor information or base price is missing.");
      return;
    }

    if (!cardholderName.trim()) {
      setError("Please enter the cardholder's name.");
      return;
    }

    if (!token) {
      setError("Authentication token is missing. Please log in again.");
      return;
    }

    setProcessing(true);
    try {
      // Generate a Stripe token
      const cardElement = elements.getElement(CardElement);
      const { token: stripeToken, error: tokenError } =
        await stripe.createToken(cardElement, {
          name: cardholderName.trim()
        });

      if (tokenError) {
        console.error("Stripe token error:", tokenError);
        setError(tokenError.message);
        setProcessing(false);
        return;
      }

      if (!stripeToken) {
        setError("Failed to generate Stripe token.");
        setProcessing(false);
        return;
      }

      // Create a PaymentIntent on your server
      const response = await fetch(
        "https://convolly-backend.onrender.com/api/subscription/subscribe",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`
          },
          body: JSON.stringify({
            email: studentEmail,
            tutorId: tutor?.id,
            lessonsPerWeek,
            cardToken: stripeToken.id
          })
        }
      );

      // Check if the response is OK
      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 401) {
          throw new Error(
            "Unauthorized: Invalid or expired token. Please log in again."
          );
        }
        throw new Error(
          errorData.suggestion || `Server responded with status ${response.status}`
        );
      }

      const { clientSecret } = await response.json();

      // Confirm the payment with Stripe
      const { error: confirmError, paymentIntent } =
        await stripe.confirmCardPayment(clientSecret, {
          payment_method: {
            card: cardElement,
            billing_details: { name: cardholderName.trim() }
          }
        });

      if (confirmError) {
        console.error("Stripe confirm error:", confirmError);
        setError(confirmError.message);
        setProcessing(false);
      } else {
        if (onCompletePayment) {
          onCompletePayment(paymentIntent);
        } else {
          console.warn("onCompletePayment is not provided.");
        }
        setProcessing(false);
      }
    } catch (err) {
      console.error("Payment error:", err);
      setError(
        err.message || "An error occurred while processing the payment."
      );
      setProcessing(false);
    }
  };

  return (
    <div className="w-full mx-auto">
      <h2 className="font-bold text-lg text-[#1A1A40] pb-5 capitalize">
        Payment Method
      </h2>
      <div className="space-y-4">
        <div className="w-full flex items-center p-4 border rounded-lg transition-all border-primary bg-green-50">
          <RadioButton checked={selectedMethod === "credit"} />
          <div className="flex-1 flex items-center justify-between ml-3">
            <h3 className="font-medium text-[#1A1A40]">
              Credit card or debit card
            </h3>
            <div className="flex gap-2">
              <img src={visa} alt="Visa icon" />
              <img src={mastercard} alt="Mastercard icon" />
            </div>
          </div>
        </div>

        <form
          onSubmit={handleSubmit}
          className="border border-primary mt-2 rounded-md p-4"
        >
          <div className="mb-4">
            <label className="block text-sm font-medium text-[#1A1A40] mb-1">
              Cardholder Name
            </label>
            <input
              type="text"
              value={cardholderName}
              onChange={(e) => setCardholderName(e.target.value)}
              placeholder="Enter cardholder's name"
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: "16px",
                  color: "#1A1A40",
                  "::placeholder": { color: "#aab7c4" }
                },
                invalid: { color: "#9e2146" }
              }
            }}
          />
          {error && <div className="text-red-500 mt-2">{error}</div>}
          <Button
            type="submit"
            disabled={!stripe || processing}
            className="bg-primary p-3 rounded-md text-white w-full text-lg font-bold mt-4"
          >
            {processing ? "Processing..." : "Confirm monthly subscription"}
          </Button>
        </form>

        <div className="pt-6">
          <p className="text-md text-[#1A1A40]">
            By pressing the "Confirm monthly subscription" button, you agree to{" "}
            <a href="#" className="underline">
              Convolly's Refund and Payment Policy
            </a>
          </p>
          <div className="bg-[#1FC16B1A] p-3 rounded-lg mt-3">
            You can change your tutor for free or cancel your subscription at
            any time
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentMethods;
