import React, { useState } from "react";

const PriceToggler = () => {
	const [activeOption, setActiveOption] = useState("25mins");

	return (
		<div className="flex text-[#1A1A40] h-[55px] bg-gray-100 rounded-md p-1 mx-auto w-full">
			<button
				className={`h-[40px] w-full p-2 m-1 mt-1 flex justify-center left-[8px]  rounded-md text-center cursor-pointer transition-all duration-100 ${
					activeOption === "25mins"
						? "bg-[#FFFFFF] shadow-sm"
						: "bg-transparent"
				}`}
				onClick={() => setActiveOption("25mins")}
			>
				<div className="flex text-lg font-bold text-[#1A1A40]">
					<p>25 mins</p>-<p>$2</p>
				</div>
			</button>

			<button
				className={`h-[40px] w-full p-2 m-1 mt-1 flex justify-center left-[8px]rounded-md text-center cursor-pointer transition-all duration-100 ${
					activeOption === "50mins" ? "bg-white shadow-sm" : "bg-transparent"
				}`}
				onClick={() => setActiveOption("50mins")}
			>
				<div className="flex text-lg font-bold text-[#1A1A40]">
					<p>50 mins</p>-<p>$4</p>
				</div>
			</button>
		</div>
	);
};

export default PriceToggler;
