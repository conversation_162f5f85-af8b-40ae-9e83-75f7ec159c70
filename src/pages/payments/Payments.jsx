import Navbar from "@/components/navbar/navbar";
import React from "react";
import TutorCard from "./components/TutorCard";
import PriceToggler from "./components/PriceTogler";
import PaymentMethods from "./components/PaymentMethods";
import users from "../../assets/svgs/payments/users.svg";
import books from "../../assets/svgs/payments/book-edit.svg";
import check from "../../assets/svgs/payments/BadgeCheck.svg";
import SuccessModal from "./components/SuccessModal";
import { useNavigate } from "react-router-dom";

const Payments = () => {
  const [showSuccessOverlay, setShowSuccessOverlay] = React.useState(false);
  const navigate = useNavigate();

  const handlePaymentComplete = () => {
    setShowSuccessOverlay(true);
  };

  const closeOverlay = () => {
    setShowSuccessOverlay(false);
  };

  const handleContinue = () => {
    setShowSuccessOverlay(false);
    navigate("/post-payment");
  };

  return (
    <div className="relative w-full bg-white">
      <Navbar />
      <div className="max-w-[979px] sm:mt-16 mt-10 mx-auto px-4 lg:px-0">
        {/* Main container - responsive flex layout */}
        <div className="flex flex-col lg:flex-row justify-center gap-6 lg:gap-10">
          {/* Order Summary Card */}
          <div className="border border-[#E8E8E8] p-6 rounded-md w-full lg:w-[481px] order-1">
            <div className="space-y-6">
              {/* Tutor Info Section */}
              <div className="space-y-4">
                <div className="space-y-4">
                  <div className="h-[90px]">
                    <TutorCard />
                  </div>
                  <div className="flex gap-[19px] text-[#4B5563] pt-6 text-[18px] font-medium">
                    <div className="flex flex-col items-start gap-2 w-[144px]">
                      <img
                        src={users}
                        alt="students icon"
                        className="w-7 h-7"
                      />
                      <p>300 students</p>
                    </div>
                    <div className="flex flex-col items-start gap-2 w-[268px]">
                      <img src={books} className="w-7 h-7" alt="lessons icon" />
                      <p>400 lessons completed</p>
                    </div>
                  </div>
                </div>

                <hr className="bg-[#E8E8E8] h-px border-0" />

                {/* Trial Lesson Section */}
                <div className="space-y-4">
                  <div className="space-y-5">
                    <p className="text-[#1A1A40] text-[22px] font-bold">
                      Your trial lesson
                    </p>
                    <div className="h-[55px]">
                      <PriceToggler />
                    </div>
                    <div className="flex justify-between items-center p-2 mt-2">
                      <div className="text-[#1A1A40] w-[41px] flex flex-col items-center">
                        <p className="uppercase text-sm">thu</p>
                        <p className="text-2xl font-bold">14</p>
                      </div>
                      <div className="border-l pl-4 flex-1 flex flex-col">
                        <p className="font-medium text-[#1A1A40]">
                          Saturday, 10:00-10:25
                        </p>
                        <p className="text-[#4B5563] text-[16px]">
                          Time zone: Africa/Lagos (GMT +1:00)
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <hr className="bg-[#E8E8E8] h-px border-0" />

              {/* Order Summary */}
              <div className="space-y-4">
                <p className="text-[#1A1A40] font-bold text-[22px]">
                  Your order
                </p>
                <div className="space-y-2">
                  <div className="flex justify-between text-[#4B5563] font-medium text-sm">
                    <p>25-min lesson</p>
                    <p>$1.80</p>
                  </div>
                  <div className="flex justify-between text-[#4B5563] font-medium text-sm">
                    <p>Processing fee</p>
                    <p>$0.80</p>
                  </div>
                  <div className="flex justify-between items-center pt-2 border-t border-[#E8E8E8]">
                    <p className="text-lg font-bold text-[#1A1A40]">Total</p>
                    <p className="text-lg font-bold text-[#1A1A40]">$2.60</p>
                  </div>
                </div>
              </div>

              {/* Guarantee Section */}
              <div className="bg-[#1FC16B1A] p-4 flex flex-auto rounded-md">
                <div className="top-0">
                  <img src={check} alt="badge icon" />
                </div>
                <div className="mx-4">
                  <p className="font-lg text-[#1A1A40] mb-1">
                    Free replacement or refund
                  </p>
                  <p className="text-md text-[18px] text-[#4B5563]">
                    Try another tutor for free or get a <br /> refund
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Methods Section */}
          <div className="w-full lg:w-auto order-2 lg:order-2">
            <PaymentMethods onCompletePayment={handlePaymentComplete} />
          </div>

          {/* Success Overlay */}
          {showSuccessOverlay && (
            <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
              <SuccessModal
                isOpen={showSuccessOverlay}
                onClose={closeOverlay}
                onContinue={handleContinue}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Payments;
