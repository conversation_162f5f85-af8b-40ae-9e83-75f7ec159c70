const API_URL = "https://convolly-backend.onrender.com/api";

/**
 * Enhanced API client for chat operations with proper error handling
 */
class ChatApiError extends Error {
  constructor(message, status, details) {
    super(message);
    this.name = "ChatApiError";
    this.status = status;
    this.details = details;
  }
}

/**
 * Generic API request handler with error handling
 */
const apiRequest = async (url, options = {}) => {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new ChatApiError(
        data.message || "API request failed",
        response.status,
        data.details
      );
    }

    if (!data.success) {
      throw new ChatApiError(
        data.message || "Request failed",
        data.status || 400,
        data.details
      );
    }

    return data;
  } catch (error) {
    if (error instanceof ChatApiError) {
      throw error;
    }

    // Network or other errors
    throw new ChatApiError("Network error or server unavailable", 0, {
      originalError: error.message,
    });
  }
};

/**
 * Build query string from parameters
 */
const buildQueryString = (params) => {
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, value.toString());
    }
  });
  return searchParams.toString();
};

/**
 * Get conversation messages with pagination
 * @param {string} userId - User ID (for backward compatibility)
 * @param {string} conversationId - Conversation ID
 * @param {string} accessToken - Authentication token
 * @param {Object} options - Pagination options
 * @returns {Promise<Object>} Messages data with pagination info
 */
const getMessages = async (
  userId,
  conversationId,
  accessToken,
  options = {}
) => {
  // Handle both old and new function signatures
  let actualConversationId, actualAccessToken, actualOptions;

  if (
    typeof userId === "string" &&
    typeof conversationId === "string" &&
    typeof accessToken === "string"
  ) {
    // Old signature: getMessages(userId, conversationId, accessToken)
    actualConversationId = conversationId;
    actualAccessToken = accessToken;
    actualOptions = options || {};
  } else {
    // New signature: getMessages(conversationId, accessToken, options)
    actualConversationId = userId;
    actualAccessToken = conversationId;
    actualOptions = accessToken || {};
  }

  const { limit = 20, sortOrder = "asc", cursor = null } = actualOptions;

  const queryParams = buildQueryString({
    limit,
    sortOrder,
    cursor,
  });

  const url = `${API_URL}/chat/conversation-messages/${actualConversationId}?${queryParams}`;

  try {
    const data = await apiRequest(url, {
      method: "GET",
      headers: {
        authorization: `Bearer ${actualAccessToken}`,
      },
    });

    return {
      messages: data.data || [],
      pagination: data.details?.pagination || {},
      conversationId: data.details?.conversationId,
      participantCount: data.details?.participantCount,
    };
  } catch (error) {
    // Fallback to old API format if new format fails
    console.warn("New messages API failed, trying old format:", error);
    try {
      const res = await fetch(
        `${API_URL}/chat/conversation-messages/${actualConversationId}/${
          userId || "user"
        }`,
        {
          method: "POST",
          headers: {
            authorization: `Bearer ${actualAccessToken}`,
          },
        }
      );
      const data = await res.json();
      return {
        messages: data.data || [],
        pagination: {},
        conversationId: actualConversationId,
        participantCount: 2,
      };
    } catch (fallbackError) {
      console.error("Both message API formats failed:", fallbackError);
      throw error; // Throw original error
    }
  }
};

/**
 * Get user conversations with pagination
 * @param {string} userId - User ID
 * @param {string} accessToken - Authentication token
 * @param {Object} options - Pagination options
 * @returns {Promise<Object>} Conversations data with pagination info
 */
const getUserConversations = async (userId, accessToken, options = {}) => {
  const { limit = 20, sortOrder = "desc", cursor = null } = options;

  const queryParams = buildQueryString({
    limit,
    sortOrder,
    cursor,
  });

  const url = `${API_URL}/chat/user-conversations/${userId}?${queryParams}`;

  try {
    const data = await apiRequest(url, {
      method: "GET",
      headers: {
        authorization: `Bearer ${accessToken}`,
      },
    });

    return {
      conversations: data.data || [],
      pagination: data.details?.pagination || {},
      totalConversations: data.details?.totalConversations || 0,
      isOwnProfile: data.details?.isOwnProfile || false,
    };
  } catch (error) {
    // Fallback to old API format if new format fails
    console.warn("New API failed, trying old format:", error);
    try {
      const res = await fetch(`${API_URL}/chat/user-conversations/${userId}`, {
        method: "POST",
        headers: {
          authorization: `Bearer ${accessToken}`,
        },
      });
      const data = await res.json();
      return {
        conversations: data.data || [],
        pagination: {},
        totalConversations: 0,
        isOwnProfile: true,
      };
    } catch (fallbackError) {
      console.error("Both API formats failed:", fallbackError);
      throw error; // Throw original error
    }
  }
};

/**
 * Legacy auth functions (keeping for compatibility)
 */
const registerUser = async (payload) => {
  return await apiRequest(`${API_URL}/auth/register`, {
    method: "POST",
    body: JSON.stringify(payload),
  });
};

const loginUser = async (payload) => {
  const data = await apiRequest(`${API_URL}/auth/login`, {
    method: "POST",
    body: JSON.stringify(payload),
  });

  return {
    ...data.data.user,
    accessToken: data.data.accessToken,
  };
};

/**
 * Get user profile information
 * @param {Object} payload - User query payload
 * @returns {Promise<Object>} User data
 */
const getUser = async (payload) => {
  const data = await apiRequest(`${API_URL}/get-user`, {
    method: "POST",
    body: JSON.stringify(payload),
  });

  return data.data;
};

/**
 * Get list of users for chat
 * @returns {Promise<Array>} Users list
 */
const getUsers = async () => {
  const data = await apiRequest(`${API_URL}/chat-users`);
  return data.data;
};

/**
 * Search for users or conversations
 * @param {string} query - Search query
 * @param {string} accessToken - Authentication token
 * @param {Object} options - Search options
 * @returns {Promise<Object>} Search results
 */
const searchChats = async (query, accessToken, options = {}) => {
  const {
    type = "all", // 'users', 'conversations', 'messages', 'all'
    limit = 10,
  } = options;

  const queryParams = buildQueryString({
    q: query,
    type,
    limit,
  });

  const data = await apiRequest(`${API_URL}/chat/search?${queryParams}`, {
    method: "GET",
    headers: {
      authorization: `Bearer ${accessToken}`,
    },
  });

  return data.data;
};

/**
 * Mark messages as read
 * @param {string} conversationId - Conversation ID
 * @param {Array<string>} messageIds - Message IDs to mark as read
 * @param {string} accessToken - Authentication token
 * @returns {Promise<Object>} Success response
 */
const markMessagesAsRead = async (conversationId, messageIds, accessToken) => {
  return await apiRequest(`${API_URL}/chat/messages/read`, {
    method: "POST",
    headers: {
      authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify({
      conversationId,
      messageIds,
    }),
  });
};

/**
 * Get unread message count
 * @param {string} userId - User ID
 * @param {string} accessToken - Authentication token
 * @returns {Promise<Object>} Unread count data
 */
const getUnreadCount = async (userId, accessToken) => {
  const data = await apiRequest(`${API_URL}/chat/unread-count/${userId}`, {
    method: "GET",
    headers: {
      authorization: `Bearer ${accessToken}`,
    },
  });

  return data.data;
};

export {
  // Core chat functions
  getMessages,
  getUserConversations,
  searchChats,
  markMessagesAsRead,
  getUnreadCount,

  // User management
  getUser,
  getUsers,

  // Auth functions (legacy)
  registerUser,
  loginUser,

  // Error class
  ChatApiError,

  // Utilities
  buildQueryString,
};
