import {
  decryptFile,
  decryptText,
  encryptFile,
  encryptText,
} from "@/utils/crypto";
import { getFileMeta } from "@/utils";

const getText = (text, size) =>
  text || (size > 0 ? `File${size > 1 ? "s" : ""} attached` : "");

export const decryptRecipientFile = async (receiver, fileMessage) => {
  const decrypted = await decryptFile(
    fileMessage.recipients[receiver.id],
    receiver.encryptedData.privateKey
  );

  const blob = new Blob([decrypted], { type: fileMessage.mimetype });

  const file = new File(
    [blob],
    `${fileMessage.name}.${fileMessage.extention}`,
    {
      type: fileMessage.mimetype,
      lastModified: Date.now(),
    }
  );

  return {
    file,
    size: fileMessage.size,
    name: fileMessage.name,
    mimetype: fileMessage.mimetype,
    extention: fileMessage.extention,
    ...getFileMeta(file),
  };
};

export const decryptMessage = async (currentUser, msg) => {
  if (!msg) return;

  const text = await decryptText(
    msg.recipients[currentUser.id],
    currentUser.encryptedData.privateKey
  );

  const files = [];

  for (const file of msg.files) {
    files.push(await decryptRecipientFile(currentUser, file));
  }

  return { text, files };
};

export const serializeToChatMessage = async (
  sender,
  receiver,
  opts = {
    files: undefined,
    message: undefined,
    conversationId: undefined,
  }
) => {
  const { message, files = [], conversationId } = opts;

  const encryptedFiles = [];

  for (const { file } of files) {
    const recipients = {};

    recipients[receiver.id] = await encryptFile(
      file,
      receiver.encryptedData.publicKey
    );

    recipients[sender.id] = await encryptFile(
      file,
      sender.encryptedData.publicKey
    );

    encryptedFiles.push({
      recipients,
      name: file.name,
      extention: file.name.split(".").pop(),
      mimetype: file.type,
      size: file.size,
    });
  }

  const receiverEncryptedMessage = message
    ? await encryptText(message, receiver.encryptedData.publicKey)
    : null;

  const senderEncryptedMessage = message
    ? await encryptText(message, sender.encryptedData.publicKey)
    : null;

  const tempId = new Date().getTime();

  const chatMessage = {
    conversationId,
    tempId,
    sender: {
      role: sender?.role,
      id: sender?.id,
      // frontend use only
      fullname: sender?.fullname,
      image: sender?.image,
    },
    receiver: {
      role: receiver?.role,
      id: receiver?.id,
      // frontend use only
      fullname: receiver?.fullname,
      image: receiver?.image,
    },
    files: encryptedFiles,
    recipients: {
      [sender?.id]: senderEncryptedMessage,
      [receiver?.id]: receiverEncryptedMessage,
    },
    text: message,
  };

  return chatMessage;
};
