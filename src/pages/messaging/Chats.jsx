import React, { useCallback, useEffect, useRef, useState } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import ChatRoom from "./components/ChatRoom";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useGetUserConversations } from "@/api/chat";
import { useGetUserProfile } from "@/api/user";
import useInfiniteScroll from "@/hooks/useInfiniteScroll";
import { decryptMessage } from "./utils";
import Loader from "@/components/loader/loader";
import { safelyBind } from "@/utils/event";
import { CHAT_CONVERSATION, CHAT_MESSAGE_DELIVERED } from "@/constants";
import useChatProvider from "@/hooks/useChatProvider";
import { BadgeCount } from "@/components/badge/badge";
import { cn } from "@/utils";
import PadlockIcon from "@/assets/svgs/Padlock";
import { Avatar } from "@/components/ui/avatar";
import { getTimeAgo, sortByTimestamp } from "@/utils/date";
import SearchInput from "@/components/inputs/SearchInput";

export const CONVERSATION_PARAM_KEY = "chat_cid";

const Chats = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [mutatedMessage, setMutatedMessage] = useState(null);

  const [chats, setChats] = useState(undefined);
  const [conversationSelected, setConversationSelected] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");

  const currentUser = useSelector((state) => state?.app?.userInfo?.user);

  const conversationId = searchParams.get(CONVERSATION_PARAM_KEY) || "";

  const receiverRole = searchParams.get("role") || undefined;

  const stateRef = useRef({
    ignoreChats: false,
    selected: !!userId,
    hasDeliveredInit: false,
  });

  const { socketIsConnected, socket } = useChatProvider();

  const { data: otherUser, isFetching: isFetchingUser } = useGetUserProfile(
    userId,
    receiverRole
  );

  const {
    data: conversations,
    isFetching: isFetchingConversations,
    isSearching,
    hasCacheData,
  } = useInfiniteScroll(useGetUserConversations(currentUser?.id, searchTerm));

  const normalizeToChat = useCallback(
    async (chat) => ({
      ...chat,
      unRead: chat.unRead || {},
      otherUser:
        chat.otherUser ||
        Object.values(chat.participants).filter(
          (u) => u.id !== currentUser.id
        )[0],
      ...(await decryptMessage(currentUser, chat.lastMessage)),
      sortDate: chat.lastMessage?.createdAt || chat.sortDate || undefined,
    }),
    [currentUser]
  );

  useEffect(() => {
    (async () => {
      if (stateRef.current.ignoreChats || !conversations) return;

      let chats = [];

      let conversationSelected = null;

      if (conversations)
        for (const chat of conversations) {
          chats.push(await normalizeToChat(chat));
        }

      if (conversationId)
        conversationSelected =
          chats.find((c) => c.id === conversationId) || null;

      if (!hasCacheData && !conversationSelected && otherUser) {
        if (!chats.find((chat) => !!chat.participants[otherUser.id])) {
          chats = [
            {
              participants: { [otherUser.id]: otherUser },
              otherUser,
              text: "Start a new conversation",
              sortDate: new Date().toISOString(),
              unRead: {},
              isDummy: true,
            },
            ...chats,
          ];

          conversationSelected = chats[0];
        }
      }

      setChats(chats);
      setConversationSelected(conversationSelected);
    })();
  }, [
    hasCacheData,
    currentUser,
    otherUser,
    conversations,
    normalizeToChat,
    setSearchParams,
    conversationId,
  ]);

  useEffect(() => {
    if (!socket) return;

    if (!stateRef.current.hasDeliveredInit && chats?.length) {
      for (const conversation of chats) {
        if (
          !conversation.isDummy &&
          conversation.lastMessage.sender.id !== currentUser.id &&
          !conversation.lastMessage.deliveredAt
        ) {
          socket.emit(
            CHAT_MESSAGE_DELIVERED,
            [conversation.lastMessage.id],
            conversation.otherUser.id
          );
        }
      }

      stateRef.current.hasDeliveredInit = true;
    }
  }, [chats?.length, currentUser?.id, otherUser?.id, socket]);

  const onConversationChange = useCallback(
    async (conversation) => {
      console.log(conversation);

      if (!currentUser?.id || !socket || !otherUser?.id) return;

      stateRef.current.ignoreChats = true;

      let newChats = [];

      let isBool = false;

      const newChat = await normalizeToChat(conversation);

      for (const chat of chats) {
        isBool =
          isBool ||
          chat.id === conversation.id ||
          chat.otherUser.id === newChat.otherUser.id;

        newChats.push(isBool ? newChat : chat);
      }

      if (!isBool) newChats = [newChat, ...newChats];

      if (stateRef.current.selected)
        setSearchParams(
          (params) => {
            params.set(CONVERSATION_PARAM_KEY, conversation.id);
            return params;
          },
          { replace: true }
        );

      setChats(sortByTimestamp(newChats));

      setMutatedMessage(conversation.lastMessage);

      if (
        conversation.lastMessage.sender.id !== currentUser.id &&
        !conversation.lastMessage.deliveredAt
      )
        socket.emit(
          CHAT_MESSAGE_DELIVERED,
          [conversation.lastMessage.id],
          otherUser.id
        );
    },
    [
      chats,
      currentUser?.id,
      normalizeToChat,
      setSearchParams,
      otherUser?.id,
      socket,
    ]
  );

  useEffect(() => {
    if (socket) {
      safelyBind(socket, CHAT_CONVERSATION, onConversationChange);
      return () => socket.off(CHAT_CONVERSATION, onConversationChange);
    }
  }, [socket, onConversationChange]);

  const handleChatClick = (chat) => {
    setConversationSelected(chat);

    navigate(
      `/${currentUser?.role}/messages/${
        chat.otherUser.id
      }?${CONVERSATION_PARAM_KEY}=${chat.id || ""}`
    );
  };

  const isFetching = isFetchingConversations || !socketIsConnected || !chats;

  if (isFetching) return <Loader fullscreen />;

  const withConversation = conversationId || !!conversationSelected;

  const hideChats = !hasCacheData && !chats.length;

  return (
    <div className="w-full h-full">
      <div className="w-full sm:flex h-full gap-5">
        <div
          className={cn(
            `
        w-full border rounded-md bg-gray-50 
        overflow-hidden sm:max-w-[40%]
          `,
            hideChats && "hidden"
          )}
        >
          <div className="h-full">
            <div
              className="
            w-full max-w-[90%] mx-auto
            my-[20px]
            "
            >
              <SearchInput onSearch={setSearchTerm} />
            </div>

            <div
              className="
            overflow-y-auto h-[calc(100%-100px)]
            "
            >
              {isSearching ? (
                <Loader fullscreen spinnerOnly />
              ) : chats?.length ? (
                chats.map((chat, i) => {
                  // if (!chat) return null;
                  return (
                    <div
                      key={i}
                      onClick={() => handleChatClick(chat)}
                      className={`p-3 cursor-pointer hover:bg-gray-100 flex items-center gap-3 border-b ${
                        chat.otherUser.id === otherUser?.id
                          ? "bg-[#EBEDF0]"
                          : "bg-white"
                      }`}
                    >
                      <Avatar
                        src={chat.otherUser.image}
                        size="md"
                        username={chat.otherUser.fullname}
                      />

                      <div className="w-full">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium truncate">
                              {chat.otherUser
                                ? chat.otherUser.fullname
                                : "Unknown User"}
                            </p>

                            <p className="">{chat.text}</p>
                          </div>

                          <div>
                            <div className="text-xs text-gray-500 whitespace-nowrap">
                              {getTimeAgo(chat.sortDate, currentUser.timezone)}
                            </div>

                            <BadgeCount count={chat.unRead[currentUser.id]} />
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <p
                  className="
              font-medium font-fig-tree text-[18px] 
              text-[#AAAAAA] text-center flex-1
              h-full flex items-center
              justify-center p-2
              "
                >
                  No chats found. Try starting a new one or refining your
                  search!
                </p>
              )}
            </div>
          </div>
        </div>

        <div
          className={cn("w-full sm:max-w-[60%]", hideChats && "sm:max-w-full")}
        >
          {withConversation ? (
            <ChatRoom
              mutatedMessage={mutatedMessage}
              loading={isFetchingUser}
              chatUnRead={conversationSelected?.unRead}
              key={conversationId}
              conversationId={conversationId}
              otherUser={otherUser}
            />
          ) : (
            <div
              className="
            flex flex-col items-center justify-center 
            h-full gap-2 w-full border rounded-md
            p-2 py-6
            "
            >
              <div
                className="
              flex-1 flex flex-col items-center justify-center 
              gap-2 w-full
              "
              >
                {chats?.length ? (
                  <>
                    <p className="font-bold text-[22px] text-[#1A1A40] font-fig-tree">
                      Start chatting
                    </p>

                    <p
                      className="
              font-medium font-fig-tree text-[18px] 
              text-[#AAAAAA]
              "
                    >
                      Select a chat and continue where you left off.
                    </p>
                  </>
                ) : (
                  <>
                    <p className="font-bold text-[22px] text-[#1A1A40] font-fig-tree">
                      Your inbox is empty for now
                    </p>

                    <p
                      className="
              font-medium font-fig-tree text-[18px] 
              text-[#AAAAAA]
              "
                    >
                      New message will appear here once someone reaches out.
                      Stay tuned!
                    </p>
                  </>
                )}
              </div>

              <div className="flex items-center gap-2">
                <PadlockIcon />
                <p
                  className="
                text-[#AAAAAA] font-fig-tree font-medium 
                text-[16px]
                "
                >
                  End-to-end encrypted
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Chats;
