import React, {
  useEffect,
  useRef,
  useState,
  useMemo,
  useCallback,
  useLayoutEffect,
} from "react";
import { FiSend, FiPaperclip, FiX, FiFile } from "react-icons/fi";
import { safelyBind } from "@/utils/event";
import { cn, downloadFile, removeDuplicate } from "@/utils";
import { decryptMessage } from "../utils";
import useInfiniteScroll from "@/hooks/useInfiniteScroll";
import { useGetConversationMessages } from "@/api/chat";
import Loader from "@/components/loader/loader";
import {
  AVATAR_SIZE,
  CHAT_CONVERSATION,
  CHAT_MESSAGE,
  CHAT_MESSAGE_DELIVERED,
  CHAT_MESSAGE_READ,
  CHAT_STOPPED_TYPING,
  CHAT_TYPING,
} from "@/constants";
import { useChatHook } from "@/hooks/useChatProvider";
import { Textarea } from "@/components/inputs/textInput";
import { But<PERSON> } from "@/components/button/button";
import { isValidFile } from "@/utils/validators";
import { toast } from "react-toastify";
import FileIcon from "@/assets/svgs/FileIcon";
import FilePreviewModal from "./FilePreviewModal";
import { ClockIcon } from "lucide-react";
import { useInView } from "react-intersection-observer";
import { Avatar } from "@/components/ui/avatar";
import { Link } from "react-router-dom";
import useLocalTime from "@/hooks/useLocalTime";
import { getLocalTime, getTimeAgo } from "@/utils/date";
import { Skeleton } from "@/components/ui/skeleton";

const ChatRoom = ({
  otherUser,
  conversationId,
  loading: isFetchingUser,
  mutatedMessage,
  chatUnRead,
}) => {
  const [chatMessage, setChatMessage] = useState("");
  const [chatMessages, setChatMessages] = useState(undefined);
  const [filePreviews, setFilePreviews] = useState([]);
  const [typing, setTyping] = useState(null);
  const [isComposing, setIsComposing] = useState(false);
  const [filePreview, setFilePreview] = useState(null);
  const [downloadState, setDownloadState] = useState({});

  const { fullTime } = useLocalTime(otherUser?.timezone);

  const stateRef = useRef({ isTyping: false, withMsgMutation: true });

  const fileInputRef = useRef(null);
  const textareaRef = useRef(null);
  const typingTimeoutRef = useRef(null);
  const scrollRef = useRef(null);

  const { socket, currentUser, sendChatMessage, loading } = useChatHook(
    useMemo(
      () => ({
        receiver: otherUser,
      }),
      [otherUser]
    )
  );

  const {
    data,
    isFetching: isFetchingMessages,
    viewRef,
    isRefetching,
    isPending,
  } = useInfiniteScroll(
    useGetConversationMessages(conversationId, currentUser?.id),
    { scrollRef }
  );

  const { ref: messagesEndRef, inView: isBottom } = useInView();

  // Group messages by date
  const groupedMessages = useMemo(() => {
    const groups = {};

    if (!chatMessages) return groups;

    chatMessages.forEach((message) => {
      // Normalize to YYYY-MM-DD
      const date = (
        message.createdAt ? new Date(message.createdAt) : new Date()
      )
        .toISOString()
        .split("T")[0];

      if (!groups[date]) {
        groups[date] = [];
      }

      groups[date].push(message);
    });

    return groups;
  }, [chatMessages]);

  // Auto-resize textarea based on content
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${Math.min(
        textareaRef.current.scrollHeight,
        120
      )}px`;
    }
  }, [chatMessage]);

  // Clean up object URLs and timeouts when component unmounts
  useEffect(() => {
    return () => {
      filePreviews.forEach((preview) => {
        URL.revokeObjectURL(preview.url);
      });
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [filePreviews]);

  useEffect(() => {
    (async () => {
      try {
        if (!currentUser || !data) return;

        const messages = [];

        for (const msg of data) {
          messages.push({
            ...msg,
            ...(await decryptMessage(currentUser, msg)),
          });
        }

        setChatMessages(messages);
      } catch (err) {
        toast.error("Something went wrong.");
      }
    })();
  }, [currentUser, conversationId, data]);

  if (!isFetchingMessages) stateRef.current.hasFetched = true;

  const isFetching = !stateRef.current.hasFetched || isFetchingUser;

  useLayoutEffect(() => {
    if (!currentUser?.id || !socket || !chatMessages || !otherUser?.id) return;

    console.log(stateRef.current.withMsgMutation, isBottom);

    if (stateRef.current.withMsgMutation && isBottom) {
      console.log("is bottom...");

      const unRead = [];

      const notDelivered = [];

      for (const { id, readAt, sender, deliveredAt } of chatMessages) {
        if (sender.id !== currentUser.id) {
          if (!readAt) unRead.push(id);

          if (!deliveredAt) notDelivered.push(id);
        }
      }

      console.log(unRead, notDelivered, chatMessages, "no unread");

      if (unRead.length) socket.emit(CHAT_MESSAGE_READ, unRead, otherUser.id);
      // else if (chatUnRead)
      //   socket.emit(CHAT_CONVERSATION, {
      //     id: conversationId,
      //     unRead: {
      //       ...chatUnRead,
      //       [currentUser.id]: 0,
      //     },
      //   });

      if (notDelivered.length)
        socket.emit(CHAT_MESSAGE_DELIVERED, notDelivered, otherUser.id);

      if (unRead.length) stateRef.current.withMsgMutation = false;
    }
  }, [
    chatMessages?.length,
    currentUser?.id,
    isBottom,
    otherUser?.id,
    socket,
    // chatUnRead,
    conversationId,
  ]);

  const onMessage = useCallback(
    async (msg, tempId) => {
      if (!currentUser?.id) return;

      if (
        msg.conversation.id === conversationId ||
        msg.conversation.participants[currentUser.id]
      ) {
        try {
          const decryptedMessage = await decryptMessage(currentUser, msg);

          const isSender = msg.sender.id === currentUser.id;

          setChatMessages((messages = []) => {
            let newMessages = [];
            const newMessage = { ...msg, ...decryptedMessage };

            if (isSender) {
              newMessages = messages.map((message) =>
                message.tempId === tempId ? newMessage : message
              );
            } else newMessages = [...messages, newMessage];

            return removeDuplicate(newMessages);
          });

          if (!isSender) stateRef.current.withMsgMutation = true;
        } catch (err) {
          console.error("Error processing new-chat-message:", err);
        }
      }
    },
    [conversationId, currentUser]
  );

  const onMessageChange = useCallback((message) => {
    console.log(message, " message chnage");
    setChatMessages((messages = []) =>
      messages.map((msg) =>
        msg.id === message.id ? { ...msg, ...message, files: msg.files } : msg
      )
    );
  }, []);

  const onTyping = useCallback((from) => {
    setTyping(from);
  }, []);

  const onStoppedTyping = useCallback(() => {
    setTyping(null);
  }, []);

  useEffect(() => {
    if (!socket) return;

    safelyBind(socket, CHAT_MESSAGE, onMessage);

    return () => {
      socket.off(CHAT_MESSAGE, onMessage);
    };
  }, [onMessage, socket]);

  useEffect(() => {
    if (mutatedMessage) {
      console.log(mutatedMessage);
      onMessageChange(mutatedMessage);
    }
  }, [mutatedMessage, onMessageChange]);

  useEffect(() => {
    if (!socket) return;

    safelyBind(socket, CHAT_MESSAGE_READ, onMessageChange);

    safelyBind(socket, CHAT_MESSAGE_DELIVERED, onMessageChange);

    safelyBind(socket, CHAT_TYPING, onTyping);

    safelyBind(socket, CHAT_STOPPED_TYPING, onStoppedTyping);

    return () => {
      socket.off(CHAT_MESSAGE_READ, onMessageChange);
      socket.off(CHAT_MESSAGE_DELIVERED, onMessageChange);
      socket.off(CHAT_TYPING, onTyping);
      socket.off(CHAT_STOPPED_TYPING, onStoppedTyping);
    };
  }, [onMessageChange, onStoppedTyping, onTyping, socket]);

  // Handle file selection and preview
  const handleFileSelect = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    const newPreviews = [];

    for (const file of files) {
      const { error, meta } = isValidFile(file);

      if (error) {
        toast.error(error);
        break;
      }

      newPreviews.push({
        file,
        url: meta.kind === "Image" ? URL.createObjectURL(file) : null,
        name: file.name,
        size: file.size,
        ...meta,
      });
    }

    setFilePreviews((prev) => [...prev, ...newPreviews]);
    e.target.value = ""; // Reset file input
  };

  const removeFilePreview = (index) => {
    URL.revokeObjectURL(filePreviews[index].url);
    setFilePreviews((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSendChatMessage = async () => {
    if (isPending || (!chatMessage && filePreviews.length === 0)) return;

    const message = await sendChatMessage({
      chat: {
        message: chatMessage,
        files: filePreviews,
        conversationId,
      },
    });

    if (!message) return;

    setChatMessages((messages = []) =>
      removeDuplicate([
        ...messages,
        {
          ...message,
          files: filePreviews,
        },
      ])
    );

    socket.emit("chat-user-stopped-typing", otherUser.id, currentUser);

    setChatMessage("");
    setFilePreviews([]);
  };

  // indicate typing
  const handleTyping = (e) => {
    // if (isFetching) return;

    // Update the message text
    setChatMessage(e.target.value);

    // Only emit typing events if we have a socket and other user
    if (!socket || !otherUser?.id) return;

    // Handle typing start
    if (!stateRef.current.isTyping && e.target.value.length > 0) {
      stateRef.current.isTyping = true;
      socket.emit("chat-user-typing", otherUser.id, currentUser);
    }

    // Clear any existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set a timeout to stop typing indication
    typingTimeoutRef.current = setTimeout(() => {
      socket.emit("chat-user-stopped-typing", otherUser.id, currentUser);
      stateRef.current.isTyping = false;
    }, 500);
  };

  // Add these new useEffect hooks for composition events
  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const handleCompositionStart = () => {
      setIsComposing(true);
      if (socket && otherUser?.id && !stateRef.current.isTyping) {
        stateRef.current.isTyping = true;
        socket.emit("chat-user-typing", otherUser.id, currentUser);
      }
    };

    const handleCompositionEnd = () => {
      // Don't immediately stop typing as user might continue typing
      setIsComposing(false);
    };

    textarea.addEventListener("compositionstart", handleCompositionStart);
    textarea.addEventListener("compositionend", handleCompositionEnd);

    return () => {
      textarea.removeEventListener("compositionstart", handleCompositionStart);
      textarea.removeEventListener("compositionend", handleCompositionEnd);
    };
  }, [socket, otherUser, currentUser]);

  const messageEntries = Object.entries(groupedMessages);

  return (
    <>
      <div className="w-full mx-auto border rounded-md bg-gray-50 h-full flex flex-col">
        <div
          className="
        border-b rounded-md bg-white p-4 flex items-center gap-3
        justify-between w-full rounded-b-none
        "
        >
          <div className="flex items-center gap-3 w-full">
            {isFetchingUser ? (
              <>
                <Skeleton className={cn(AVATAR_SIZE["md"])} />
                <div className="flex flex-col gap-2 flex-1 w-full max-w-[200px]">
                  <Skeleton />
                  <Skeleton />
                </div>
              </>
            ) : (
              <>
                <Avatar src={otherUser.image} username={otherUser.fullname} />

                <div>
                  <h3 className="text-[18px] font-medium">
                    {otherUser.fullname}
                  </h3>
                  <p
                    className={`text-sm ${
                      otherUser.isLoggedIn ? "text-[#54C68A]" : "text-[#1A1A40]"
                    }`}
                  >
                    {otherUser?.isLoggedIn
                      ? "Online"
                      : `last seen ${getTimeAgo(
                          otherUser.lastLoggedAt,
                          currentUser.timezone
                        )}`}
                  </p>
                </div>
              </>
            )}
          </div>

          {isFetchingUser ? (
            <Skeleton className="w-full max-w-[200px]" />
          ) : (
            <div className="font-normal text-[14px] font-fig-tree text-[#1a1a40]">
              <span className="capitalize font-medium text-[#000]">
                {otherUser.role}'s Time:
              </span>{" "}
              <span className="">{fullTime}</span>
            </div>
          )}
        </div>
        {/* Messages Container */}
        <div className="flex-1 flex flex-col p-4 h-full overflow-hidden">
          <div
            ref={scrollRef}
            className="
          flex-1 overflow-y-auto mb-4 p-2 bg-white border 
          rounded-md h-full
          "
          >
            <div className="space-y-4 h-full">
              {isFetching ? (
                <Loader spinnerOnly fullscreen size="sm" />
              ) : (
                <>
                  <div ref={viewRef} />
                  {isRefetching ? (
                    <Loader spinnerOnly size={"sm"} className="mx-auto my-1" />
                  ) : null}

                  {messageEntries.map(([date, messages]) => (
                    <div key={date} className="space-y-3 chat-group pb-6">
                      <div
                        className="
                      text-center text-xs text-gray-500 my-2
                      bg-[#EBEDF0] rounded-[4px] w-auto
                      h-[35px] p-[10px] max-w-fit mx-auto
                      "
                      >
                        <p
                          className="
                        font-fig-tree text-[14px] text-[#1A1A40]
                        "
                        >
                          {getTimeAgo(date, currentUser.timezone, {
                            formatToday: false,
                          })}
                        </p>
                      </div>
                      {messages.map((message) => {
                        const key = message.tempId || message.id;
                        const isSender = message.sender.id === currentUser.id;

                        return (
                          <div
                            key={key}
                            className={`scroll-target flex ${
                              message.sender.id === currentUser.id
                                ? "justify-end"
                                : "justify-start"
                            } mb-2`}
                          >
                            <div
                              className={cn(
                                `
                            w-auto max-w-[70%] flex gap-2 
                            items-start
                            `,
                                isSender && "flex-row-reverse"
                              )}
                            >
                              <Link to="# " className="inline-block">
                                <Avatar
                                  size="xs"
                                  src={message.sender.image}
                                  username={message.sender.fullname}
                                />
                              </Link>
                              <div
                                className="
                              w-full max-w-[calc(100%-30px)]
                              space-y-1
                              "
                              >
                                <div className="w-full">
                                  <p
                                    className={cn(
                                      `
                                  text-[#4B5563] font-fig-tree text-[14px]
                                  max-w-full break-words
                                  `,
                                      isSender && "text-right"
                                    )}
                                  >
                                    {isSender ? "You" : message.sender.fullname}
                                  </p>
                                </div>
                                <div
                                  className={`w-full p-3 rounded-lg ${
                                    message.sender.id === currentUser.id
                                      ? "bg-[#1FC16B1A] text-[#4B5563]"
                                      : "bg-[#EBEDF0] text-[#4B5563]"
                                  }`}
                                >
                                  {message.files?.length ? (
                                    <div className="space-y-12 mb-2">
                                      {message.files.map((file, index) => (
                                        <div key={index}>
                                          <div className="flex items-start gap-2 w-full">
                                            <FileIcon />

                                            <div className="space-y-1 max-w-full break-words">
                                              <p
                                                className="
          text-[#000] font-fig-tree text-[16px]
          max-w-full break-words
          "
                                              >
                                                {file.name}
                                              </p>

                                              <p
                                                className="
          text-[#4B5563] font-fig-tree font-normal 
          text-[14px] max-w-full break-words
          "
                                              >
                                                {file.sizeLabel}, {file.label}
                                              </p>
                                            </div>
                                          </div>

                                          <div className="border border-[#D2D2D2] my-4" />

                                          <div className="flex gap-2 items-center">
                                            {console.log(file)}
                                            {file.kind === "Image" ? (
                                              <Button
                                                className="flex-1 font-medium"
                                                onClick={() =>
                                                  setFilePreview(file)
                                                }
                                              >
                                                Preview
                                              </Button>
                                            ) : null}

                                            <Button
                                              loading={!!downloadState[key]}
                                              className="flex-1 font-medium"
                                              variant={
                                                file.kind === "Image"
                                                  ? "outline"
                                                  : undefined
                                              }
                                              onClick={() => {
                                                setDownloadState((state) => ({
                                                  ...state,
                                                  [key]: true,
                                                }));

                                                downloadFile(file.file, {
                                                  onSuccess: () => {
                                                    setDownloadState(
                                                      (state) => {
                                                        delete state[key];
                                                        return { ...state };
                                                      }
                                                    );
                                                  },
                                                });
                                              }}
                                            >
                                              Save
                                            </Button>
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  ) : null}

                                  {message.text ? (
                                    <p
                                      className={cn(
                                        "text-sm",
                                        message.files.length && "mt-4"
                                      )}
                                    >
                                      {message.text}
                                    </p>
                                  ) : null}

                                  <div className="flex justify-between items-center mt-2">
                                    <span className="text-xs opacity-80">
                                      {
                                        getLocalTime(
                                          currentUser.timezone,
                                          message.createdAt
                                        ).time
                                      }
                                    </span>
                                    {message.sender.id === currentUser.id && (
                                      <div className="flex items-center gap-1 ml-2">
                                        {message.readAt ||
                                        message.deliveredAt ? (
                                          <span
                                            className={cn(
                                              "text-xs",
                                              message.readAt && "text-[#0066FF]"
                                            )}
                                          >
                                            ✓✓
                                          </span>
                                        ) : message.createdAt ? (
                                          <span className="text-xs">✓</span>
                                        ) : (
                                          <span className="text-xs opacity-50">
                                            <ClockIcon size={16} />
                                          </span>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ))}
                </>
              )}
              <div ref={messagesEndRef} className="border border-white" />
            </div>
          </div>

          {/* Typing indicator */}
          {typing && (
            <p className="text-gray-500 italic text-sm mb-2">
              {typing.firstname} is typing...
            </p>
          )}

          {/* File previews before sending */}
          {filePreviews.length > 0 && (
            <div className="flex gap-2 mb-2 overflow-x-auto p-2 bg-gray-100 rounded">
              {filePreviews.map((preview, index) => (
                <div key={index} className="relative shrink-0">
                  {preview.kind === "Image" ? (
                    <img
                      src={preview.url}
                      alt="Preview"
                      className="h-20 w-20 object-cover rounded border"
                    />
                  ) : (
                    <div className="h-20 w-20 bg-gray-200 rounded border flex flex-col items-center justify-center p-2">
                      <FiFile size={24} className="text-gray-500" />
                      <span className="text-xs text-center truncate w-full">
                        {preview.name}
                      </span>
                    </div>
                  )}
                  <button
                    onClick={() => removeFilePreview(index)}
                    className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow hover:bg-gray-100"
                  >
                    <FiX size={14} className="text-gray-600" />
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Enhanced Message Input */}
          <div className="relative">
            {/* Hidden file input */}
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
              multiple
              accept="image/*, .pdf, .doc, .docx, .ppt, .pptx, .xls, .xlsx"
            />

            {/* Textarea with send button */}
            <div
              className={cn(
                `
          relative bg-white p-2 rounded-md border
              `
              )}
            >
              {/* <div className="absolute top-0 left-0 w-full h-full" /> */}
              <Textarea
                disabled={isFetching}
                ref={textareaRef}
                value={chatMessage}
                onChange={handleTyping}
                onCompositionStart={() => setIsComposing(true)}
                onCompositionEnd={() => setIsComposing(false)}
                placeholder="Type a message..."
                className="
                w-full rounded-md focus:outline-none 
                !bg-transparent
                "
                rows={2}
                onEnter={() => {
                  if (!isComposing) {
                    handleSendChatMessage();
                  }
                }}
              />

              <div className="flex justify-between items-end">
                <button
                  onClick={handleFileSelect}
                  className="
                  text-gray-500 hover:text-gray-700 p-1
                  disabled:text-gray-500 disabled:cursor-not-allowed
                  "
                  title="Attach file"
                  disabled={loading || isFetching}
                >
                  <FiPaperclip size={20} />
                </button>

                <Button
                  onClick={handleSendChatMessage}
                  disabled={
                    isFetching ||
                    (!chatMessage.trim() && filePreviews.length === 0)
                  }
                  loading={loading}
                  className={cn(
                    `
                  flex font-medium gap-1 items-center px-3 py-2 
                  rounded-md text-white bg-primary
                  disabled:text-gray-700 disabled:bg-gray-300
                  `,
                    loading && "disabled:bg-primary"
                  )}
                  title="Send message"
                >
                  Send
                  <FiSend size={15} />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <FilePreviewModal
        filePreview={filePreview}
        onClose={() => setFilePreview(null)}
      />
    </>
  );
};

export default ChatRoom;
