import React, { useState } from 'react';
import dayjs from 'dayjs';
import { 
  FiFile, 
  FiImage, 
  FiDownload, 
  FiMoreVertical, 
  FiReply, 
  FiCopy,
  FiTrash2,
  FiEdit3
} from 'react-icons/fi';

/**
 * Enhanced message bubble component with actions and file handling
 */
const MessageBubble = ({ 
  message, 
  isOwnMessage, 
  currentUser,
  onReply,
  onEdit,
  onDelete,
  onDownloadFile,
  className = ""
}) => {
  const [showActions, setShowActions] = useState(false);
  const [imageError, setImageError] = useState(false);

  const isPending = message.tempId && !message.id;

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Handle copy message text
  const handleCopyText = () => {
    if (message.text) {
      navigator.clipboard.writeText(message.text);
      setShowActions(false);
    }
  };

  // Handle file download
  const handleFileDownload = (file) => {
    if (onDownloadFile) {
      onDownloadFile(file, message);
    }
  };

  // Handle image load error
  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div 
      className={`flex ${isOwnMessage ? "justify-end" : "justify-start"} mb-2 group ${className}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div
        className={`max-w-[70%] p-3 rounded-lg relative ${
          isOwnMessage
            ? "bg-primary text-white"
            : "bg-gray-100 text-gray-900"
        } ${isPending ? "opacity-70" : ""}`}
      >
        {/* Reply indicator */}
        {message.replyTo && (
          <div className={`text-xs mb-2 p-2 rounded ${
            isOwnMessage ? "bg-black bg-opacity-20" : "bg-gray-200"
          }`}>
            <div className="font-medium">
              Replying to {message.replyTo.sender?.firstname || message.replyTo.sender?.firstName}
            </div>
            <div className="opacity-75 truncate">
              {message.replyTo.text?.substring(0, 50)}...
            </div>
          </div>
        )}

        {/* Message text */}
        {message.text && (
          <p className="text-sm mb-2 whitespace-pre-wrap break-words">
            {message.text}
          </p>
        )}

        {/* File attachments */}
        {message.files?.length > 0 && (
          <div className="flex gap-2 mb-2 flex-wrap">
            {message.files.map((file, index) => (
              <div key={index} className="max-w-[200px]">
                {file.type === "image" && file.preview && !imageError ? (
                  <div className="relative group/image">
                    <img
                      src={file.preview}
                      alt="Attachment"
                      className="max-h-40 rounded border cursor-pointer hover:opacity-90 transition-opacity"
                      onClick={() => {/* TODO: Open image viewer */}}
                      onError={handleImageError}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover/image:bg-opacity-20 rounded transition-all duration-200 flex items-center justify-center">
                      <FiImage className="text-white opacity-0 group-hover/image:opacity-100 transition-opacity" size={24} />
                    </div>
                    {/* Download button for images */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleFileDownload(file);
                      }}
                      className="absolute top-2 right-2 p-1 bg-black bg-opacity-50 text-white rounded-full opacity-0 group-hover/image:opacity-100 transition-opacity hover:bg-opacity-70"
                      title="Download image"
                    >
                      <FiDownload size={12} />
                    </button>
                  </div>
                ) : (
                  <div 
                    className={`flex items-center gap-2 p-2 rounded border cursor-pointer transition-colors ${
                      isOwnMessage 
                        ? "bg-white bg-opacity-20 hover:bg-opacity-30" 
                        : "bg-white hover:bg-gray-50"
                    }`}
                    onClick={() => handleFileDownload(file)}
                  >
                    <FiFile className="text-current flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="text-xs truncate font-medium">
                        {file.name}
                      </div>
                      <div className="text-xs opacity-75">
                        {formatFileSize(file.size)}
                      </div>
                    </div>
                    <FiDownload className="text-current flex-shrink-0" size={14} />
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Message footer */}
        <div className="flex justify-between items-center mt-1">
          <span className="text-xs opacity-75">
            {dayjs(message.createdAt).format("h:mm A")}
          </span>
          
          {/* Message status for own messages */}
          {isOwnMessage && (
            <div className="flex items-center gap-1 ml-2">
              {isPending ? (
                <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin"></div>
              ) : message.readAt ? (
                <span className="text-xs opacity-75" title="Read">✓✓</span>
              ) : message.deliveredAt ? (
                <span className="text-xs opacity-75" title="Delivered">✓</span>
              ) : (
                <span className="text-xs opacity-75" title="Sent">⏳</span>
              )}
            </div>
          )}
        </div>

        {/* Message actions (hover) */}
        {showActions && !isPending && (
          <div className={`absolute top-1/2 transform -translate-y-1/2 ${
            isOwnMessage ? '-left-2' : '-right-2'
          }`}>
            <div className="relative">
              <button
                className="p-1 bg-white shadow-md rounded-full text-gray-600 hover:text-gray-800 transition-colors"
                onClick={() => setShowActions(!showActions)}
              >
                <FiMoreVertical size={14} />
              </button>
              
              {/* Actions dropdown */}
              <div className={`absolute top-0 ${
                isOwnMessage ? 'right-full mr-2' : 'left-full ml-2'
              } bg-white shadow-lg rounded-md border py-1 z-10 min-w-[120px]`}>
                
                {/* Reply */}
                <button
                  onClick={() => {
                    onReply?.(message);
                    setShowActions(false);
                  }}
                  className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                >
                  <FiReply size={14} />
                  Reply
                </button>

                {/* Copy text */}
                {message.text && (
                  <button
                    onClick={handleCopyText}
                    className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                  >
                    <FiCopy size={14} />
                    Copy
                  </button>
                )}

                {/* Edit (own messages only) */}
                {isOwnMessage && message.text && (
                  <button
                    onClick={() => {
                      onEdit?.(message);
                      setShowActions(false);
                    }}
                    className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                  >
                    <FiEdit3 size={14} />
                    Edit
                  </button>
                )}

                {/* Delete (own messages only) */}
                {isOwnMessage && (
                  <button
                    onClick={() => {
                      onDelete?.(message);
                      setShowActions(false);
                    }}
                    className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                  >
                    <FiTrash2 size={14} />
                    Delete
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageBubble;
