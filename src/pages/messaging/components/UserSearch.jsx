import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON>ch, FiUser, FiX, FiMessageCircle } from 'react-icons/fi';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useGetTutorsQuery } from '@/redux/slices/student/findTutorApiSlice';
import userVector from '@/assets/svgs/userVector.svg';

/**
 * User search component for finding tutors to start conversations with
 */
const UserSearch = ({ 
  onClose,
  className = "",
  placeholder = "Search for tutors to message..."
}) => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const searchInputRef = useRef(null);
  const resultsRef = useRef(null);
  const navigate = useNavigate();
  
  // Redux state
  const currentUser = useSelector(state => state.app?.userInfo?.user);
  
  // Fetch tutors
  const { data: tutorsResponse, isLoading } = useGetTutorsQuery();
  const tutors = tutorsResponse?.tutors || [];

  // Filter tutors based on search query
  const filteredTutors = tutors.filter(tutor => {
    if (!query.trim()) return false;
    
    const searchTerm = query.toLowerCase();
    const fullName = `${tutor.firstname || tutor.firstName || ''} ${tutor.lastname || tutor.lastName || ''}`.toLowerCase();
    const email = (tutor.email || '').toLowerCase();
    const subjects = (tutor.subjects || []).join(' ').toLowerCase();
    
    return fullName.includes(searchTerm) || 
           email.includes(searchTerm) || 
           subjects.includes(searchTerm);
  }).slice(0, 10); // Limit to 10 results

  // Handle input change
  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);
    setIsOpen(value.trim().length >= 2);
  };

  // Handle clear search
  const handleClear = () => {
    setQuery('');
    setIsOpen(false);
    searchInputRef.current?.focus();
  };

  // Handle tutor selection
  const handleTutorSelect = (tutor) => {
    if (!currentUser || !tutor) return;
    
    // Navigate to messaging page with the selected tutor
    navigate(`/${currentUser.role}/messages/${tutor.id}`);
    
    // Close search
    handleClear();
    if (onClose) onClose();
  };

  // Close on escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        handleClear();
        if (onClose) onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  // Focus input on mount
  useEffect(() => {
    searchInputRef.current?.focus();
  }, []);

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <FiSearch className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          ref={searchInputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          placeholder={placeholder}
          className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
        />
        
        {query && (
          <button
            onClick={handleClear}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <FiX className="h-5 w-5 text-gray-400 hover:text-gray-600" />
          </button>
        )}
      </div>

      {/* Search Results */}
      {isOpen && (
        <div
          ref={resultsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-hidden"
        >
          {isLoading ? (
            <div className="p-4 text-center text-gray-500">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2">Searching...</p>
            </div>
          ) : filteredTutors.length > 0 ? (
            <div className="py-2">
              <div className="px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide border-b">
                Tutors ({filteredTutors.length})
              </div>
              <div className="max-h-80 overflow-y-auto">
                {filteredTutors.map((tutor) => (
                  <button
                    key={tutor.id}
                    onClick={() => handleTutorSelect(tutor)}
                    className="w-full px-3 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <img
                        src={tutor.image || tutor.profilePicture || userVector}
                        alt={`${tutor.firstname || tutor.firstName} ${tutor.lastname || tutor.lastName}`}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {tutor.firstname || tutor.firstName} {tutor.lastname || tutor.lastName}
                          </p>
                          <FiMessageCircle className="h-4 w-4 text-gray-400" />
                        </div>
                        <p className="text-xs text-gray-500 truncate">
                          {tutor.email}
                        </p>
                        {tutor.subjects && tutor.subjects.length > 0 && (
                          <p className="text-xs text-gray-400 truncate">
                            {tutor.subjects.slice(0, 3).join(', ')}
                            {tutor.subjects.length > 3 && '...'}
                          </p>
                        )}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ) : query.trim().length >= 2 ? (
            <div className="p-4 text-center text-gray-500">
              <FiUser className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p>No tutors found for "{query}"</p>
              <p className="text-xs mt-1">Try searching by name, email, or subject</p>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default UserSearch;
