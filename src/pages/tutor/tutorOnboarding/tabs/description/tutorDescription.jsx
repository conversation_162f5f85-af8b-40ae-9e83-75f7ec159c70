import React, { useEffect } from "react";
import { But<PERSON> } from "@/components/button/button";
import { useSelector } from "react-redux";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import { useForm } from "react-hook-form";
import usePost from "@/hooks/usePost";
import Loader from "@/components/loader/loader";

const TutorDescription = ({ setActiveTab, instructorDetails }) => {
	const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);

	const {
		register,
		control,
		handleSubmit,
		setValue,
		formState: { errors },
	} = useForm();

	const { handlePost: handleUpdateDescription, isLoading: updating } = usePost(
		useUpdateProfileMutation
	);

	const updateDescription = async (data) => {
		console.log(data);
		const res = await handleUpdateDescription({
			...data,
			userId: tutorId,
			role: "tutor",
		});

		if (res) {
			setActiveTab("Video");
		}
	};

	useEffect(() => {
		if (instructorDetails) {
			setValue("aboutMe", instructorDetails?.aboutMe);
			setValue("teachingExperience", instructorDetails?.teachingExperience);
			setValue(
				"motivatePotentialStudent",
				instructorDetails?.motivatePotentialStudent
			);
			setValue("headline", instructorDetails?.headline);
		}
	}, [instructorDetails]);

	return (
		<>
			{updating && <Loader />}

			<form
				className="max-w-[828px] w-[93%] mx-auto"
				onSubmit={handleSubmit(updateDescription)}
			>
				<h2 className="sm:text-4xl text-3xl font-bold mb-5 sm:mt-8 lg:mt-0">
					Profile description
				</h2>
				<p className="text-[#4B5563] sm:text-lg sm:mb-10 mb-6">
					This info will go on your public profile. Not more than 400 words
					each.
				</p>

				<div>
					<h3 className="sm:text-2xl text-xl font-bold mb-3">
						1. Introduction about yourself
					</h3>
					<p className="text-[#4B5563] sm:text-lg mb-5">
						Show potential students who you are! Share your teaching experience
						and passion for education and briefly mention your interests and
						hobbies.
					</p>

					<textarea
						name=""
						id=""
						rows={5}
						className="border appearance-none resize-none outline-none rounded-lg w-full sm:p-5 p-3 mb-7"
						placeholder="Eg My name is...."
						{...register("aboutMe")}
					></textarea>
				</div>

				<div>
					<h3 className="sm:text-2xl text-xl font-bold mb-3">
						2. Teaching experience
					</h3>
					<p className="text-[#4B5563] sm:text-lg mb-5">
						Provide a detailed description of your relevant teaching experience.
						Include certifications, teaching methodology, education, and subject
						expertise.
					</p>

					<textarea
						name=""
						id=""
						rows={5}
						className="border appearance-none resize-none outline-none rounded-lg w-full sm:p-5 p-3 mb-7"
						placeholder="Eg I have 3 years teaching experience....."
						{...register("teachingExperience")}
					></textarea>
				</div>

				<div>
					<h3 className="sm:text-2xl text-xl font-bold mb-3">
						3. Motivate potential student
					</h3>
					<p className="text-[#4B5563] sm:text-lg mb-5">
						Encourage students to book their first lesson. Highlight the
						benefits of learning with you!
					</p>

					<textarea
						name=""
						id=""
						rows={5}
						className="border appearance-none resize-none outline-none rounded-lg w-full sm:p-5 p-3 mb-7"
						{...register("motivatePotentialStudent")}
						placeholder="Eg Book a trail lesson with me so we can discuss your goals and how i can help your acheive them"
					></textarea>
				</div>

				<div>
					<h3 className="sm:text-2xl text-xl font-bold mb-3">
						4. Write a catchy headline
					</h3>
					<p className="text-[#4B5563] sm:text-lg mb-5">
						Your headline is the first thing students see about you. Make it
						attention-grabbing, mention your specific teaching language and
						encourage students to read your full description.
					</p>

					<textarea
						name=""
						id=""
						rows={5}
						className="border appearance-none resize-none outline-none rounded-lg w-full sm:p-5 p-3 mb-10"
						placeholder="Eg Certified tutor with 5 years of exprience"
						{...register("headline")}
					></textarea>
				</div>

				<div className="sm:flex gap-5">
					<Button
						className="w-full h-[50px] bg-white border-[#D2D2D2] text-secondary border hover:bg-white"
						onClick={() => setActiveTab("Education")}
					>
						Back
					</Button>

					<Button
						className="w-full mt-4 sm:mt-0 h-[50px]"
						disabled={updating}
						type="submit"
					>
						Save And Continue
					</Button>
				</div>
			</form>
		</>
	);
};

export default TutorDescription;
