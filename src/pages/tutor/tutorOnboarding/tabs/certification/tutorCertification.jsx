import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/button/button";
import { CustomSelect } from "@/components/select/select";
import InputField from "@/components/inputs";
import { useForm, useFieldArray } from "react-hook-form";
import uploadIcon from "@/assets/svgs/uploadIcon.svg";
import CheckboxInput from "@/components/inputs/checkboxInput";
import { Trash2 } from "lucide-react";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import { convertToBase64 } from "@/utils";
import { useSelector } from "react-redux";
import Loader from "@/components/loader/loader";

const TutorCertification = ({ setActiveTab, instructorDetails }) => {
	const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);

	const {
		register,
		control,
		handleSubmit,
		setValue,
		formState: { errors },
	} = useForm({
		defaultValues: {
			subject: "English",
			certificates: [
				{
					title: "",
					startDate: "",
					endDate: "",
					organization: "",
					name: "",
					fileUpload: "",
				},
			],
		},
	});

	const { fields, append, remove } = useFieldArray({
		control,
		name: "certificates",
	});

	// Track custom certificate toggle and uploaded files for each certificate
	const [customCerts, setCustomCerts] = useState([]);
	const [uploadedFiles, setUploadedFiles] = useState({});

	// Generate years from 1970 to current year (2025)
	const currentYear = new Date().getFullYear();
	const yearOptions = Array.from({ length: currentYear - 1960 + 1 }, (_, i) => {
		const year = currentYear - i;
		return { value: year.toString(), label: year.toString() };
	});

	// Initialize form and states with instructorDetails.certificates
	useEffect(() => {
		if (instructorDetails?.certificates?.length > 0) {
			const formattedCertificates = instructorDetails.certificates.map(
				(cert) => ({
					title: cert.title || "",
					startDate: cert.startDate ? cert.startDate.slice(0, 4) : "",
					endDate: cert.endDate ? cert.endDate.slice(0, 4) : "",
					organization: cert.organization || "",
					name: cert.name || "",
					fileUpload: cert.file
						? {
								name: cert.file.url.split("/").pop() || "certificate",
								base64: cert.file.url || "",
						  }
						: "",
				})
			);

			setValue("certificates", formattedCertificates);

			const initialCustomCerts = instructorDetails.certificates.map((cert) =>
				cert.organization || cert.name ? true : false
			);
			setCustomCerts(initialCustomCerts);

			const initialUploadedFiles = instructorDetails.certificates.reduce(
				(acc, cert, index) => {
					if (cert.file) {
						acc[index] = {
							name: cert.file.url.split("/").pop() || "certificate",
							base64: cert.file.url,
						};
					}
					return acc;
				},
				{}
			);
			setUploadedFiles(initialUploadedFiles);
		} else {
			setCustomCerts([false]);
		}
	}, [instructorDetails, setValue]);

	const handleAddCertificate = () => {
		append({
			title: "",
			startDate: "",
			endDate: "",
			organization: "",
			name: "",
			fileUpload: "",
		});
		setCustomCerts([...customCerts, false]);
	};

	const toggleCustomCert = (index) => {
		const updated = [...customCerts];
		updated[index] = !updated[index];
		setCustomCerts(updated);
		if (!updated[index]) {
			setValue(`certificates[${index}].organization`, "");
			setValue(`certificates[${index}].name`, "");
		}
	};

	const { handlePost: handleUpdateTutor, isLoading: updating } = usePost(
		useUpdateProfileMutation
	);

	const handleFileChange = async (e, index) => {
		const file = e.target.files[0];
		if (file) {
			const base64 = await convertToBase64(file);
			setUploadedFiles((prev) => ({
				...prev,
				[index]: { name: file.name, base64 },
			}));
			setValue(`certificates[${index}].fileUpload`, {
				name: file.name,
				base64,
			});
		}
	};

	const updateCertification = async (data) => {
		const formattedCerts = data.certificates.map((cert) => ({
			title: cert.title || cert.name || "",
			organization: cert.organization || "",
			startDate: cert.startDate ? `${cert.startDate}-01-01T00:00:00.000Z` : "",
			endDate: cert.endDate ? `${cert.endDate}-01-01T00:00:00.000Z` : "",
			fileUpload: cert.fileUpload?.base64 || "",
			subject: instructorDetails?.teachingSubjects[0]?.title || "English",
		}));

		const res = await handleUpdateTutor({
			certificates: formattedCerts,
			userId: tutorId,
			role: "tutor",
		});

		if (res) {
			setActiveTab("Education");
		}
	};

	return (
		<>
			{updating && <Loader />}

			<form
				className="max-w-[528px] w-[93%] mx-auto"
				onSubmit={handleSubmit(updateCertification)}
			>
				<h2 className="sm:text-4xl text-3xl font-bold mb-5 sm:mt-8 lg:mt-0">
					Certification
				</h2>
				<p className="text-[#4B5563] sm:text-lg mb-8">
					Do you have teaching certificates? If so, describe them to enhance
					your profile credibility and get more students
				</p>

				<InputField
					register={register}
					fieldName="subject"
					placeHolder="Enter subject"
					defaultValue="English"
					label="Subject"
					disabled={true}
					isRequired={true}
				/>

				{fields?.map((cert, index) => (
					<div key={cert.id}>
						{index > 0 && (
							<button
								type="button"
								onClick={() => {
									remove(index);
									setCustomCerts((prev) => prev.filter((_, i) => i !== index));
									setUploadedFiles((prev) => {
										const updated = { ...prev };
										delete updated[index];
										return updated;
									});
								}}
								className="p-1 rounded-full h-8 w-8 mt-auto mb-[10px] ml-auto flex justify-center items-center shrink-0 bg-red-100 hover:bg-red-200"
								title="Remove certificate"
							>
								<Trash2 size={16} className="text-red-500" />
							</button>
						)}

						{!customCerts[index] ? (
							<CustomSelect
								placeholder="Select verified certification"
								label="Certification"
								options={[
									{ value: "TESOL", label: "TESOL" },
									{ value: "TEFL", label: "TEFL" },
									{ value: "TESL", label: "TESL" },
									{ value: "CELTA", label: "CELTA" },
									{ value: "DELTA", label: "DELTA" },
								]}
								name={`certificates.${index}.title`}
								control={control}
								isRequired={true}
								error={errors?.certificates?.[index]?.title?.message}
								className="p-5 py-[22px]"
								parentClassName="mb-2"
							/>
						) : (
							<div>
								<InputField
									register={register}
									fieldName={`certificates.${index}.organization`}
									label="Issued Organization"
									placeHolder="Enter organization name"
									isRequired={customCerts[index]}
									error={errors?.certificates?.[index]?.organization?.message}
								/>
								<InputField
									register={register}
									fieldName={`certificates.${index}.name`}
									label="Certification Name"
									placeHolder="Enter certification name"
									isRequired={customCerts[index]}
									error={errors?.certificates?.[index]?.name?.message}
								/>
							</div>
						)}

						<label
							className={`flex items-center gap-2 mb-5 ${
								customCerts[index] && "mt-[-15px]"
							} text-sm sm:text-base text-[#4B5563]`}
						>
							<input
								type="checkbox"
								checked={customCerts[index]}
								onChange={() => toggleCustomCert(index)}
								className="custom-checkbox text-sm border border-[#E8E8E8] bg-white rounded-xl focus:outline-none"
							/>
							My certificate is not on the list
						</label>

						<div className="flex sm:gap-5 gap-2">
							<CustomSelect
								placeholder="Select year"
								label="Start year"
								options={yearOptions}
								name={`certificates.${index}.startDate`}
								control={control}
								isRequired={true}
								error={errors?.certificates?.[index]?.startDate?.message}
								className="p-5 py-[22px]"
								parentClassName="mb-7"
							/>

							<CustomSelect
								placeholder="Select year"
								label="End year"
								options={yearOptions}
								name={`certificates.${index}.endDate`}
								control={control}
								isRequired={true}
								error={errors?.certificates?.[index]?.endDate?.message}
								className="p-5 py-[22px]"
								parentClassName="mb-7"
							/>
						</div>

						<p className="text-secondary mb-3 max-sm:text-sm">
							Upload your certificate here
						</p>

						<label
							className="w-full border border-dashed border-[#D2D2D2] sm:p-5 p-3 flex flex-col items-center rounded-lg cursor-pointer mb-7"
							htmlFor={`upload-cert-${index}`}
						>
							<img src={uploadIcon} alt="upload icon" />
							<p className="text-[#4B5563] sm:text-lg mb-2">
								Choose a file or drag and drop it here
							</p>
							<p className="text-[#4B5563] sm:text-lg mb-5">
								Jpeg, Png, Pdf Max 5mb
							</p>

							<span className="text-primary font-bold sm:text-xl text-lg">
								Browse
							</span>

							<input
								type="file"
								id={`upload-cert-${index}`}
								className="hidden"
								aria-label="upload a certificate"
								onChange={(e) => handleFileChange(e, index)}
								accept="image/jpeg,image/png,application/pdf"
							/>
						</label>

						{uploadedFiles[index] && (
							<div className="flex items-center gap-2 mb-6 text-[#4B5563] text-sm mt-[-14px]">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									strokeWidth={1.5}
									stroke="currentColor"
									className="w-5 h-5 text-primary"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										d="M19.5 14.25v3.375A2.625 2.625 0 0116.875 20.25H7.125A2.625 2.625 0 014.5 17.625V6.375A2.625 2.625 0 017.125 3.75h5.25L19.5 9v2.25"
									/>
								</svg>
								<span>{uploadedFiles[index].name}</span>
							</div>
						)}

						{index === fields.length - 1 && (
							<p
								onClick={handleAddCertificate}
								className="text-sm mt-[-20px] font-medium text-secondary underline mb-10 cursor-pointer"
							>
								Add another certificate
							</p>
						)}
					</div>
				))}

				<div className="sm:flex gap-5">
					<Button
						className="w-full h-[50px] bg-white border-[#D2D2D2] text-secondary border hover:bg-white"
						onClick={() => setActiveTab("Proficiency")}
					>
						Back
					</Button>

					<Button
						className="w-full mt-4 sm:mt-0 h-[50px]"
						disabled={updating}
						type="submit"
					>
						Save And Continue
					</Button>
				</div>
			</form>
		</>
	);
};

export default TutorCertification;
