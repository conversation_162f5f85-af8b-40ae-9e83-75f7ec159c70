import React, { useEffect } from "react";
import { But<PERSON> } from "@/components/button/button";
import { CustomSelect } from "@/components/select/select";
import MultiSelect from "@/components/select/multiSelect";
import { useFieldArray, useForm } from "react-hook-form";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import { useSelector } from "react-redux";
import Loader from "@/components/loader/loader";
import { toast } from "react-toastify";
import {
	experienceOptions,
	qualitiesOptions,
	specialititesOptions,
} from "../../onboardingUtils";

const TutorProficiency = ({ setActiveTab, instructorDetails }) => {
	const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);

	const {
		register,
		control,
		handleSubmit,
		setValue,
		watch,
		formState: { errors, isValid },
	} = useForm({
		mode: "onChange",
		defaultValues: {
			teachingSubjects: [{ title: "English" }],
			experienceLevel: "",
			qualities: [],
			specialities: [],
		},
	});

	const { handlePost: handleUpdateTutor, isLoading: updating } = usePost(
		useUpdateProfileMutation
	);

	// Watch the experienceLevel to handle validation
	const experienceLevel = watch("experienceLevel");

	useEffect(() => {
		const subject = instructorDetails?.teachingSubjects?.[0];

		if (subject) {
			// Set the experienceLevel only if it exists and hasn't been set yet
			if (subject.experienceLevel && !experienceLevel) {
				setValue("experienceLevel", subject.experienceLevel);
			}

			// Set other fields
			setValue("qualities", subject.qualities || []);
			setValue("specialities", subject.specialities || []);
		}
	}, [instructorDetails, setValue, experienceLevel]);

	const updateProficiency = async (data) => {
		const res = await handleUpdateTutor({
			teachingSubjects: [
				{
					title: "English",
					experienceLevel: data.experienceLevel,
					qualities: data.qualities,
					specialities: data.specialities,
				},
			],
			userId: tutorId,
			role: "tutor",
		});

		if (res) {
			setActiveTab("Certification");
		}
	};

	const onSubmit = handleSubmit(updateProficiency);

	return (
		<>
			{updating && <Loader />}

			<form onSubmit={onSubmit} className="max-w-[528px] w-[93%] mx-auto">
				<h2 className="sm:text-4xl text-3xl font-bold mb-5 sm:mt-8 lg:mt-0">
					English Proficiency
				</h2>
				<p className="text-[#4B5563] sm:text-lg mb-8">
					Select your English teaching proficiency level. You can select
					multiple options in each category.
				</p>

				<div className="mb-6">
					<label className="block text-sm font-medium text-gray-700 mb-1">
						Subject
					</label>
					<div className="p-2 border border-gray-200 rounded-md ">English</div>
				</div>

				<CustomSelect
					placeholder="Select your experience level"
					label="English Teaching Experience"
					options={experienceOptions}
					className="p-5 py-[22px]"
					parentClassName="mb-7"
					name="experienceLevel"
					control={control}
					isRequired={true}
					error={errors?.experienceLevel?.message}
				/>

				<MultiSelect
					options={qualitiesOptions}
					placeholder="Select your teaching qualities"
					label="Teaching Qualities"
					control={control}
					name="qualities"
					parentClassName="mb-7"
				/>

				<MultiSelect
					options={specialititesOptions}
					placeholder="Select your teaching specialities"
					label="Teaching Specialities"
					control={control}
					name="specialities"
					parentClassName="mb-10"
				/>

				<div className="sm:flex gap-5">
					<Button
						className="w-full h-[50px] mb-2 bg-white border-[#D2D2D2] text-secondary border hover:bg-white"
						onClick={() => setActiveTab("Photo")}
					>
						Back
					</Button>

					<Button
						className="w-full h-[50px]"
						disabled={updating || !experienceLevel}
						type="submit"
					>
						Save And Continue
					</Button>
				</div>
			</form>
		</>
	);
};

export default TutorProficiency;
