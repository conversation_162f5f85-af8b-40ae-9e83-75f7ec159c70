import React, { useState, useEffect } from "react";
import dollarSign from "@/assets/svgs/dollarSign.svg";
import greenTick from "@/assets/svgs/greenTick.svg";
import { Button } from "@/components/button/button";
import { ChevronDownIcon } from "lucide-react";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import { useSelector } from "react-redux";
import { useForm } from "react-hook-form";
import SuccessModal from "@/components/modal/successModal";
import Loader from "@/components/loader/loader";

const TutorPrice = ({ instructorDetails, setActiveTab }) => {
	const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);

	const {
		register,
		control,
		handleSubmit,
		setValue,
		formState: { errors },
	} = useForm();

	const [isCommissionOpen, setIsCommissionOpen] = useState(false);
	const [showSuccess, setShowSuccess] = useState(false);

	const toggleCommission = () => {
		setIsCommissionOpen((prev) => !prev);
	};

	// use the generic post mutation hook to handle the login mutation
	const { handlePost: handleUpdateTutor, isLoading: updating } = usePost(
		useUpdateProfileMutation
	);

	const updatePrice = async (data) => {
		console.log(data);
		const res = await handleUpdateTutor({
			...data,
			userId: tutorId,
			role: "tutor",
		});

		if (res) {
			// setActiveTab("Photo");
			setShowSuccess(true);
		}
	};
	useEffect(() => {
		if (instructorDetails) {
			setValue("basePrice", instructorDetails?.basePrice);
		}
	}, [instructorDetails, setValue]);

	console.log(showSuccess);

	return (
		<>
			{updating && <Loader />}

			<SuccessModal
				isOpen={showSuccess}
				onClose={() => setShowSuccess(false)}
				title="Successfully submitted"
				message="Within 5 business days, you will receive an email with your application status update."
				onButtonClick={() => {
					setActiveTab("Complete");
					setShowSuccess(false);
				}}
				buttonText="Continue"
			/>

			<form
				className="max-w-[573px] w-[93%] mx-auto"
				onSubmit={handleSubmit(updatePrice)}
			>
				<h2 className="sm:text-4xl text-3xl font-bold mb-5 sm:mt-8 lg:mt-0">
					Set your 50 minute lesson price
				</h2>
				<p className="text-[#4B5563] sm:text-lg mb-8">
					Starting with a competitive price can help attract more students. Once
					you've aced your first few trial lessons, feel free to adjust this
					price to meet your goals.
				</p>

				<p className="text-[#4B5563] sm:text-lg mb-4">
					To help you get started, we recommend you to set your initial lesson
					price to $5
				</p>

				<div className="relative w-full mb-7">
					<input
						type="number"
						className="w-full border border-[#E8E8E8] rounded-md py-2 pl-4 pr-10 outline-none appearance-none [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
						placeholder="5"
						{...register("basePrice")}
					/>

					<img
						src={dollarSign}
						alt="dollar sign"
						className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm pointer-events-none"
					/>
				</div>

				<div className="bg-[#1FC16B1A] sm:p-5 p-3 rounded-lg mb-5">
					<p className="sm:text-lg text-black font-light">
						Tutors who follow our price recommendation have a 40% higher chance
						of teaching their first trial lesson within a week of approval.
					</p>
				</div>

				<button
					onClick={toggleCommission}
					type="button"
					className="w-full text-left mb-3 sm:text-xl text-lg font-bold flex justify-between items-center focus:outline-none"
				>
					Convolly commission
					{!isCommissionOpen ? (
						<ChevronDownIcon className="w-5 h-5" />
					) : (
						<ChevronDownIcon className="w-5 h-5 rotate-180" />
					)}
				</button>

				<div
					className={`transition-all duration-500 ease-in-out overflow-hidden ${
						isCommissionOpen
							? "max-h-[1000px] opacity-100"
							: "max-h-0 opacity-0"
					}`}
				>
					<p className="text-[#4B5563] sm:text-lg mb-4">
						We use the funds for getting more students and for constant
						improvements of our learning platform
					</p>

					{[
						"For every trial lesson with a new student Convolly's commission is 100%",
						"For all the subsequent lessons, Convolly charges a percentage (18%-33%) of the hourly rate",
						"The more hours you teach, the lower your rate of commission will be",
					].map((text, idx) => (
						<div key={idx} className="flex gap-3 items-center mb-3">
							<img src={greenTick} alt="green tick" />
							<p className="text-[#4B5563] sm:text-lg">{text}</p>
						</div>
					))}

					<div className="bg-[#F5F5F5] sm:p-5 p-3 rounded-lg mb-5">
						<div className="flex justify-between items-center mb-2">
							<p className="text-black sm:text-lg">Completed hours</p>
							<p className="text-black sm:text-lg">Commission rate</p>
						</div>

						<div className="flex justify-between items-center">
							<p className="text-black sm:text-lg">0-400 hours</p>
							<p className="text-black sm:text-lg">20%</p>
						</div>
					</div>
				</div>

				<div className="sm:flex gap-5 mt-10">
					<Button
						className="w-full h-[50px] bg-white border-[#D2D2D2] text-secondary border hover:bg-white"
						onClick={() => setActiveTab("Availability")}
					>
						Back
					</Button>

					<Button
						className="w-full mt-4 sm:mt-0 h-[50px]"
						disabled={updating}
						type="submit"
					>
						Save And Continue
					</Button>
				</div>
			</form>
		</>
	);
};

export default TutorPrice;
