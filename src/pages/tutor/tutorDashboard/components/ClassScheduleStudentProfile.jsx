import React from "react";
import { Video } from "lucide-react";
import { useNavigate } from "react-router-dom";
import userVector from "@/assets/svgs/userVector.svg";

const ClassScheduleStudentProfile = ({ upcomingClasses }) => {
	const formatDisplayTime = (dateString) => {
		const date = new Date(dateString);
		// Add UTC offset to get correct local time display
		const localTime = new Date(
			date.getTime() + date.getTimezoneOffset() * 60000
		);
		return localTime.toLocaleTimeString("en-US", {
			hour: "numeric",
			minute: "2-digit",
			hour12: true,
		});
	};

	const getEndTime = (startTime, duration) => {
		const start = new Date(startTime);
		// Add UTC offset to get correct local time display
		const localStart = new Date(
			start.getTime() + start.getTimezoneOffset() * 60000
		);
		const end = new Date(localStart.getTime() + duration * 60000);
		return end.toLocaleTimeString("en-US", {
			hour: "numeric",
			minute: "2-digit",
			hour12: true,
		});
	};

	const startTime = formatDisplayTime(upcomingClasses.scheduledTime);
	const endTime = getEndTime(
		upcomingClasses.scheduledTime,
		upcomingClasses.duration
	);

	const navigate = useNavigate();
	console.log("class item", upcomingClasses);
	return (
		<div className="flex w-full items-stretch gap-3 p-1 sm:p-2 hover:bg-gray-50 transition-colors rounded-lg">
			{/* Time column */}
			<div>
				<div className="flex flex-col w-auto sm:w-full pt-1">
					<p className="text-sm font-medium text-gray-600">{startTime}</p>
					<div className="w-0.5 flex- pl-7">|</div>
					<p className="text-sm font-medium text-gray-600">{endTime}</p>
				</div>
			</div>

			{/* student card */}
			<div className="flex flex-1 items-center gap-4 p-3 rounded-lg border border-amber-200 bg-amber-50 shadow-sm">
				{/* student avatar */}
				<div className="relative">
					<img
						src={upcomingClasses?.student?.img || userVector}
						alt={upcomingClasses?.student?.name}
						className="rounded-full w-12 h-12 object-cover border-2 border-white shadow-sm"
					/>
					<div className="absolute -bottom-1 -right-1 bg-green-500 rounded-full p-1">
						<Video className="h-3 w-3 text-white" />
					</div>
				</div>

				{/* student info */}
				<div className="flex-1">
					<h3 className="font-medium text-gray-900 text-sm">
						{upcomingClasses?.student?.name}
					</h3>
					<p className="text-[#4B5563] text-xs">{upcomingClasses?.title}</p>
				</div>

				{/* Join button */}
				<button
					onClick={() =>
						navigate(`/classroom/${upcomingClasses.classroom.id}`, {
							state: upcomingClasses,
						})
					}
					className="flex items-center gap-1 bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-md text-sm font-medium transition-colors shadow-sm"
				>
					Join Class
				</button>
			</div>
		</div>
	);
};

export default ClassScheduleStudentProfile;
