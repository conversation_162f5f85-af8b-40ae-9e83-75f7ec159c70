import EditOrDeleteReviewModal from "@/pages/student/studentDashboard/reviews/components/EditOrDeleteReviewModal";
import { X } from "lucide-react";
import React, { useState, useRef, useEffect } from "react";

const TheModal = () => {
	return (
		<div className="absolute right-0 mt-2 w-auto bg-white border rounded-lg shadow-lg z-50">
			<div className="flex text-xs flex-col p-2">
				<div className="px-2 w-full">
					<button className="px-4 w-[144px] py-2 border mb-2 rounded-md">
						Last 30 days
					</button>
				</div>
				<div className="px-2 w-full">
					<button className="px-4 py-2 w-[144px] border rounded-md">
						Last 7 days
					</button>
				</div>
			</div>
		</div>
	);
};

const Last90DaysModal = () => {
	const modalRef = useRef(null);
	const buttonRef = useRef(null);
	const [showOptions, setShowOptions] = useState(false);

	const handleShowOptions = (e) => {
		e.stopPropagation();
		setShowOptions(!showOptions);
	};

	const hideOption = () => {
		setShowOptions(false);
	};

	useEffect(() => {
		const handleClickOutside = (event) => {
			// Close if clicked outside of both modal and button
			if (
				modalRef.current &&
				!modalRef.current.contains(event.target) &&
				!buttonRef.current.contains(event.target)
			) {
				hideOption();
			}
		};

		if (showOptions) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [showOptions]);

	return (
		<div className="relative">
			<div>
				<button
					ref={buttonRef}
					onClick={handleShowOptions}
					className="border px-3 py-2 rounded-lg text-sm flex items-center justify-between w-full md:w-[150px]"
					aria-expanded={showOptions}
					aria-label="Review options"
				>
					Last 30 days
				</button>

				{showOptions && (
					<div ref={modalRef} className="absolute right-0 z-50 mt-1">
						<TheModal onClose={hideOption} />
					</div>
				)}
			</div>
		</div>
	);
};

export default Last90DaysModal;
