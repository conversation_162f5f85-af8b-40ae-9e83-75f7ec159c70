import { Check } from "lucide-react";
import { Button } from "@/components/button/button";

const WithdrawSuccessModal = ({ onClose, onContinue }) => {
	return (
		<div className="fixed inset-0 z-50 flex items-center justify-center">
			{/* Overlay */}
			<div
				className="absolute inset-0 bg-black bg-opacity-30"
				onClick={onClose}
			></div>

			{/* Modal Content - now properly centered */}
			<div className="relative w-[482px] h-[350px] bg-white rounded-lg shadow-lg p-6 mx-4">
				{/* Success Icon */}
				<div className="flex justify-center my-4 mb-6">
					<div className="bg-[#1BC717] w-10 h-10 rounded-full flex items-center justify-center">
						<Check className="text-white w-6 h-6" />
					</div>
				</div>
				<div className="pt-6">
					{/* Success Message */}
					<h2 className="text-2xl font-bold text-[#1A1A40] mb-2 text-center">
						Payment successful
					</h2>
					<p className="text-gray-600 mb-8 text-center">
						Your withdrawal request has been processed successful. <br /> The
						fund will reach your account shortly
					</p>

					<Button
						className="w-full h-50"
						onClick={onContinue || onClose}
						disabled={false}
					>
						Continue
					</Button>
				</div>
			</div>
		</div>
	);
};

export default WithdrawSuccessModal;
