import { X } from "lucide-react";
import React, { useState } from "react";
import ExtraSlotsModal from "./ExtraSlotsModal";
import LessonModal from "./lessonTab/LessonModal";
import TimeOffModal from "./TimeOffModal";

import lessonImg from "@/assets/images/studentDashboard/book-edit.png";
import timeOffImg from "@/assets/images/tutorDashboard/clock-05.png";
import extraSlotsImg from "@/assets/images/tutorDashboard/more.png";

const MainScheduleModal = ({ onClose }) => {
	const [activeTab, setActiveTab] = useState("lesson");

	const tabs = [
		{
			id: "lesson",
			label: "Lesson",
			component: <LessonModal />,
			img: <img src={lessonImg} alt="lessonImg icon" className="w-6 h-6"></img>,
		},
		{
			id: "timeOff",
			label: "Time Off",
			component: <TimeOffModal />,
			img: <img src={lessonImg} alt="lessonImg icon" className="w-6 h-6"></img>,
		},
		{
			id: "extraSlots",
			label: "Extra Slots",
			component: <ExtraSlotsModal />,
			img: <img src={lessonImg} alt="lessonImg icon" className="w-6 h-6"></img>,
		},
	];

	return (
		<div className="w-[700px] bg-white rounded-md p-6">
			<div className="flex justify-between items-center mb-6">
				<h1 className="text-xl font-bold">Schedule</h1>
				<button onClick={onClose} className="text-gray-500 hover:text-gray-700">
					<X size={20} />
				</button>
			</div>

			{/* Tab Navigation */}
			<div className="flex  border-gray-200 mb-6">
				{tabs.map((tab) => (
					<button
						key={tab.id}
						onClick={() => setActiveTab(tab.id)}
						className={`px-4 py-2 border bg-[#EBEDF0] mr-4 rounded-lg font-medium  ${
							activeTab === tab.id
								? "border-b-2 text-white bg-primary border-primary"
								: "text-[#1A1A40]"
						}`}
					>
						<div className="flex space-x-4">
							<span className="pr-2">{tab.img}</span>
							{tab.label}
						</div>
					</button>
				))}
			</div>

			{/* Tab Content */}
			<div className="min-h-[300px]">
				{tabs.find((tab) => tab.id === activeTab)?.component}
			</div>
		</div>
	);
};

export default MainScheduleModal;
