import React from "react";
import img from "@/assets/images/tutor1.png";

const student = {
	img: <img src={img} className="w-14 h-14 rounded-full object-cover"></img>,
	name: "<PERSON>",
	lessons: 10,
	lessonType: "subscription",
};

const StudentCard = () => {
	return (
		<div className="h-[80px] border rounded-md text-[#1A1A40] w-[481px] pb-4 flex justify-between">
			<div className="flex p-2">
				<div className="mr-4">
					<span>{student.img}</span>
				</div>
				<div>
					<p>{student.name}</p>
					<p className="text-[#4B5563]">Lessons: {student.lessons}</p>
				</div>
			</div>
			<p className="text-sm pt-6 p-4">{student.lessonType}</p>
		</div>
	);
};

export default StudentCard;
