import React from "react";

const ConfirmationModal = ({
	show,
	title,
	confirmText = "Yes, confirm",
	cancelText = "No",
	onConfirm,
	onCancel,
	confirmColor = "green",
}) => {
	if (!show) return null;

	const confirmButtonClasses = {
		green: "bg-green-500 hover:bg-green-600",
		red: "bg-red-500 hover:bg-red-600",
		blue: "bg-blue-500 hover:bg-blue-600",
	};

	return (
		<div
			className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
			onClick={onCancel}
		>
			<div
				className="bg-white rounded-xl p-6 w-full max-w-md text-center"
				onClick={(e) => e.stopPropagation()}
			>
				<h3 className="text-xl font-bold mb-6">{title}</h3>
				<div className="flex justify-center gap-4">
					<button
						className="border border-gray-300 py-2 px-6 rounded-md hover:bg-gray-50"
						onClick={onCancel}
					>
						{cancelText}
					</button>
					<button
						className={`${confirmButtonClasses[confirmColor]} text-white py-2 px-6 rounded-md`}
						onClick={onConfirm}
					>
						{confirmText}
					</button>
				</div>
			</div>
		</div>
	);
};

export default ConfirmationModal;
