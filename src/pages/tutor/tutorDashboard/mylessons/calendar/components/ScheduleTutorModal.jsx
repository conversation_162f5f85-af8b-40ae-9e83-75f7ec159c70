import { X } from "lucide-react";
import React from "react";

const ScheduleTutorModal = ({
	selectedSlot,
	tutors = [],
	onClose,
	onScheduleTutor,
}) => {
	return (
		<div
			// className="w-full fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
			className=""
			onClick={onClose}
		>
			<div
				className="bg-white rounded-xl sm:w-[959px] p-6 w-full max-w-full"
				onClick={(e) => e.stopPropagation()}
			>
				<div className="flex justify-between items-center p-6 mb-4">
					<h3 className="text-xl font-bold">
						Select a tutor you want to schedule a class with
					</h3>
					<button
						className="text-gray-500 hover:text-gray-700"
						onClick={onClose}
					>
						<X />
					</button>
				</div>
				{/* 
				<p className="text-gray-600 mb-4">{selectedSlot.dateLabel}</p>
				<p className="text-gray-600 mb-6">{selectedSlot.timeLabel}</p> */}

				<div className="max-h-[60vh] px-6 overflow-y-auto">
					{tutors.map((tutor) => (
						<div
							key={tutor.id}
							className="flex justify-between items-center py-3 border-b last:border-b-0"
						>
							<div className="flex items-center space-x-4">
								<img
									src={tutor.image}
									alt={tutor.name}
									className="w-12 h-12 rounded-full object-cover"
								/>
								<div>
									<p className="text-[#1A1A40]font-medium">{tutor.name}</p>
									<p className="text-gray-600 text-sm">{tutor.subject}</p>
								</div>
							</div>
							<div className="text-right">
								<p className="text-[#1A1A40] text-sm">
									{tutor.lessons} lessons
								</p>
								<button
									className="text-blue-600 hover:underline text-sm"
									onClick={() => onScheduleTutor(tutor)}
								>
									Schedule
								</button>
							</div>
							<div>
								<p className="text-gray-800 font-medium">
									{tutor.price} ({tutor.duration})
								</p>
								<p className="text-gray-600 text-xs">Per lesson</p>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default ScheduleTutorModal;
