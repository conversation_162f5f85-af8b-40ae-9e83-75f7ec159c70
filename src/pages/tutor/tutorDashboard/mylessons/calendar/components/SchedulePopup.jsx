import React from "react";

const SchedulePopup = () => {
	return (
		<div className="bg-white rounded-xl p-6 w-full max-w-md">
			<h3 className="text-xl font-bold mb-4">
				Select a tutor you want to schedule a class with
			</h3>
			<p className="text-gray-600 mb-4">{selectedSlot.dateLabel}</p>
			<p className="text-gray-600 mb-4">{selectedSlot.timeLabel}</p>
			{mockTutors.map((tutor) => (
				<div
					key={tutor.id}
					className="flex justify-between items-center py-2 border-b"
				>
					<div className="flex items-center space-x-4">
						<img
							src={tutor.image}
							alt={tutor.name}
							className="w-12 h-12 rounded-full"
						/>
						<div>
							<p className="text-gray-800 font-medium">{tutor.name}</p>
							<p className="text-gray-600">{tutor.subject}</p>
						</div>
					</div>
					<div className="text-right">
						<p className="text-gray-600">{tutor.lessons} lessons</p>
						<button className="text-blue-600 hover:underline">Schedule</button>
						<p className="text-gray-800">
							{tutor.price} ({tutor.duration})
						</p>
					</div>
				</div>
			))}
		</div>
	);
};

export default SchedulePopup;
