import React from "react";
import MyLessonsStudentsCard from "./MyLessonsStudentsCard";
import useGet from "@/hooks/useGet";
import { useGetTutorClassesQuery } from "@/redux/slices/student/classesApiSlice";
import userVector from "@/assets/svgs/userVector.svg";

const Students = () => {
	const { data: bookings, isLoading } = useGet(useGetTutorClassesQuery, "");

	// Process students data to group by student and count lessons
	const processStudents = () => {
		if (!bookings?.bookings) return [];

		const studentMap = new Map();

		bookings.bookings.forEach((booking) => {
			const studentId = booking.student?.id;

			if (studentMap.has(studentId)) {
				// If student exists, increment lesson count
				const existing = studentMap.get(studentId);
				studentMap.set(studentId, {
					...existing,
					lessonCount: existing.lessonCount + 1,
					bookings: [...existing.bookings, booking],
				});
			} else {
				// Add new student entry
				studentMap.set(studentId, {
					id: studentId,
					name: booking.student?.name,
					email: booking.student?.email,
					avatar: userVector,
					lessonCount: 1,
					isFreeTrial: booking.isFreeTrial,
					bookings: [booking],
					// You can add more fields from the first booking as needed
					courseTitle: booking.title,
					lessonType: booking.isFreeTrial ? "trial lesson" : "regular lesson",
					price: booking.isFreeTrial ? `(50mins)` : "$XX.XX", // Adjust with actual price if available
				});
			}
		});

		return Array.from(studentMap.values());
	};

	const students = processStudents();

	if (isLoading) {
		return <div>Loading students...</div>;
	}

	if (students.length === 0) {
		return <div>No students found.</div>;
	}

	return (
		<div className="">
			{students.map((student) => (
				<MyLessonsStudentsCard
					key={student?.id}
					student={{
						...student,
						name: student?.name,
						avatar: student.avatar,
						courseTitle: student.courseTitle,
						lessonType: student.lessonType,
						price: student.price,
						lessonCount: student.lessonCount,
					}}
				/>
			))}
		</div>
	);
};

export default Students;
