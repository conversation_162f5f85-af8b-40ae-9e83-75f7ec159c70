import React from "react";
import { useNavigate } from "react-router-dom";
import userVector from "@/assets/svgs/userVector.svg";
import { formatFullName } from "@/utils/utils";

const MyLessonsStudentsCard = ({ student }) => {
	const navigate = useNavigate();

	// Format price: if it's a trial, only show time
	const isTrial = student.lessonType?.toLowerCase().includes("trial");
	const formattedPrice = isTrial
		? student.price.match(/\((.*?)\)/)?.[1] || "Time N/A"
		: student.price;

	// Capitalize lesson type
	const formattedLessonType = student.lessonType
		?.split(" ")
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
		.join(" ");

	console.log(student);

	return (
		<div className="border rounded-md p-4 mb-4">
			{/* Mobile Header (only shows on mobile) */}
			<div className="md:hidden flex items-center mb-4">
				<img
					src={student?.avatar || userVector}
					alt={student?.firstname}
					className="object-cover w-14 h-14 mr-4 rounded-md"
				/>
				<div>
					<p className="text-[#1A1A40] text-md font-semibold">
						{formatFullName(student?.name) || "Student Name"}
					</p>
					<p className="text-sm text-[#4B5563]">{student?.courseTitle}</p>
				</div>
			</div>

			{/* Desktop Layout (shows on md screens and above) */}
			<div className="hidden md:flex justify-between items-center">
				<div className="flex">
					<img
						src={student.avatar}
						alt={student?.firstname}
						className="object-cover w-14 h-14 mr-6 rounded-md"
					/>
					<div className="mt-1">
						<p className="text-[#1A1A40] text-md font-semibold">
							{formatFullName(student?.name)}
						</p>
						<div className="flex flex-row">
							<p className="text-sm text-[#4B5563]">{student.courseTitle}</p>
						</div>
					</div>
				</div>

				{/* number of lessons */}
				<div className="text-center">
					<p className="text-[#1A1A40] text-md font-semibold">
						{student.lessonCount}{" "}
						{student.lessonCount === 1 ? "lesson" : "lessons"}
					</p>
					<p className="text-[#4B5563] text-sm">Schedule</p>
				</div>

				{/* price of lesson or just time if trial */}
				<div className="text-center">
					<p className="text-[#1A1A40] text-md font-semibold">
						{formattedPrice}
					</p>
					<p className="text-[#4B5563] text-sm">per lesson</p>
				</div>

				{/* lesson type button (Trial Lesson, Single Lesson and subs.) */}
				<div>
					<span className="p-2 w-[130px] text-lg text-primary font-medium">
						{formattedLessonType}
					</span>
				</div>
			</div>

			{/* Mobile Details (only shows on mobile) */}
			<div className="md:hidden grid grid-cols-3 gap-4 mt-4">
				<div>
					<p className="text-[#4B5563] text-sm">Lessons</p>
					<p className="text-[#1A1A40] text-md font-semibold">
						{student.lessonCount}{" "}
						{student.lessonCount === 1 ? "lesson" : "lessons"}
					</p>
				</div>

				<div>
					<p className="text-[#4B5563] text-sm">Price</p>
					<p className="text-[#1A1A40] text-md font-semibold">
						{formattedPrice}
					</p>
				</div>

				<div>
					<p className="text-[#4B5563] text-sm">Type</p>
					<p className="text-primary text-md font-medium">
						{formattedLessonType}
					</p>
				</div>
			</div>
		</div>
	);
};

export default MyLessonsStudentsCard;
