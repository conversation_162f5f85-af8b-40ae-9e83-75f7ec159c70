import React from "react";
import star from "@/assets/svgs/fullStar.svg";
import RateTutorModal from "@/pages/student/studentDashboard/reviews/components/RateTutorModal";
import { format } from "date-fns";
import userVector from "@/assets/svgs/userVector.svg";
import { capitalizeWords, formatFullName } from "@/utils/utils";
import { useReviewTutorMutation } from "@/redux/slices/student/reviewApiSlice";
import { useSelector } from "react-redux";

const PastLessons = ({ lesson }) => {
	const [reviewTutor] = useReviewTutorMutation();
	const user = useSelector((state) => state?.app?.userInfo?.user);

	const date = format(new Date(lesson.scheduledTime), "EEEE, MMMM d, HH:mm");

	console.log(lesson);

	return (
		<div className="border rounded-lg p-3 sm:p-4 md:p-6">
			<div className="flex flex-col sm:flex-row sm:justify-between gap-3 sm:gap-4">
				{/* Main content section */}
				<div className="flex flex-1 min-w-0">
					<img
						src={lesson?.studentImage || userVector}
						alt="Tutor profile"
						className="object-cover w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-lg flex-shrink-0 mr-3 sm:mr-4"
					/>
					<div className="min-w-0 flex-1">
						{/* Date and time */}
						<h3 className="text-[#1A1A40] text-sm sm:text-base md:text-lg lg:text-xl font-semibold leading-tight mb-1 sm:mb-2">
							{date}
						</h3>

						{/* Payment info and tutor details */}
						<div className="text-[#4B5563] text-xs sm:text-sm space-y-1 sm:space-y-0">
							<div className="flex flex-wrap items-start gap-1">
								<span className="font-medium">
									{formatFullName(lesson?.student?.name) || "Student Name"},
								</span>
								<span className="break-words">{lesson?.title}</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default PastLessons;
