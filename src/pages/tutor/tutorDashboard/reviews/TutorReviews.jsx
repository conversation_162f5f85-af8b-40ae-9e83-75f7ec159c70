import React from "react";
import { Star } from "lucide-react";
import useGet from "@/hooks/useGet";
import { useGetReviewsQuery } from "@/redux/slices/tutor/tutorReviewsApiSlice";
import likes from "@/assets/images/tutorDashboard/favourite.png";
import comments from "@/assets/images/tutorDashboard/comment-03.png";
import StarRating from "@/assets/images/studentDashboard/Star 5.png";
import Loader from "@/components/loader/loader";

const TutorReviews = () => {
	const { data: reviews, isLoading: gettingReviews } = useGet(
		useGetReviewsQuery,
		""
	);
	console.log(reviews);

	// Calculate rating breakdown from reviews
	const ratingBreakdown = Array(5)
		.fill(0)
		.map((_, index) => {
			const star = 5 - index;
			return (reviews?.data?.reviews || []).filter(
				(review) => review.rating === star
			).length;
		});

	const totalReviews = reviews?.data?.statistics?.totalReviews || 0;
	const averageRating = reviews?.data?.statistics?.averageRating || 0;

	const formatDate = (dateString) => {
		return dateString
			? new Date(dateString).toLocaleDateString("en-US", {
					year: "numeric",
					month: "short",
					day: "numeric",
			  })
			: "N/A";
	};

	return (
		<div className="w-full">
			{gettingReviews && <Loader />}

			{/* Header Metrics */}
			<div className="bg-white px-4 py-6 w-full rounded-md border border-[#E8E8E8]">
				<div className="flex flex-col sm:flex-row sm:cols-3 justify-between gap-4 sm:gap-6">
					{/* Total Reviews */}
					<div>
						<h3 className="text-lg md:text-[22px] font-semibold text-[#1A1A40]">
							Total Reviews
						</h3>
						<p className="text-md md:text-[38px] font-semibold text-[#1A1A40]">
							{totalReviews}
						</p>
					</div>

					{/* Average Rating */}
					<div>
						<h3 className="text-lg md:text-[22px] font-semibold text-[#1A1A40]">
							Average Rating
						</h3>
						<div className="flex items-center gap-1">
							<p className="text-md md:text-[38px] font-semibold text-[#1A1A40]">
								{averageRating.toFixed(1)}
							</p>
							<div className="flex">
								{[...Array(5)].map((_, i) => (
									<Star
										key={i}
										className={`w-5 h-5 ${
											i < Math.round(averageRating)
												? "fill-yellow-400 stroke-yellow-400"
												: "stroke-gray-300"
										}`}
									/>
								))}
							</div>
						</div>
					</div>

					{/* Rating Breakdown */}
					<div className="space-y-2 w-full sm:w-1/2 md:w-1/3">
						{[5, 4, 3, 2, 1].map((stars, index) => (
							<div key={stars} className="flex items-center gap-2">
								<div className="flex items-center w-8">
									<span className="text-sm text-gray-600">{stars}</span>
								</div>
								<div className="flex-1 bg-gray-100 rounded-full h-2">
									<div
										className="bg-[#54C68A] h-2 rounded-full"
										style={{
											width: `${
												totalReviews
													? (ratingBreakdown[index] / totalReviews) * 100
													: 0
											}%`,
										}}
									></div>
								</div>
								<span className="text-xs font-semibold w-4">
									{ratingBreakdown[index]}
								</span>
							</div>
						))}
					</div>
				</div>
			</div>

			{/* Reviews List */}
			<div className="mt-6">
				<h3 className="text-lg md:text-xl font-semibold text-[#1A1A40] mb-4">
					Student Reviews
				</h3>
				{gettingReviews ? (
					<p className="text-[#4B5563] text-sm">Loading reviews...</p>
				) : !reviews?.data?.reviews?.length ? (
					<p className="text-[#4B5563] text-sm">No reviews yet.</p>
				) : (
					reviews.data.reviews.map((review, index) => (
						<div
							key={review.id || index}
							className="my-2 pb-2 mb-4 border rounded-md p-4 flex flex-col sm:flex-row justify-between"
						>
							<div className="flex flex-col sm:flex-row w-full">
								{/* Student Info */}
								<div className="flex items-start sm:pr-6 w-full sm:w-auto mb-4 sm:mb-0">
									<img
										src={review.student?.avatar || "/default-avatar.png"}
										alt={review.student?.name || "Student"}
										className="object-cover w-12 h-12 sm:w-16 sm:h-16 mr-4 rounded-md"
									/>
									<div className="mt-1">
										<p className="text-[#1A1A40] text-sm sm:text-md font-semibold">
											{review.student?.name || "Anonymous"}
										</p>
										<p className="text-[#4B5563] text-xs sm:text-sm sm:hidden">
											{formatDate(review.ratingDate)}
										</p>
									</div>
								</div>

								{/* Rating and Content */}
								<div className="flex flex-col w-full sm:w-[calc(100%-220px)]">
									<div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-5 py-1 sm:py-2">
										<p className="text-[#1A1A40] text-sm sm:text-md flex font-semibold">
											{[...Array(review.rating || 0)].map((_, i) => (
												<img
													key={i}
													src={StarRating}
													alt="star"
													className="w-4 sm:w-5"
												/>
											))}
										</p>
										<p className="text-xs sm:text-sm text-[#4B5563] hidden sm:block">
											{formatDate(review.ratingDate)}
										</p>
									</div>
									<div>
										<p className="text-[#4B5563] text-xs sm:text-sm">
											{review.comment || "No comment provided."}
										</p>

										{/* Comments and Likes */}
										<div className="flex mt-3 sm:mt-4">
											<div className="flex items-center gap-2 pr-4">
												<img
													src={comments}
													className="w-5 h-5"
													alt="comments"
												/>
												<span className="text-xs text-[#4B5563]">
													{review.commentsCount || 0}
												</span>
											</div>
											<div className="flex items-center gap-2">
												<img src={likes} className="w-5 h-5" alt="likes" />
												<span className="text-xs text-[#4B5563]">
													{review.likesCount || 0}
												</span>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					))
				)}
			</div>
		</div>
	);
};

export default TutorReviews;
