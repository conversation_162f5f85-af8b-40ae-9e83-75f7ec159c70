import React from "react";
import img from "@/assets/images/tutor1.png";
import StarRating from "@/assets/images/studentDashboard/Star 5.png";
import { X } from "lucide-react";

const student = {
	img: <img src={img} className="h-14 w-14 object-cover rounded-full"></img>,
	name: "drink water",
	ratingDate: " 12/23/2023",
	reviewMsg:
		"Start creating your public tutor profile. Your progress will be automatically saved as you complete each section. You can return at any time to finish your registration. Start creating your public tutor profile. Your progress will be automatically saved as you complete each section. You can return at any time to finish your registration.",
};

const ViewRatingModal = ({ onClose }) => {
	return (
		<div className="bg-white w-sm mx-auto flex justify-between rounded-md p-4">
			<div className="flex">
				<div className="flex pr-6">
					<span className="pr-4">{student.img}</span>
					<div className="w-100">
						<p className="text-xl pr-4 text-[#1A1A40] font-semibold">
							{student.name}
						</p>
					</div>
				</div>
				<div className="flex flex-col w-[450px] mr-6">
					<div className="flex gap-5 flex-row py-2 ">
						<p className="text-[#1A1A40] text-md flex font-semibold">
							<img src={StarRating} alt="" />
							<img src={StarRating} alt="" />
							<img src={StarRating} alt="" />
							<img src={StarRating} alt="" />
						</p>{" "}
						<p className="text-[16px] text-[#4B5563]">{student.ratingDate}</p>
					</div>
					<div>
						<p className="text-[#4B5563] font-medium text-sm w-100">
							{student.reviewMsg}
						</p>
					</div>
				</div>
			</div>
			<div className="items-end">
				<button onClick={onClose}>
					<X />
				</button>
			</div>
		</div>
	);
};

export default ViewRatingModal;
