import { Button } from "@/components/button/button";
import React from "react";
import { useForm, useFieldArray } from "react-hook-form";

const TutorBackgroundSettings = () => {
	const {
		register,
		control,
		handleSubmit,
		formState: { errors },
	} = useForm({
		defaultValues: {
			certificates: [
				{
					subject: "",
					description: "",
					issuedBy: "",
					startYear: "",
					endYear: "",
				},
			],
			education: [
				{
					university: "",
					degree: "",
					degreeType: "",
					specialization: "",
					startYear: "",
					endYear: "",
				},
			],
		},
	});

	const {
		fields: certificateFields,
		append: appendCertificate,
		remove: removeCertificate,
	} = useFieldArray({
		control,
		name: "certificates",
	});

	const {
		fields: educationFields,
		append: appendEducation,
		remove: removeEducation,
	} = useFieldArray({
		control,
		name: "education",
	});

	const degreeOptions = [
		{ value: "bachelor", label: "Bachelor's degree" },
		{ value: "master", label: "Master's degree" },
		{ value: "phd", label: "PhD" },
		{ value: "diploma", label: "Diploma" },
		{ value: "associate", label: "Associate degree" },
	];

	const onSubmit = (data) => {
		console.log("Form Data:", data);
	};

	return (
		<div className="md:max-w-[528px] w-auto">
			<form
				onSubmit={handleSubmit(onSubmit)}
				className="flex text-[#1A1A40] text-[18px] flex-col gap-4 mt-4"
			>
				{/* Teaching Certificates Section */}
				<div>
					<h2 className="font-bold text-[#1A1A40] text-[26px] mb-4">
						Teaching certificate
					</h2>

					{certificateFields.map((field, index) => (
						<div key={field.id} className="mb-6 border-b pb-6">
							<div className="py-2 mb-4">
								<label className="block font-medium pb-4">Subject</label>
								<input
									{...register(`certificates.${index}.subject`)}
									className="w-full p-2 border rounded-md"
									placeholder="e.g. English"
								/>
							</div>
							<div className="py-2 mb-4">
								<label className="block font-medium pb-4">Certification</label>
								<input
									{...register(`certificates.${index}.description`)}
									className="w-full p-2 border rounded-md"
									placeholder="e.g. Convolly language teaching certificate"
								/>
							</div>
							<div className="py-2 mb-4">
								<label className="block font-medium pb-4">Description</label>
								<input
									{...register(`certificates.${index}.description`)}
									className="w-full p-2 border rounded-md"
									placeholder="e.g. Convolly language teaching certificate"
								/>
							</div>

							<div className="py-2 mb-4">
								<label className="block font-medium pb-4">Issued by</label>
								<input
									{...register(`certificates.${index}.issuedBy`)}
									className="w-full p-2 border rounded-md"
									placeholder="e.g. Convolly"
								/>
							</div>

							<div className="grid py-4 grid-cols-2 gap-4 mt-4">
								<div className="py-2 mb-4">
									<label className="block font-medium pb-4">Start year</label>
									<input
										type="number"
										{...register(`certificates.${index}.startYear`)}
										className="w-full p-2 border rounded-md"
										placeholder="2020"
									/>
								</div>
								<div className="py-2 mb-4">
									<label className="block font-medium pb-4">End year</label>
									<input
										type="number"
										{...register(`certificates.${index}.endYear`)}
										className="w-full p-2 border rounded-md"
										placeholder="2024"
									/>
								</div>
							</div>

							{index > 0 && (
								<button
									type="button"
									onClick={() => removeCertificate(index)}
									className="mt-2 text-red-500"
								>
									Remove certificate
								</button>
							)}
						</div>
					))}

					<button
						type="button"
						onClick={() =>
							appendCertificate({
								subject: "",
								description: "",
								issuedBy: "",
								startYear: "",
								endYear: "",
							})
						}
						className="underline"
					>
						Add another certificate
					</button>
				</div>

				{/* Education Section */}
				<div className="mt-8">
					<h2 className="font-bold text-[#1A1A40] text-[26px] mb-4">
						Education
					</h2>

					{educationFields.map((field, index) => (
						<div key={field.id} className="mb-6 border-b pb-6">
							<div className="mt-4 py-2 mb-4">
								<label className="block font-medium pb-4">University</label>
								<input
									{...register(`education.${index}.university`)}
									className="w-full p-2 border rounded-md"
									placeholder="e.g. Caleb University"
								/>
							</div>

							<div className="py-2 mb-4">
								<label className="block font-medium pb-4">Degree</label>
								<input
									{...register(`education.${index}.degree`)}
									className="w-full p-2 border rounded-md"
									placeholder="e.g. BSc (Hon) Chemical Engineering"
								/>
							</div>
							<div className="py-2 mb-4">
								<label className="block font-medium pb-4">Degree type</label>
								<select
									{...register(`education.${index}.degreeType`)}
									className="w-full p-2 border rounded-md bg-white"
								>
									{degreeOptions.map((option) => (
										<option key={option.value} value={option.value}>
											{option.label}
										</option>
									))}
								</select>
							</div>

							<div className="py-2 mb-4">
								<label className="block font-medium pb-4">Specialization</label>
								<input
									{...register(`education.${index}.specialization`)}
									className="w-full p-2 border rounded-md"
									placeholder="e.g. English study"
								/>
							</div>

							<div className="grid grid-cols-2 gap-4 mt-4">
								<div className="py-2 mb-4">
									<label className="block font-medium pb-4">Start year</label>
									<input
										type="number"
										{...register(`education.${index}.startYear`)}
										className="w-full p-2 border rounded-md"
										placeholder="2020"
									/>
								</div>
								<div className="py-2 mb-4">
									<label className="block font-medium pb-4">End year</label>
									<input
										type="number"
										{...register(`education.${index}.endYear`)}
										className="w-full p-2 border rounded-md"
										placeholder="2024"
									/>
								</div>
							</div>

							{index > 0 && (
								<button
									type="button"
									onClick={() => removeEducation(index)}
									className="mt-2 text-red-500"
								>
									Remove education
								</button>
							)}
						</div>
					))}

					<button
						type="button"
						onClick={() =>
							appendEducation({
								university: "",
								degree: "",
								degreeType: "",
								specialization: "",
								startYear: "",
								endYear: "",
							})
						}
						className="underline"
					>
						Add another education
					</button>
				</div>

				<Button type="submit" className="h-50">
					Save changes
				</Button>
			</form>
		</div>
	);
};

export default TutorBackgroundSettings;
