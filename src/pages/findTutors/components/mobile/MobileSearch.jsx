import { X, SearchIcon } from "lucide-react";
import React, { useState, useEffect } from "react";
import { useDebounce } from "../../../../hooks/useDebounce";
import searchIcon from "../../../../assets/svgs/findtutor/search-02.svg";

const MobileSearch = ({ onClose, onSearch, searchQuery }) => {
	const [searchValue, setSearchValue] = useState(searchQuery || "");
	const debouncedSearchValue = useDebounce(searchValue, 500);

	useEffect(() => {
		if (debouncedSearchValue !== undefined) {
			onSearch(debouncedSearchValue);
		}
	}, [debouncedSearchValue, onSearch]);

	const handleClear = () => {
		setSearchValue("");
		onSearch("");
	};

	return (
		<div className="w-full px-4 pt-6 pb-10">
			<div className="shadow-sm top-[10px] bottom-[10px] flex w-full justify-between items-center mb-4">
				<button onClick={handleClear} className="text-[#4B5563] text-sm">
					Clear all
				</button>
				<h2 className="font-bold text-[22px] text-[#1A1A40]">Search</h2>
				<button
					onClick={onClose}
					className="h-[30px] w-[30px] flex items-center justify-center"
				>
					<X size={24} />
				</button>
			</div>

			<div className="w-full">
				<div className="flex items-center p-3 border border-gray-300 rounded-lg">
					<img
						src={searchIcon}
						alt="searchIcon"
						className="text-[#A4A4A4]"
						height={24}
						width={24}
					/>
					<input
						type="text"
						placeholder="Search by name"
						className="ml-2 w-full border-none outline-none focus:ring-0 text-md"
						autoFocus
						value={searchValue}
						onChange={(e) => setSearchValue(e.target.value)}
					/>
				</div>
			</div>
		</div>
	);
};

export default MobileSearch;
