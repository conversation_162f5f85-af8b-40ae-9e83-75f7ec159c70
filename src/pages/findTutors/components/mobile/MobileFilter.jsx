import {
	X,
	ChartBar,
	MessageCircle,
	Calendar1Icon,
	CheckCheckIcon,
	ComputerIcon,
} from "lucide-react";
import { useState } from "react";

import chartUp from "../../../../assets/svgs/findtutor/chart-up.svg";
import calender from "../../../../assets/svgs/findtutor/calendar.svg";

import message from "../../../../assets/svgs/findtutor/message.svg";
import saleTag from "../../../../assets/svgs/findtutor/sale-tag.svg";
import selectCountry from "../../../../assets/svgs/findtutor/select-country.svg";
import starAward from "../../../../assets/svgs/findtutor/star-award.svg";
import ApleIntelligence from "../../../../assets/svgs/findtutor/apple-intelligence.svg";
import { useForm } from "react-hook-form";

import { CustomSelect } from "../../../../components/select/select";
import MultiSelect from "../../../../components/select/multiSelect";
import PriceDropdown from "../priceDropdown/priceDropdown";
import AvailabilityDropdown from "../availabilityDropdown/availabilityDropdown";

const languages = ["English", "French", "Spanish"];
const countries = ["Nigeria", "America", "Canada"];
const specialities = ["Grammar", "Conversation", "Exam Prep"];
const alsoSpeaks = ["German", "Arabic", "Mandarin"];
const qualities = ["Native Speaker", "Professional Teacher", "Friendly"];
const languageLevels = ["Beginner", "Intermediate", "Advanced"];
const availabilityTimes = ["Morning", "Afternoon", "Evening"];
const daysOfWeek = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

const MobileFilter = ({
	onClose,
	filters,
	onFilterChange,
	searchInput,
	onSearch,
	handleFilterChange,
	onClearFilters,
	onApplyFilters,
}) => {
	const {
		register,
		control,
		formState: { errors },
	} = useForm();

	const [activeDropdown, setActiveDropdown] = useState(null);
	const [experienceLevel, setExperienceLevel] = useState(null);
	const [language, setLanguage] = useState(null);
	const [speciality, setSpeciality] = useState(null);
	const [quality, setQuality] = useState(null);

	const languages = ["English", "French", "Spanish"];
	const industries = ["Medicine", "Law", "Military"];

	const countries = ["Nigeria", "America", "Canada"];
	const specialities = ["Phonetics", "Conversation", "Exam Prep"];
	const alsoSpeaks = ["German", "Arabic", "Mandarin", "Spanish"];
	const qualities = ["Native Speaker", "Professional Teacher", "Friendly"];
	const languageLevels = ["Beginner", "Intermediate", "Advanced"];

	const availabilityTimes = ["Morning", "Afternoon", "Evening"];
	const daysOfWeek = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

	return (
		<div className="w-full px-4 pt-6 pb-10">
			<div className="shadow-sm top-[10px] bottom-[10px] flex w-full justify-between items-center mb-4">
				<button className="text-[#4B5563] text-sm" onClick={onClearFilters}>
					Clear all
				</button>
				<h2 className="font-bold text-[22px] text-[#1A1A40]">Filter</h2>
				<button
					onClick={onClose}
					className="h-[30px] w-[30px] flex items-center justify-center"
				>
					<X size={24} />
				</button>
			</div>
			<br />
			<div className="h-[333px] gap-3">
				{/* select country  */}
				<div className="flex items-center w-full">
					<div>
						<img
							src={selectCountry}
							alt="select country"
							height={24}
							width={24}
							className="pb-1"
						/>
					</div>
					<MultiSelect
						options={countries.map((country) => ({
							label: country,
							value: country.toLowerCase().substring(0, 3),
						}))}
						placeholder="Select country"
						buttonClassName="border-none"
						control={control}
						name="country"
						onChange={(selected) =>
							handleFilterChange(
								"country",
								selected?.map((item) => item.value)
							)
						}
					/>
				</div>
				<hr className="bg-[#E8E8E8] w-fill border-[1px] border-[#E8E8E8]" />
				{/* price  */}
				<div className="flex items-center w-full py-1">
					<div>
						<img
							src={saleTag}
							alt="price icon"
							height={24}
							width={24}
							className="pb-1"
						/>
					</div>
					<PriceDropdown
						className="border-none"
						onChange={(selected) =>
							handleFilterChange("price", selected?.value)
						}
					/>{" "}
				</div>
				<hr className="w-full border-[1px] border-[#E8E8E8]" />
				{/* availability  */}
				<div className="flex items-center w-full py-1">
					<div>
						<img
							src={calender}
							alt="claneder icon"
							height={24}
							width={24}
							className="pb-1"
						/>
					</div>
					<AvailabilityDropdown
						times={availabilityTimes}
						days={daysOfWeek}
						className="border-none"
						onChange={(selected) =>
							handleFilterChange("available", selected?.value)
						}
					/>
				</div>
				<hr className="w-full border-[1px] border-[#E8E8E8]" />
				{/* also speaks | languages  */}
				<div className="flex items-center w-full py-1">
					<div className="">
						<img
							src={message}
							alt="message icon"
							height={24}
							width={24}
							className="pb-1"
						/>
					</div>
					<CustomSelect
						placeholder="Also Speaks"
						label=""
						className="border-none"
						options={alsoSpeaks.map((lang) => ({
							value: lang.toLowerCase(),
							label: lang,
						}))}
						name="speaks"
						control={control}
						isRequired={false}
						onChange={(selected) =>
							handleFilterChange("language", selected?.value)
						}
					/>
				</div>
				<hr className="w-full border-[1px] border-[#E8E8E8]" />
				{/* specialities  */}
				<div className="flex items-center w-full py-1">
					<div>
						<img
							src={ApleIntelligence}
							alt="specialities icon"
							height={24}
							width={24}
							className="pb-1"
						/>
					</div>
					<CustomSelect
						placeholder="Specialities"
						label=""
						className="border-none"
						options={specialities.map((spec) => ({
							value: spec.toLowerCase().replace(" ", "_"),
							label: spec,
						}))}
						name="speciality"
						control={control}
						isRequired={false}
						onChange={(selected) =>
							handleFilterChange("speciality", selected?.value)
						}
					/>
				</div>
				<hr className="w-full border-[1px] border-[#E8E8E8]" />
				{/* qualitites  */}
				<div className="flex items-center w-full py-1">
					<div>
						<img
							src={starAward}
							alt="qualitites icon"
							height={24}
							width={24}
							className="pb-1"
						/>
					</div>
					<MultiSelect
						options={qualities.map((quality) => ({
							label: quality,
							value: quality.toLowerCase().replace(" ", "_"),
						}))}
						placeholder="Select qualities"
						buttonClassName="border-none"
						control={control}
						name="qualities"
						onChange={(selected) =>
							handleFilterChange(
								"qualitiy",
								selected?.map((item) => item.value)
							)
						}
					/>
				</div>
				<hr className="w-full border-[1px] border-[#E8E8E8]" />
				{/* Language Experience Level  */}
				<div className="flex w-full py-1">
					<div className="flex items-center">
						<img
							src={chartUp}
							alt="experience level icon"
							height={24}
							width={24}
							className="pb-1"
						/>
					</div>

					<CustomSelect
						placeholder="Language Level"
						className="border-none"
						label=""
						options={languageLevels.map((level) => ({
							value: level.toLowerCase(),
							label: level,
						}))}
						name="level"
						control={control}
						isRequired={false}
						onChange={(selected) =>
							handleFilterChange("experienceLevel", selected?.value)
						}
					/>
				</div>
				<hr className="w-full border-[1px] border-[#E8E8E8]" />
			</div>
			<br />
			<br />
			<button
				onClick={onApplyFilters}
				className="w-full p-[10px] gap-3 h-[50px] bg-primary text-white rounded-lg font-medium"
			>
				Find A Tutor
			</button>
		</div>
	);
};

export default MobileFilter;
