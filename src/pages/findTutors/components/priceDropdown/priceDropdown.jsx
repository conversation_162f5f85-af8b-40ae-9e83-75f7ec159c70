import React, { useState, useRef, useEffect } from "react";
import { ChevronDownIcon } from "lucide-react";
import * as SliderPrimitive from "@radix-ui/react-slider";
import clsx from "clsx";

const PriceDropdown = ({
	defaultValue = [4, 100],
	onChange,
	placeholder = "Select price range",
	label = "",
	className,
}) => {
	const dropdownRef = useRef(null);
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const [priceRange, setPriceRange] = useState(defaultValue);

	useEffect(() => {
		const handleClickOutside = (event) => {
			if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
				setIsDropdownOpen(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, []);

	const handleSliderChange = (val) => {
		setPriceRange(val);
		onChange?.(val);
	};

	return (
		<div className="relative w-full space-y-1" ref={dropdownRef}>
			{label && <p className="text-sm text-secondary mb-1">{label}</p>}

			<button
				type="button"
				className={clsx(
					"border px-3 py-[9px] bg-white rounded-md w-full text-left text-sm overflow-auto flex justify-between items-center",
					priceRange.length ? "text-black" : "text-[#777777]",
					"border-[#E8E8E8]", // default border
					className // overrideable
				)}
				onClick={() => setIsDropdownOpen(!isDropdownOpen)}
			>
				<span className="truncate">
					{priceRange.length
						? `$ ${priceRange[0].toLocaleString()} - $ ${priceRange[1].toLocaleString()}`
						: placeholder}
				</span>
				<ChevronDownIcon className="w-4 h-4 ml-2 text-gray-500 shrink-0" />
			</button>

			{isDropdownOpen && (
				<div className="absolute mt-1 bg-white border border-[#E8E8E8] w-full z-10 p-4 rounded-md">
					<SliderPrimitive.Root
						className="relative flex items-center w-full h-5"
						value={priceRange}
						onValueChange={handleSliderChange}
						min={4}
						max={100}
						step={1}
						aria-label="Price Range"
					>
						<SliderPrimitive.Track className="relative h-[4px] w-full bg-gray-200 rounded-full">
							<SliderPrimitive.Range className="absolute h-full bg-primary" />
						</SliderPrimitive.Track>

						{priceRange.map((_, index) => (
							<SliderPrimitive.Thumb
								key={index}
								className="block w-[22px] h-[22px] border bg-white border-[#E8E8E8] rounded-full focus:outline-none"
							/>
						))}
					</SliderPrimitive.Root>

					<div className="mt-2 text-sm text-gray-700 flex justify-between bg-[#E8E8E8] p-3 rounded-lg">
						<div>
							<p className="text-[#A4A4A4] max-sm:text-sm">Minimum</p>
							<span className="text-lg font-bold">$ 4</span>
						</div>
						<div>
							<p className="text-[#A4A4A4] max-sm:text-sm">Maximum</p>
							<span className="text-lg font-bold text-right">$ 100</span>
						</div>
						{/* - $
            {priceRange[1].toLocaleString()} */}
					</div>
				</div>
			)}
		</div>
	);
};

export default PriceDropdown;
