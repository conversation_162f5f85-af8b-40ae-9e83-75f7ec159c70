import React, { useState, useRef, useEffect } from "react";
import { ChevronDownIcon } from "lucide-react";
import Morning from "../../../../assets/svgs/morning.jsx";
import Afternoon from "../../../../assets/svgs/afternoon.jsx";
import Evening from "../../../../assets/svgs/evening.jsx";
import clsx from "clsx";

const timeSlots = [
  // { label: "Morning", value: "9-11", icon: <Morning /> },
  // { label: "Afternoon", value: "12-18", icon: <Afternoon /> },
  // { label: "Evening", value: "19-22", icon: <Evening /> }

  { label: "Morning", value: "9-11", Icon: Morning },
  { label: "Afternoon", value: "12-18", Icon: Afternoon },
  { label: "Evening", value: "19-22", Icon: Evening }
];

const daysOfWeek = [
  { label: "Sun", value: "Sunday" },
  { label: "Mon", value: "Monday" },
  { label: "Tue", value: "Tuesday" },
  { label: "Wed", value: "Wednesday" },
  { label: "Thu", value: "Thursday" },
  { label: "Fri", value: "Friday" },
  { label: "Sat", value: "Saturday" }
];

const AvailabilityDropdown = ({
  defaultValue = { timeRange: [], days: [] },
  onChange,
  label = "",
  placeholder = "Select time & days",
  className = ""
}) => {
  const dropdownRef = useRef(null);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTimes, setSelectedTimes] = useState(defaultValue.timeRange);
  const [selectedDays, setSelectedDays] = useState(defaultValue.days);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    onChange?.({ timeRange: selectedTimes, days: selectedDays });
  }, [selectedTimes, selectedDays]);

  const toggleTime = (time) => {
    setSelectedTimes((prev) =>
      prev.includes(time) ? prev.filter((t) => t !== time) : [...prev, time]
    );
  };

  const toggleDay = (day) => {
    setSelectedDays((prev) =>
      prev.includes(day) ? prev.filter((d) => d !== day) : [...prev, day]
    );
  };
  const hasSelection = selectedTimes.length || selectedDays.length;
  const displayText = hasSelection
    ? `${selectedTimes.join(", ")} | ${selectedDays.join(", ")}`
    : placeholder;

  return (
    <div className="relative w-full space-y-1" ref={dropdownRef}>
      {label && <p className="text-sm text-secondary mb-1">{label}</p>}

      <button
        type="button"
        className={clsx(
          "border px-3 py-[9px] bg-white rounded-md w-full text-left text-sm overflow-auto flex justify-between items-center",
          hasSelection ? "text-black" : "text-[#777777]",
          "border-[#E8E8E8]",
          className
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="truncate">{displayText}</span>
        <ChevronDownIcon className="w-4 h-4 ml-2 text-gray-500 shrink-0" />
      </button>

      {isOpen && (
        <div className="absolute mt-1 bg-white border border-[#E8E8E8] text-[#A4A4A4] w-full z-10 p-4 rounded-md space-y-4">
          <div>
            <p className="text-sm font-semibold mb-2">Time of the day</p>
            <div className="flex gap-2 flex-wrap">
              {timeSlots.map(({ label, value, Icon }) => (
                <button
                  key={value}
                  onClick={() => toggleTime(value)}
                  className={`px-3 py-2 rounded-md border grow text-sm hover:bg-[#e8f9f2] hover:border-[#e8f9f2] group flex flex-col items-center ${
                    selectedTimes.includes(value)
                      ? "bg-[#e8f9f2] border-[#e8f9f2]"
                      : "bg-white border-gray-300"
                  }`}
                >
                  {<Icon active={selectedTimes.includes(value)} />}
                  <p
                    className={`my-1 ${
                      selectedTimes.includes(value)
                        ? "text-primary"
                        : "text-[#A4A4A4]"
                    }`}
                  >
                    {label}
                  </p>

                  <p
                    className={`${
                      selectedTimes.includes(value)
                        ? "text-primary"
                        : "text-[#A4A4A4]"
                    }`}
                  >
                    {value}
                  </p>
                </button>
              ))}
            </div>
          </div>

          <div>
            <p className="text-sm font-semibold mb-2">Days of the Week</p>
            <div className="flex gap-2 flex-wrap">
              {daysOfWeek.map(({ label, value }) => (
                <button
                  key={value}
                  onClick={() => toggleDay(value)}
                  className={`w-10 py-2 text-sm rounded-md border hover:bg-[#e8f9f2] hover:border-[#e8f9f2] ${
                    selectedDays.includes(value)
                      ? "bg-[#e8f9f2] text-primary border-[#e8f9f2]"
                      : "bg-white text-[#A4A4A4] border-gray-300"
                  }`}
                >
                  {label}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AvailabilityDropdown;
