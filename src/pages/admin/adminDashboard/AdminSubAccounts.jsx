import React, { useState } from "react";
import { EditIcon, Trash2Icon } from "lucide-react";
import AddAndEditModal from "../components/subaccounts/AddAndEditModal";
import DeleteUserModal from "../components/subaccounts/DeleteUserModal";
import TableComponent from "@/components/table/table";
import useGet from "@/hooks/useGet";
import { useGetSubAdminsQuery } from "@/redux/slices/admin/AdminSubAccountsApiSlice";
import Loader from "@/components/loader/loader";

const AdminSubAccounts = () => {
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedSubAdminId, setSelectedSubAdminId] = useState(null);

  const { data: subAdmins, isLoading: gettingAdmins } = useGet(
    useGetSubAdminsQuery,
    ""
  );

  const columns = [
    { header: "Full Name", accessor: "fullname" },
    { header: "Email", accessor: "email" },
    { header: "Username", accessor: "username" },
    { header: "Department", accessor: "department" },
    { header: "Admin Level", accessor: "adminLevel" },
    {
      header: "Status",
      accessor: "isActive",
      render: (value) => (value ? "Active" : "Inactive"),
    },
  ];

  const openAddModal = () => {
    setSelectedSubAdminId(null); // Clear selected ID for adding new user
    setShowAddModal(true);
  };

  const openEditModal = (id) => {
    setSelectedSubAdminId(id); // Set ID for editing
    setShowAddModal(true);
  };

  const openDeleteModal = (id) => {
    setSelectedSubAdminId(id); // Set ID for deletion
    setShowDeleteModal(true);
  };

  const closeModal = () => {
    setShowDeleteModal(false);
    setShowAddModal(false);
    setSelectedSubAdminId(null);
  };

  const headerActions = (
    <div className="flex items-center gap-4">
      <button
        onClick={openAddModal}
        className="px-4 py-2 text-white flex items-center gap-2 border rounded-lg bg-primary hover:bg-primary-dark transition-colors"
        aria-label="Add new sub-admin"
      >
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M12 4v16m8-8H4"
          />
        </svg>
        <span>Add new user</span>
      </button>
    </div>
  );

  return (
    <div>
      {gettingAdmins && <Loader />}

      <div className="flex justify-between mb-4">
        <h1 className="text-2xl pt-4 font-semibold">Sub-Admin Accounts</h1>
      </div>

      <div>
        <TableComponent
          columns={columns}
          headerActions={headerActions}
          data={subAdmins || []}
          title=""
        />
      </div>

      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <AddAndEditModal
            onClose={closeModal}
            subAdminId={selectedSubAdminId}
          />
        </div>
      )}

      {showDeleteModal && selectedSubAdminId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <DeleteUserModal
            onCancel={closeModal}
            subAdminId={selectedSubAdminId}
          />
        </div>
      )}
    </div>
  );
};

export default AdminSubAccounts;