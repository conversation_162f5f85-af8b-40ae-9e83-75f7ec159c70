import React from "react";
import StudentChart from "./components/StudentChart";
import RevenueStats from "./components/RevenueStats";

import { useNavigate } from "react-router-dom";
import {
  useGetDashboardTransactionaQuery,
  useGetDashboardStatsQuery
} from "@/redux/slices/admin/adminDashboardApiSlice";
import useGet from "@/hooks/useGet";
import Loader from "@/components/loader/loader";
import TableComponent from "@/components/table/table";

import CoinsIcon from "@/assets/svgs/admin/CoinsIcon";
import HoursIcon from "@/assets/svgs/admin/hoursIcon";
import LessonsIcon from "@/assets/svgs/admin/lessonsIcon";
import StudentsIcon from "@/assets/svgs/admin/studentsIcon";
import TutorsIcon from "@/assets/svgs/admin/tutorsIcon";

const AdminDashboard = () => {
  const navigate = useNavigate();

  // Define cards with dynamic values from stats.overview
  const cards = [
    {
      Icon: CoinsIcon,
      value: "revenueThisMonth",
      label: "Total Revenue"
    },
    {
      Icon: StudentsIcon,
      value: "totalStudents",
      label: "Total no of Students"
    },
    {
      Icon: TutorsIcon,
      value: "totalTutors",
      label: "Total no of Tutors"
    },
    {
      Icon: LessonsIcon,
      value: "totalLessons",
      label: "Total no of Lessons"
    },
    {
      Icon: HoursIcon,
      value: "totalLessons",
      label: "Total no of hours taught"
    }
  ];

  const { data: stats, isLoading: gettingStats } = useGet(
    useGetDashboardStatsQuery,
    ""
  );

  const { data: transactions, isLoading: gettingTransactions } = useGet(
    useGetDashboardTransactionaQuery,
    ""
  );

  // Function to get status styling based on status value
  const getStatusStyle = (status) => {
    switch (status.toLowerCase()) {
      case "paid":
        return "bg-[#ECFDF3] text-[#037847]";
      case "pending":
        return "bg-[#FFDB431A] text-[#DFB400]";
      case "failed":
        return "bg-[#FB37481A] text-[#D00416]";
      case "refunded":
        return "bg-[#4D00FF1A] text-[#3C00C6]";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const columns = [
    { header: "Transaction ID", accessor: "transactionId" },
    { header: "Tutor's Name", accessor: "tutorName" },
    { header: "Wise email address", accessor: "wiseEmail" },
    { header: "Payout method", accessor: "payoutMethod" },
    { header: "Date", accessor: "date" },
    { header: "Time", accessor: "time" },
    { header: "Amount", accessor: "amount" },
    {
      header: "Status",
      accessor: "status",
      render: (row) => (
        <span
          className={`px-2 py-1 rounded-full text-sm ${getStatusStyle(
            row.status
          )}`}
        >
          {row.status}
        </span>
      )
    }
  ];

  const headerActions = (
    <button
      className="border-none outline-none text-primary"
      onClick={() => navigate("/admin/history")}
    >
      See all
    </button>
  );

  return (
    <div className="w-full">
      {(gettingStats || gettingTransactions) && <Loader />}

      <p className="text-[26px] font-semibold text-[#1A1A40]">Dashboard</p>

      <div className="my-8">
        <div className="grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 gap-4 sm:gap-6">
          {cards?.map(({ label, Icon, value }, index) => (
            <div
              key={index}
              className="h-[148px]  rounded-md border border-[#E8E8E8] flex flex-col justify-between p-4"
            >
              <div className="flex justify-between">
                <p className="bg-gray-200 w-30 rounded-md p-2">
                  <Icon className="" />
                </p>
                <span></span>
              </div>

              <p className="text-[26px] text-[#1A1A40] font-bold my-2">
                {stats?.overview?.[value] ?? "0"}
              </p>
              <p className="text-[14px] text-[#4B5563]">{label}</p>
            </div>
          ))}
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-6 mb-5">
        <div className="w-full">
          <RevenueStats />
        </div>

        <div>
          <StudentChart />
        </div>
      </div>

      <TableComponent
        columns={columns}
        headerActions={headerActions}
        data={transactions || []}
        title="Transaction History"
      />
    </div>
  );
};

export default AdminDashboard;
