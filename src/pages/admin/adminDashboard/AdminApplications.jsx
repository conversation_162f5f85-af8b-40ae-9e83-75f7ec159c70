import React, { useState } from "react";
import ViewApplicationModal from "../components/applications/ViewApplicationModal";
import { useGetPendingTutorsQuery } from "@/redux/slices/admin/adminPendingTutorApiSlice";
import useGet from "@/hooks/useGet";
import { Loader } from "lucide-react";
import RejectFeedbackModalForm from "../components/applications/RejectFeedbackModalForm";
import ViewProfileModal from "../components/applications/ViewProfileModal";

const AdminApplications = () => {
	const [activeTab, setActiveTab] = useState("Application");
	const [selectedTutorId, setSelectedTutorId] = useState(null);
	const [isModalOpen, setIsModalOpen] = useState(false);

	const {
		data: applications,
		isLoading: gettingApplications,
		error,
	} = useGet(useGetPendingTutorsQuery);

	const tabs = [
		{ name: "Application" },
		{ name: "Profile" },
		{ name: "Reject" },
	];

	const showModal = (tutorId) => {
		setSelectedTutorId(tutorId);
		setIsModalOpen(true);
		setActiveTab("Application"); // Reset to first tab when opening modal
	};

	const closeModal = () => {
		setIsModalOpen(false);
		setSelectedTutorId(null);
	};

	const renderTabContent = () => {
		switch (activeTab) {
			case "Application":
				return (
					<ViewApplicationModal
						tutorId={selectedTutorId}
						onClose={closeModal}
						setActiveTab={setActiveTab}
					/>
				);
			case "Profile":
				return (
					<ViewProfileModal
						tutorId={selectedTutorId}
						onClose={closeModal}
						setActiveTab={setActiveTab}
					/>
				);
			case "Reject":
				return (
					<RejectFeedbackModalForm
						tutorId={selectedTutorId}
						onClose={closeModal}
						setActiveTab={setActiveTab}
					/>
				);
			default:
				return null;
		}
	};

	if (gettingApplications) return <Loader />;

	return (
		<div className="p-4">
			<div className="mb-6">
				<h1 className="text-2xl md:text-[26px] font-semibold text-[#1A1A40]">
					Applications
				</h1>
			</div>

			<div className="overflow-x-auto">
				<div className="min-w-full overflow-hidden">
					{/* Desktop Table */}
					<table className="w-full hidden md:table">
						<thead className="border-b text-sm sm:text-md border-[#E0E0E0]">
							<tr className="bg-[#F5F5F5]">
								<th className="p-4 text-left">Tutor's Name</th>
								<th className="p-4 text-left">Email address</th>
								<th className="p-4 text-left">Country</th>
								<th className="p-4 text-left">Subject You Teach</th>
								<th className="p-4 text-left">Actions</th>
							</tr>
						</thead>
						<tbody>
							{applications?.map((app, id) => (
								<tr
									key={id}
									className="border-b border-[#E0E0E0] hover:bg-gray-50"
								>
									<td className="p-4">{app.fullname}</td>
									<td className="p-4">{app.email}</td>
									<td className="p-4">{app.Country}</td>
									<td className="p-4">
										{app.teachingSubjects?.map((subject, index) => (
											<p key={index}>{subject.title}</p>
										))}
									</td>
									<td className="p-4">
										<button
											onClick={() => showModal(app.id)}
											className="px-4 py-2 rounded-md bg-primary text-white hover:bg-primary-dark transition-colors"
										>
											View
										</button>
									</td>
								</tr>
							))}
						</tbody>
					</table>

					{/* Mobile Cards */}
					<div className="md:hidden space-y-4">
						{applications?.map((app, id) => (
							<div
								key={id}
								className="border border-[#E0E0E0] rounded-lg p-4 shadow-sm"
							>
								<div className="mb-3">
									<h3 className="font-medium text-lg">{app.fullname}</h3>
									<p className="text-gray-600">{app.email}</p>
								</div>
								<div className="mb-3">
									<p className="font-medium">Country:</p>
									<p>{app.Country}</p>
								</div>
								<div className="mb-4">
									<p className="font-medium">Subjects:</p>
									{app.teachingSubjects?.map((subject, index) => (
										<p key={index} className="text-sm">
											{subject.title}
										</p>
									))}
								</div>
								<button
									onClick={() => showModal(app.id)}
									className="w-full py-2 rounded-md bg-primary text-white hover:bg-primary-dark transition-colors"
								>
									View Application
								</button>
							</div>
						))}
					</div>
				</div>
			</div>

			{/* Modal with Tabs */}
			{isModalOpen && selectedTutorId && (
				<div className="fixed inset-0 bg-black bg-opacity-50 h-screen flex items-center justify-center z-50 p-4">
					<div className="bg-white rounded-lg w-full max-w-4xl max-h-[99vh] overflow-auto">
						{/* Tab Navigation */}
						<div className="flex justify-around bg-[#1FC16B1A] p-3 sticky top-0 z-10">
							{tabs?.map(({ name }, index) => (
								<button
									key={index}
									className={`flex gap-2 items-center ${
										activeTab === name ? "font-semibold" : ""
									}`}
									onClick={() => setActiveTab(name)}
								>
									<span
										className={`block w-8 h-8 ${
											activeTab === name
												? "bg-primary text-white"
												: "bg-white text-primary"
										} flex justify-center items-center text-md sm:text-xl font-bold rounded-full border border-primary transition-all`}
									>
										{index + 1}
									</span>
									<span className="text-sm sm:text-lg text-secondary">
										{name}
									</span>
								</button>
							))}
						</div>

						{/* Tab Content */}
						<div className="p-4">{renderTabContent()}</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default AdminApplications;
