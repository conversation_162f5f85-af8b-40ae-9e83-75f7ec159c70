import React from "react";
import { CheckCircle2Icon, ClockIcon, XCircleIcon } from "lucide-react";
import TableComponent from "@/components/table/table";
import useGet from "@/hooks/useGet";
import { useGetRefundsQuery } from "@/redux/slices/admin/adminRefundApiSlice";

import searchIcon from "@/assets/images/adminDashboard/searchIcon.png";

const AdminRefundApproval = () => {
	const { data: refunds, isLoading: gettingPayoutStats } = useGet(
		useGetRefundsQuery,
		""
	);

	const getStatusIcon = (status) => {
		switch (status) {
			case "approved":
				return <CheckCircle2Icon className="text-green-500" size={20} />;
			case "rejected":
				return <XCircleIcon className="text-red-500" size={20} />;
			default:
				return <ClockIcon className="text-yellow-500" size={20} />;
		}
	};

	const refundRequests = [
		{
			id: "REF-1001",
			tutorName: "<PERSON>",
			studentName: "<PERSON>",
			subscriptionPlan: "Premium Monthly",
			wiseEmail: "<EMAIL>",
			amount: "$49.99",
			date: "2023-10-15",
			status: "pending",
			reason: "Dissatisfied with teaching quality",
		},
		{
			id: "REF-1002",
			tutorName: "David Kim",
			studentName: "Emma Wilson",
			subscriptionPlan: "Basic Quarterly",
			wiseEmail: "<EMAIL>",
			amount: "$89.97",
			date: "2023-10-10",
			status: "approved",
			reason: "Service not as described",
		},
		{
			id: "REF-1003",
			tutorName: "Maria Garcia",
			studentName: "James Peterson",
			subscriptionPlan: "Premium Annual",
			wiseEmail: "<EMAIL>",
			amount: "$399.00",
			date: "2023-10-05",
			status: "rejected",
			reason: "Changed mind after 30-day period",
		},
		{
			id: "REF-1004",
			tutorName: "Robert Smith",
			studentName: "Olivia Brown",
			subscriptionPlan: "Standard Monthly",
			wiseEmail: "<EMAIL>",
			amount: "$29.99",
			date: "2023-10-18",
			status: "pending",
			reason: "Technical issues with platform",
		},
	];

	return (
		<div>
			<div className="flex justify-between mb-6">
				<h1 className="text-2xl font-semibold">Refund Requests</h1>
				<div className="items-end gap-4 flex">
					<p className="text-[#1A1A40] font-medium text-lg p-2">Filter by:</p>

					<div className="flex border-2 focus:outline-primary rounded-xl">
						<img
							src={searchIcon}
							alt="LessonsIcon"
							className="w-7 mt-2 m-2 h-6 object-cover"
						/>{" "}
						<input
							type="text"
							className="p-2 focus:outline-none "
							placeholder="Enter student’s name"
						/>
					</div>
				</div>
			</div>
			<div className="bg-white rounded-lg shadow overflow-hidden">
				<table className="w-full">
					<thead className="bg-gray-50">
						<tr>
							<th className="p-4 text-left text-md font-medium text-gray-500">
								ID
							</th>
							<th className="p-4 text-left text-md font-medium text-gray-500">
								Tutor
							</th>
							<th className="p-4 text-left text-md font-medium text-gray-500">
								Student
							</th>
							<th className="p-4 text-left text-md font-medium text-gray-500">
								Subscription
							</th>
							<th className="p-4 text-left text-md font-medium text-gray-500">
								Wise Email
							</th>
							<th className="p-4 text-left text-md font-medium text-gray-500">
								Amount
							</th>
							<th className="p-4 text-left text-md font-medium text-gray-500">
								Date
							</th>
							<th className="p-4 text-left text-md font-medium text-gray-500">
								Status
							</th>
						</tr>
					</thead>
					<tbody className="divide-y divide-gray-200">
						{refundRequests.map((request) => (
							<tr key={request.id} className="hover:bg-gray-50">
								<td className="p-4 text-md">{request.id}</td>
								<td className="p-4 text-md font-medium">{request.tutorName}</td>
								<td className="p-4 text-md">{request.studentName}</td>
								<td className="p-4 text-md">{request.subscriptionPlan}</td>
								<td className="p-4 text-md ">{request.wiseEmail}</td>
								<td className="p-4 text-md font-medium">{request.amount}</td>
								<td className="p-4 text-md text-gray-500">{request.date}</td>
								<td className="p-4">
									<div className="flex items-center gap-2">
										{getStatusIcon(request.status)}
										<span
											className={`text-sm capitalize ${
												request.status === "approved"
													? "text-green-500"
													: request.status === "rejected"
													? "text-red-500"
													: "text-yellow-500"
											}`}
										>
											{request.status}
										</span>
									</div>
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</div>
		</div>
	);
};

export default AdminRefundApproval;
