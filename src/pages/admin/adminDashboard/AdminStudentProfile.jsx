import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/button/button";
import { BookIcon, ChevronLeft, Edit } from "lucide-react";
import Loader from "@/components/loader/loader";
import DeleteprofileConfirmationModal from "./components/DeleteprofileConfirmationModal";
import ChangeEmailForm from "./components/ChangeEmailForm";
import useGet from "@/hooks/useGet";
import { useGetStudentDetailsQuery } from "@/redux/slices/admin/adminStudentApiSlice";
import user from "@/assets/svgs/userVector.svg"


const AdminStudentProfile = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [showEditEmailModal, setShowEditEmailModal] = React.useState(false);

  const openModal = () => {
    setShowDeleteModal(true);
  };
  const closeModal = () => {
    setShowDeleteModal(false);
  };

  const { data: studentDetails, isLoading: gettingStudentDetails } = useGet(
    useGetStudentDetailsQuery,
    id,
    !!id
  );

  return (
    <div className="bg-white rounded-lg">

      {gettingStudentDetails && <Loader />}


      {/* Student Overview */}
      <div className="p-4 border rounded-md mb-8">
        <h1 className="text-xl my-4 font-bold text-[#1A1A40]">
          Student Details
        </h1>

        <div className="flex items-start justify-between">
          <div className="flex items-center gap-4">
            <img
              src={studentDetails?.image || user}
              alt={studentDetails?.name}
              className="w-14 h-14 rounded-full object-cover"
            />
            <div>
              <h2 className="text-xl font-semibold">{studentDetails?.name}</h2>
              <p className="text-gray-600">
                Current level: {studentDetails?.languages[0]?.level || "-"}
              </p>
            </div>
          </div>
          <div>
            <button
              onClick={openModal}
              className="bg-[#D00416] px-4 py-2 font-semibold text-white rounded-lg"
            >
              Delete Profile
            </button>
          </div>
        </div>

        <div className=" mt-6">
          <div className="flex space-x-6">
            <div className="flex space-x-1 text-center">
              <BookIcon size={20} />
              <p className="font-medium">{studentDetails?.lessons}</p>
              <p className="text-gray-600">Lessons</p>
            </div>
            <div className="flex space-x-1 text-center">
              <BookIcon size={20} />{" "}
              <p className="font-medium">{studentDetails?.tutors}</p>
              <p className="text-gray-600">Tutors</p>
            </div>
            <div className="flex space-x-1 text-center">
              <BookIcon size={20} />
              <p className="text-gray-600">Rating</p>
              <p className="font-medium">{studentDetails?.rating}</p>
            </div>
          </div>

          {/* Languages */}
          <div className="flex">
            <div className="flex flex-wrap gap-2 mt-2">
              <h3 className="font-medium text-[#1A1A40]">Languages:</h3>

              {studentDetails?.languages.map((lang, index) => (
                <span
                  key={index}
                  className="px-1 text-gray-700 rounded-full text-sm"
                >
                  {lang.name} ({lang.level})
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Personal Information */}
      <div className="p-4 border rounded-md">
        <h2 className="text-xl font-semibold mb-4">Personal Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <p className="text-gray-600 text-sm">Full name</p>
            <p className="font-medium">{studentDetails?.fullname}</p>
          </div>

          <div>
            <p className="text-gray-600 text-sm">Email address</p>
            <div className="flex items-center gap-1" >
              <p className="font-medium">{studentDetails?.email}</p>
              <Edit onClick={() => setShowEditEmailModal(true)} size={16} />
            </div>
          </div>


            <div>
              <p className="text-gray-600 text-sm">Country of residence</p>
              <p className="font-medium">{studentDetails?.country}</p>
            </div>



            <div>
              <p className="text-gray-600 text-sm">Phone number</p>
              <p className="font-medium">{studentDetails?.phone}</p>
            </div>
        </div>
      </div>
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <DeleteprofileConfirmationModal
            onCancel={() => setShowDeleteModal(false)}
          />
        </div>
      )}

      {showEditEmailModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <ChangeEmailForm onClose={() => setShowEditEmailModal(false)} />
        </div>
      )}
    </div>
  );
};

export default AdminStudentProfile;
