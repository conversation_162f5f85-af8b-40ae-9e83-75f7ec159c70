import React from "react";
import TableComponent from "@/components/table/table";
import useGet from "@/hooks/useGet";
import {
  useGetPayoutsQuery,
  useGetPayoutStatsQuery
} from "@/redux/slices/admin/adminPayoutApiSlice";
import Loader from "@/components/loader/loader";
import TotalClasses from "@/assets/svgs/studentDashboard/totalClasses";
import TotalNoOfTutorsIcon from "@/assets/svgs/studentDashboard/totalNoOfTutorsIcon";

const AdminPayouts = () => {
  const { data: payouts, isLoading: gettingPayouts } = useGet(
    useGetPayoutsQuery,
    ""
  );

  const { data: payoutStats, isLoading: gettingPayoutStats } = useGet(
    useGetPayoutStatsQuery,
    ""
  );

  const cards = [
    {
      Icon: TotalClasses,
      value: "successfulPayments",
      label: "Total no of successful payments"
    },

    {
      Icon: TotalNoOfTutorsIcon,
      value: "failedPayments",
      label: "Total no of failed payments"
    },
    {
      Icon: TotalNoOfTutorsIcon,
      value: "totalAttempts",
      label: "Total no of payment attempts"
    },
    {
      Icon: TotalNoOfTutorsIcon,
      value: "failureRate",
      label: "Payment failure rate"
    }
  ];

  const columns = [
    { header: "Transaction ID", accessor: "transactionId" },
    { header: "Tutor's Name", accessor: "tutorName" },
    { header: "Wise email address", accessor: "wiseEmail" },
    { header: "Payout method", accessor: "payoutMethod" },
    { header: "Date", accessor: "date" },
    { header: "Time", accessor: "time" },
    { header: "Amount", accessor: "amount" },
    {
      header: "Status",
      accessor: "status",
      render: (row) => (
        <span
          className={`px-2 py-1 rounded-full text-sm ${getStatusStyle(
            row.status
          )}`}
        >
          {row.status}
        </span>
      )
    },
    {
      header: "",
      accessor: "",
      render: (row) => (
        <button
          onClick={() => handleProcessPayout(transaction.id)}
          className="bg-primary text-white px-3 py-2 rounded-lg"
        >
          Process Payout
        </button>
      )
    }
  ];

  // Function to get status styling based on status value
  const getStatusStyle = (status) => {
    switch (status.toLowerCase()) {
      case "paid":
        return "bg-[#ECFDF3] text-[#037847]";
      case "pending":
        return "bg-[#FFDB431A] text-[#DFB400]";
      case "failed":
        return "bg-[#FB37481A] text-[#D00416]";
      case "refunded":
        return "bg-[#4D00FF1A] text-[#3C00C6]";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const headerActions = <></>;

  return (
    <div className="w-full">
      {gettingPayoutStats && <Loader />}

      <p className="text-[26px] font-semibold text-[#1A1A40]">Payouts</p>

      <div className="my-4">
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
          {cards?.map(({ label, Icon, value }, index) => (
            <div
              key={index}
              className="h-[148px] rounded-md border border-[#E8E8E8] flex flex-col justify-between p-4"
            >
              <Icon />
              <p className="text-[26px] text-[#1A1A40] font-bold my-2">
                {payoutStats?.paymentMetrics?.[value] ?? "0"}
              </p>
              <p className="text-[14px] text-[#4B5563]">{label}</p>
            </div>
          ))}
        </div>
      </div>

      {gettingPayouts && <Loader />}

      <TableComponent
        columns={columns}
        // headerActions={headerActions}
        data={payouts || []}
        title=""
      />
    </div>
  );
};

export default AdminPayouts;
