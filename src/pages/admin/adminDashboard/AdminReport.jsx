import React from "react";
import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axi<PERSON>, YAxi<PERSON>, ResponsiveContainer } from "recharts";
import { Download, ChevronDown } from "lucide-react";
import MainStats from "../components/report/MainStats";
import ReportHeader from "../components/report/ReportHeader";

const AdminReport = () => {
	const data = [
		{ month: "Jan", revenue: 20, commission: 15, processingFee: 8 },
		{ month: "Feb", revenue: 20, commission: 16, processingFee: 10 },
		{ month: "Mar", revenue: 20, commission: 12, processingFee: 5 },
		{ month: "Apr", revenue: 20, commission: 15, processingFee: 8 },
		{ month: "May", revenue: 20, commission: 12, processingFee: 6 },
		{ month: "Jun", revenue: 20, commission: 15, processingFee: 8 },
		{ month: "Jul", revenue: 20, commission: 12, processingFee: 6 },
		{ month: "Aug", revenue: 20, commission: 15, processingFee: 8 },
		{ month: "Sep", revenue: 20, commission: 12, processingFee: 6 },
		{ month: "Oct", revenue: 20, commission: 15, processingFee: 8 },
		{ month: "Nov", revenue: 20, commission: 12, processingFee: 6 },
		{ month: "Dec", revenue: 20, commission: 15, processingFee: 8 },
	];

	return (
		<>
			<div className="pb-4">
				<MainStats />
			</div>

			<div className="bg-white rounded-lg border border-gray-200 p-6 w-full">
				<div className="mb-6">
					<ReportHeader />
				</div>

				<div className="h-80">
					<ResponsiveContainer width="100%" height="100%">
						<BarChart
							data={data}
							margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
							barCategoryGap="20%"
						>
							<XAxis
								dataKey="month"
								axisLine={false}
								tickLine={false}
								tick={{ fontSize: 12, fill: "#6B7280" }}
							/>
							<YAxis
								axisLine={false}
								tickLine={false}
								tick={{ fontSize: 12, fill: "#6B7280" }}
								domain={[0, 100]}
								ticks={[0, 20, 40, 60, 80, 100]}
							/>
							<Bar
								dataKey="revenue"
								fill="#10B981"
								radius={[2, 2, 0, 0]}
								barSize={8}
							/>
							<Bar
								dataKey="commission"
								fill="#6366F1"
								radius={[2, 2, 0, 0]}
								barSize={8}
							/>
							<Bar
								dataKey="processingFee"
								fill="#F59E0B"
								radius={[2, 2, 0, 0]}
								barSize={8}
							/>
						</BarChart>
					</ResponsiveContainer>
				</div>
			</div>
		</>
	);
};

export default AdminReport;
