import React from "react";
import { But<PERSON> } from "@/components/button/button";
import { Clock1 } from "lucide-react";
import {
  useGetAllStudentsQuery,
  useGetStudentDetailsQuery
} from "@/redux/slices/admin/adminStudentApiSlice";
import useGet from "@/hooks/useGet";
import Loader from "@/components/loader/loader";
import user from "@/assets/svgs/userVector.svg";
import { useNavigate } from "react-router-dom";

import LessonsIcon from "@/assets/images/adminDashboard/lessonsIcon.png";
import TutorsIcon from "@/assets/images/adminDashboard/tutorsIcon.png";
import RatingIcon from "@/assets/images/adminDashboard/ratingIcon.png";

const AdminStudentsPage = () => {
  const navigate = useNavigate();

  const { data: students, isLoading: gettingStudents } = useGet(
    useGetAllStudentsQuery,
    ""
  );

  return (
    <div>
      {gettingStudents && <Loader />}

      <h1 className="font-semibold text-2xl mb-5">Students</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {students?.map((student) => (
          <div className="w-auto bg-white border rounded-lg shadow-md p-4 justify-between hover:shadow-lg transition-shadow duration-200">
            <div className="text-[#1A1A40] flex items-center gap-4">
              <img
                src={student.image || user}
                alt={student.fullname}
                className="w-14 h-14 rounded-full object-cover"
              />
              <div>
                <p className="text-[18px] font-medium">{student.fullname}</p>
                <p className="text-gray-600">{student.currentLevel}</p>
              </div>
            </div>

            <hr className="py-1 my-2" />

            <div>
              <div className="flex items-center gap-2 mb-2">
                <div className="flex space-x-2 pr-4">
                  <img
                    src={LessonsIcon}
                    alt="LessonsIcon"
                    className="w-7 h-6 object-cover"
                  />
                  <p className="text-gray-600">{student.lessons} Lessons</p>
                </div>
                <div className="flex space-x-2 pr-4">
                  <img
                    src={TutorsIcon}
                    alt="LessonsIcon"
                    className="w-7 h-6 object-cover"
                  />{" "}
                  <p className="text-gray-600">{student.tutors} Tutors</p>
                </div>
                <div className="flex space-x-2">
                  <img
                    src={RatingIcon}
                    alt="LessonsIcon"
                    className="w-7 h-6 object-cover"
                  />{" "}
                  <p className="text-gray-600">Rating: {student.rating}/100</p>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 items-center mt-2">
                <p className="text-[#1A1A40] text-md">Languages: </p>

                {student?.languages?.map((language, index) => (
                  <span
                    key={index}
                    className="px-2 text-gray-700 rounded-full text-sm"
                  >
                    {language?.name} ({language?.level})
                  </span>
                ))}
              </div>
            </div>

            <div className="mt-4 items-center">
              <Button
                onClick={() => navigate(`/admin/students/${student.id}`)}
                className="h-50 w-full"
              >
                View Profile
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AdminStudentsPage;
