import React from "react";
import { useForm } from "react-hook-form";
import { X } from "lucide-react";
import { Button } from "@/components/button/button";

const ChangeEmailForm = ({
  currentEmail = "<EMAIL>",
  onClose,
  onSubmit
}) => {
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting }
  } = useForm({
    defaultValues: {
      currentEmail: currentEmail,
      newEmail: "",
      confirmEmail: ""
    }
  });

  const newEmail = watch("newEmail");

  const onSubmitForm = async (data) => {
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log("Form submitted:", data);
      if (onSubmit) {
        onSubmit(data);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  const validateEmailMatch = (value) => {
    return value === newEmail || "Email addresses do not match";
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-[493px]">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl text-[#1A1A40] font-semibold">
              Change email
            </h2>
            <button onClick={onClose} className="transition-colors">
              <X size={24} />
            </button>
          </div>

          {/* Form */}
          <div className="space-y-6">
            {/* Current Email */}
            <div>
              <label className="block text-md font-medium  mb-2">
                Current email address
              </label>
              <input
                type="email"
                {...register("currentEmail")}
                className="w-full px-3 py-3 border border-gray-300 rounded-md bg-gray-50 text-gray-500 cursor-not-allowed"
                readOnly
              />
            </div>

            {/* New Email */}
            <div>
              <label className="block text-md font-medium  mb-2">
                New email address
              </label>
              <input
                type="email"
                {...register("newEmail", {
                  required: "New email address is required",
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: "Invalid email address"
                  }
                })}
                placeholder="Enter your new email address"
                className={`w-full px-3 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                  errors.newEmail ? "border-red-300" : "border-gray-300"
                }`}
              />
              {errors.newEmail && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.newEmail.message}
                </p>
              )}
            </div>

            {/* Confirm Email */}
            <div>
              <label className="block text-md font-medium  mb-2">
                Confirm new email address
              </label>
              <input
                type="email"
                {...register("confirmEmail", {
                  required: "Please confirm your new email address",
                  validate: validateEmailMatch
                })}
                placeholder="Enter your new email address"
                className={`w-full px-3 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                  errors.confirmEmail ? "border-red-300" : "border-gray-300"
                }`}
              />
              {errors.confirmEmail && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.confirmEmail.message}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <Button
              onClick={handleSubmit(onSubmitForm)}
              disabled={isSubmitting}
              className={`w-full py-3 px-4  h-[50px] rounded-md text-white font-medium transition-colors ${
                isSubmitting ? " bg-gray-400 cursor-not-allowed" : "bg-primary "
              }`}
            >
              {isSubmitting ? "Saving..." : "Save change"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChangeEmailForm;
