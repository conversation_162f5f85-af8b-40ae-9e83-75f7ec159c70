import React from "react";

const DeleteprofileConfirmationModal = ({ onConfirm, onCancel }) => {
  return (
    <div onClick={onCancel}>
      <div className="bg-white rounded-xl p-6 w-full max-w-2xl text-center">
        <div className="flex flex-col items-center p-6">
          <h2 className="text-2xl font-bold mb-2">
            Are you sure you want to delete the user profile ?
          </h2>

          <p className="my-4 text-lg">
            This will permanently remove the user account. <br /> This action
            cannot be undone
          </p>
          <div className="flex justify-center gap-4 mb-4">
            <button
              className="bg-[#D00416] text-white py-2 px-6 font-bold rounded-md hover:bg-red-600"
              onClick={onConfirm}
            >
              Yes, Delete
            </button>
            <button
              className="border border-gray-300 py-2 px-6 font-bold rounded-md hover:bg-gray-50"
              onClick={onCancel}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeleteprofileConfirmationModal;
