import React, { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import DashboardIcon from "@/assets/svgs/sidebar/dashboardIcon";
import LessonsIcon from "@/assets/svgs/sidebar/lessonsIcon";
import MessageIcon from "@/assets/svgs/sidebar/messageIcon";
import ReviewsIcon from "@/assets/svgs/sidebar/reviewsIcon";
import SettingsIcon from "@/assets/svgs/sidebar/settingsIcon";

import BookingIcon from "@/assets/svgs/sidebar/bookingIcon";
import HistoryIcon from "@/assets/svgs/sidebar/historyIcon";
import ReportsIcon from "@/assets/svgs/sidebar/reportsIcon";
import ApplicationIcon from "@/assets/svgs/sidebar/applicationIcon";
import StudentsIcon from "@/assets/svgs/sidebar/studentsIcon";
import TutorsIcon from "@/assets/svgs/sidebar/tutorsIcon";

import PayoutsIcon from "@/assets/svgs/sidebar/payoutsIcon";
import RefundIcon from "@/assets/svgs/sidebar/refundIcon";
import SubaccountsIcon from "@/assets/svgs/sidebar/subaccountsIcon";

import LogoutIcon from "@/assets/svgs/sidebar/logoutIcon";

const AdminSidebarLinks = () => {
	const navigate = useNavigate();
	const location = useLocation();
	const [showLogoutModal, setShowLogoutModal] = useState(false);

	const navItems = [
		{
			path: "/admin/dashboard",
			name: "Dashboard",
			Icon: DashboardIcon,
		},
		{
			path: "/admin/students",
			name: "Students",
			Icon: StudentsIcon,
		},
		{
			path: "/admin/tutors",
			name: "Tutors",
			Icon: TutorsIcon,
		},
		{
			path: "/admin/history",
			name: "History",
			Icon: HistoryIcon,
		},
		{
			path: "/admin/bookings",
			name: "Bookings",
			Icon: BookingIcon,
		},
		{
			path: "/admin/payouts",
			name: "Payouts",
			Icon: PayoutsIcon,
		},

		{
			path: "/admin/refund-approval",
			name: "Refund Approal",
			Icon: RefundIcon,
		},

		{
			path: "/admin/subaccounts",
			name: "Subaccounts",
			Icon: SubaccountsIcon,
		},
		{
			path: "/admin/applications",
			name: "Application",
			Icon: ApplicationIcon,
		},
		{
			path: "/admin/reports",
			name: "Financial Reports",
			Icon: ReportsIcon,
		},
		{
			path: "/logout",
			name: "Logout",
			Icon: LogoutIcon,
			isLogout: true,
		},
	];

	const isActive = (path) => {
		if (path === "/admin/dashboard") {
			return location.pathname === path;
		}
	};

	const handleLogoutClick = (e, item) => {
		e.preventDefault();
		if (item.isLogout) {
			setShowLogoutModal(true);
		} else {
			navigate(item.path);
		}
	};

	const handleLogout = () => {
		// Perform actual logout logic here
		navigate("/logout");
		setShowLogoutModal(false);
	};

	return (
		<div className="flex flex-col  space-y-2">
			{navItems.map((item, index) => (
				<div
					key={index}
					className={`p-2 flex flex-row items-center hover:bg-primary text-[#1A1A40] cursor-pointer hover:text-white transition rounded-md ${
						location.pathname.includes(item.path) && !item.isLogout
							? "bg-primary text-white"
							: ""
					}`}
					onClick={() =>
						item.isLogout ? dispatch(logOut()) : navigate(item.path)
					}
				>
					<item.Icon
						stroke={
							location.pathname.includes(item.path) && !item.isLogout
								? "#ffffff"
								: item.isLogout
								? "#D00416"
								: "#1A1A40"
						}
					/>

					<button
						className={`md:text-lg p-2 rounded-md font-bold ${
							location.pathname.includes(item.path) && !item.isLogout
								? "text-white"
								: item.isLogout
								? "text-[#D00416]"
								: ""
						}`}
					>
						{item.name}
					</button>
				</div>
			))}
		</div>
	);
};

export default AdminSidebarLinks;
