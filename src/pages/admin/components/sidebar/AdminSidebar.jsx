import React, { useEffect, useRef } from "react";
import AdminSidebarLinks from "./AdminSidebarLinks";

const AdminSidebar = ({ isOpen, toggleSidebar }) => {
	const sidebarRef = useRef(null);

	useEffect(() => {
		const handleClickOutside = (event) => {
			if (
				sidebarRef.current &&
				!sidebarRef.current.contains(event.target) &&
				isOpen
			) {
				toggleSidebar(); // Close the sidebar
			}
		};

		document.addEventListener("mousedown", handleClickOutside);

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [isOpen, toggleSidebar]);

	return (
		<div
			ref={sidebarRef}
			className={`fixed lg:relative z-50 lg:z-auto lg:block w-[256px] shadow-lg p-3 overflow-auto h-full bg-white transition-all duration-300 ${
				isOpen ? "left-0" : "-left-full"
			} lg:left-0`}
		>
			<div className="w-[250px]">
				<AdminSidebarLinks />
			</div>
		</div>
	);
};

export default AdminSidebar;
