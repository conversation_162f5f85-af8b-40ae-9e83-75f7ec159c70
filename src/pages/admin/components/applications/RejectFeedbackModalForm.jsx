import React, { useState } from "react";
import { But<PERSON> } from "@/components/button/button";
import { useRejectPendingTutorMutation } from "@/redux/slices/admin/adminPendingTutorApiSlice";
import { toast } from "react-toastify";

const RejectFeedbackModalForm = ({ onClose, tutorId, setActiveTab }) => {
	const [rejectionReason, setRejectionReason] = useState("");
	const [rejectTutor, { isLoading: isSubmitting }] =
		useRejectPendingTutorMutation();

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (!rejectionReason.trim()) {
			toast.error("Please provide a rejection reason");
			return;
		}

		try {
			await rejectTutor({
				id: tutorId,
				rejectionReason: rejectionReason.trim(),
			}).unwrap();
			toast.success("Tu<PERSON> rejected successfully");
			onClose();
		} catch (error) {
			toast.error(error.data?.message || "Failed to reject tutor");
		}
	};

	return (
		<div className="bg-white p-2 sm:p-6 rounded-md flex flex-col h-full">
			<div className="flex justify-between items-center mb-6">
				<h1 className="text-md sm:text-xl font-semibold text-[#1A1A40]">
					Reject Application
				</h1>
				<button
					onClick={() => setActiveTab("Profile")}
					className="text-gray-500 hover:text-gray-700"
				>
					Back to Profile
				</button>
			</div>

			<div className="flex-1 overflow-y-auto">
				<div className="space-y-6">
					<div>
						<label
							htmlFor="rejectionReason"
							className="block text-sm sm:text-md font-medium text-[#1A1A40] mb-2"
						>
							Please provide detailed feedback for rejection
						</label>
						<textarea
							id="rejectionReason"
							value={rejectionReason}
							onChange={(e) => setRejectionReason(e.target.value)}
							rows={10}
							className="w-full text-[#4B5563] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary resize-none"
							placeholder="Explain why the application is being rejected..."
							required
						/>
						<p className="mt-1 text-sm text-gray-500">
							This feedback will be sent to the tutor
						</p>
					</div>
				</div>
			</div>

			<div className="mt-6 flex justify-end space-x-3 border-t pt-4">
				<Button
					type="button"
					onClick={() => setActiveTab("Profile")}
					variant="outline"
					disabled={isSubmitting}
				>
					Cancel
				</Button>
				<Button
					type="submit"
					onClick={handleSubmit}
					disabled={isSubmitting || !rejectionReason.trim()}
					isLoading={isSubmitting}
				>
					Submit Rejection
				</Button>
			</div>
		</div>
	);
};

export default RejectFeedbackModalForm;
