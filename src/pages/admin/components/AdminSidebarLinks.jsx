import React, { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import DashboardIcon from "@/assets/svgs/sidebar/dashboardIcon";
import LessonsIcon from "@/assets/svgs/sidebar/lessonsIcon";
import MessageIcon from "@/assets/svgs/sidebar/messageIcon";
import ReviewsIcon from "@/assets/svgs/sidebar/reviewsIcon";
import SettingsIcon from "@/assets/svgs/sidebar/settingsIcon";
import LogoutIcon from "@/assets/svgs/sidebar/logoutIcon";

const AdminSidebarLinks = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [showLogoutModal, setShowLogoutModal] = useState(false);

  const navItems = [
    {
      path: "/admin/dashboard",
      name: "Dashboard",
      Icon: DashboardIcon
    },
    {
      path: "/admin/students",
      name: "Students",
      Icon: DashboardIcon
    },
    {
      path: "/admin/tutors",
      name: "<PERSON><PERSON>",
      Icon: DashboardIcon
    },

    {
      path: "/admin/history",
      name: "History",
      Icon: MessageIcon
    },
    {
      path: "/admin/bookings",
      name: "Bookings",
      Icon: MessageIcon
    },
    {
      path: "/admin/payouts",
      name: "Payouts",
      Icon: MessageIcon
    },
    {
      path: "/admin/refund-approval",
      name: "Refund Approal",
      Icon: ReviewsIcon
    },
    {
      path: "/admin/notifications",
      name: "Notifications",
      Icon: LessonsIcon
    },
    {
      path: "/admin/subaccounts",
      name: "Subaccounts",
      Icon: LessonsIcon
    },
    {
      path: "/logout",
      name: "Logout",
      Icon: LogoutIcon,
      isLogout: true
    }
  ];

  const isActive = (path) => {
    if (path === "/admin/dashboard") {
      return location.pathname === path;
    }
    if (path === "/tutor/my-lessons") {
      return (
        location.pathname.startsWith("/tutor/my-lessons") ||
        location.pathname.startsWith("/tutor/students") ||
        location.pathname.startsWith("/tutor/calendar")
      );
    }
    return location.pathname.startsWith(path);
  };

  const handleLogoutClick = (e, item) => {
    e.preventDefault();
    if (item.isLogout) {
      setShowLogoutModal(true);
    } else {
      navigate(item.path);
    }
  };

  const handleLogout = () => {
    // Perform actual logout logic here
    navigate("/logout");
    setShowLogoutModal(false);
  };

  return (
    <div className="flex flex-col  space-y-2">
      {navItems.map((item, index) => (
        <div
          key={index}
          className={`p-2 flex flex-row items-center hover:bg-primary text-[#1A1A40] cursor-pointer hover:text-white transition rounded-md ${
            location.pathname.includes(item.path) && !item.isLogout
              ? "bg-primary text-white"
              : ""
          }`}
          onClick={() =>
            item.isLogout ? dispatch(logOut()) : navigate(item.path)
          }
        >
          <item.Icon
            stroke={
              location.pathname.includes(item.path) && !item.isLogout
                ? "#ffffff"
                : item.isLogout
                ? "#D00416"
                : "#1A1A40"
            }
          />

          <button
            className={`md:text-lg p-2 rounded-md font-bold ${
              location.pathname.includes(item.path) && !item.isLogout
                ? "text-white"
                : item.isLogout
                ? "text-[#D00416]"
                : ""
            }`}
          >
            {item.name}
          </button>
        </div>
      ))}
    </div>
  );
};

export default AdminSidebarLinks;
