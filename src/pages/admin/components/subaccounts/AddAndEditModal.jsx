import React, { useState } from "react";
import { X } from "lucide-react";
import { useForm } from "react-hook-form";
import { useCreateSubAdminsMutation } from "@/redux/slices/admin/AdminSubAccountsApiSlice";
import usePost from "@/hooks/usePost";

const permissionsList = [
  "manage_users",
  "view_users",
  "suspend_users",
  "delete_users",
  "approve_tutors",
  "reject_tutors",
  "manage_tutors",
  "view_tutor_details",
  "moderate_content",
  "flag_content",
  "remove_content",
  "manage_payments",
  "view_financial_reports",
  "process_refunds",
  "manage_subscriptions",
  "system_settings",
  "view_analytics",
  "manage_admins",
  "audit_logs",
  "manage_support",
  "send_notifications",
  "manage_announcements",
  "super_admin"
];

const AddAndEditModal = ({ onClose }) => {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm({
    defaultValues: {
      firstname: "",
      lastname: "",
      email: "",
      password: "",
      department: "",
      adminLevel: 1,
      permissions: []
    }
  });

  const { handlePost: handleCreateAdmin, isLoading: creatingAdmin } = usePost(
    useCreateSubAdminsMutation
  );

  const permissions = watch("permissions");

  const handlePermissionChange = (permission) => {
    const currentPermissions = permissions || [];
    const updatedPermissions = currentPermissions.includes(permission)
      ? currentPermissions.filter((p) => p !== permission)
      : [...currentPermissions, permission];
    setValue("permissions", updatedPermissions, { shouldValidate: true });
  };

  const onSubmit = async (data) => {
    const res = await handleCreateAdmin(data).unwrap();

    if (res) {
      onClose();
    }
  };

  return (
    <div className="bg-white p-6 rounded-md w-[700px] max-h-[90vh] overflow-y-auto">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-semibold text-[#1A1A40]">
          Add New Member
        </h1>
        <button onClick={onClose} aria-label="Close modal">
          <X size={24} />
        </button>
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="pb-2 mt-4">
          <label htmlFor="firstname" className="text-md">
            First Name
          </label>
          <input
            type="text"
            {...register("firstname", { required: "First name is required" })}
            placeholder="Enter First Name"
            className="w-full px-4 py-2 mt-2 border rounded-md outline-primary"
          />
          {errors.firstname && (
            <p className="text-red-500 text-sm">{errors.firstname.message}</p>
          )}
        </div>

        <div className="pb-2 mt-4">
          <label htmlFor="lastname" className="text-md">
            Last Name
          </label>
          <input
            type="text"
            {...register("lastname", { required: "Last name is required" })}
            placeholder="Enter Last Name"
            className="w-full px-4 py-2 mt-2 border rounded-md outline-primary"
          />
          {errors.lastname && (
            <p className="text-red-500 text-sm">{errors.lastname.message}</p>
          )}
        </div>

        <div className="pb-2 mt-4">
          <label htmlFor="email" className="text-md">
            Email
          </label>
          <input
            type="email"
            {...register("email", {
              required: "Email is required",
              pattern: {
                value: /\S+@\S+\.\S+/,
                message: "Invalid email format"
              }
            })}
            placeholder="Enter Email"
            className="w-full px-4 py-2 mt-2 border rounded-md outline-primary"
          />
          {errors.email && (
            <p className="text-red-500 text-sm">{errors.email.message}</p>
          )}
        </div>

        <div className="pb-2 mt-4">
          <label htmlFor="password" className="text-md">
            Password
          </label>
          <input
            type="password"
            {...register("password", { required: "Password is required" })}
            placeholder="Enter Password"
            className="w-full px-4 py-2 mt-2 border rounded-md outline-primary"
          />
          {errors.password && (
            <p className="text-red-500 text-sm">{errors.password.message}</p>
          )}
        </div>

        <div className="pb-2 mt-4">
          <label htmlFor="department" className="text-md">
            Department or Role
          </label>
          <input
            type="text"
            {...register("department", { required: "Department is required" })}
            placeholder="Enter Department or Role"
            className="w-full px-4 py-2 mt-2 border rounded-md outline-primary"
          />
          {errors.department && (
            <p className="text-red-500 text-sm">{errors.department.message}</p>
          )}
        </div>

        <div className="pb-2 mt-4">
          <label htmlFor="adminLevel" className="text-md">
            Admin Level (1-5)
          </label>
          <input
            type="number"
            {...register("adminLevel", {
              required: "Admin level is required",
              min: { value: 1, message: "Admin level must be between 1 and 5" },
              max: { value: 5, message: "Admin level must be between 1 and 5" }
            })}
            placeholder="Enter Admin Level"
            min="1"
            max="5"
            className="w-full px-4 py-2 mt-2 border rounded-md outline-primary"
          />
          {errors.adminLevel && (
            <p className="text-red-500 text-sm">{errors.adminLevel.message}</p>
          )}
        </div>

        <div className="pb-2 mt-4">
          <label className="text-md">Permissions (Access to)</label>
          <div className="grid grid-cols-3 gap-2 mt-2">
            {permissionsList.map((permission) => (
              <label key={permission} className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={permissions.includes(permission)}
                  onChange={() => handlePermissionChange(permission)}
                  className="h-4 w-4"
                />
                <span className="text-sm capitalize">
                  {permission.replace(/_/g, " ")}
                </span>
              </label>
            ))}
          </div>
        </div>

        <div className="flex justify-end gap-4 mt-6">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border rounded-md text-gray-600 hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={creatingAdmin}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark disabled:opacity-50"
          >
            {creatingAdmin ? "Saving..." : "Create"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddAndEditModal;
