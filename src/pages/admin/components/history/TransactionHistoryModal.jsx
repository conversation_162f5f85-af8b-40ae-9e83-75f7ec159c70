import React, { useState, useRef, useEffect } from "react";
import { Download, ChevronDown } from "lucide-react";

const SortModal = ({ closeModal }) => {
	return (
		<div className="absolute right-0 mt-2 w-full bg-white border rounded-lg shadow-lg z-50">
			<div className="flex text-sm flex-col p-2">
				<button
					className="px-4 py-2 text-left hover:bg-gray-100 rounded-md"
					onClick={closeModal}
				>
					Subscription
				</button>
				<button
					className="px-4 py-2 text-left hover:bg-gray-100 rounded-md"
					onClick={closeModal}
				>
					Single lesson
				</button>
				<button
					className="px-4 py-2 text-left hover:bg-gray-100 rounded-md"
					onClick={closeModal}
				>
					Free trial
				</button>
				<button
					className="px-4 py-2 text-left hover:bg-gray-100 rounded-md"
					onClick={closeModal}
				>
					Free trial
				</button>
				<button
					className="px-4 py-2 text-left hover:bg-gray-100 rounded-md"
					onClick={closeModal}
				>
					Paid
				</button>
				<button
					className="px-4 py-2 text-left hover:bg-gray-100 rounded-md"
					onClick={closeModal}
				>
					Pending
				</button>
				<button
					className="px-4 py-2 text-left hover:bg-gray-100 rounded-md"
					onClick={closeModal}
				>
					Failed
				</button>
				<button
					className="px-4 py-2 text-left hover:bg-gray-100 rounded-md"
					onClick={closeModal}
				>
					Refund
				</button>
			</div>
		</div>
	);
};

const TransactionHistoryModal = () => {
	const [isSortModalOpen, setIsSortModalOpen] = useState(false);
	const modalRef = useRef(null);
	const buttonRef = useRef(null);

	const openSortModal = () => setIsSortModalOpen(true);
	const closeSortModal = () => setIsSortModalOpen(false);

	useEffect(() => {
		const handleClickOutside = (event) => {
			if (
				isSortModalOpen &&
				!modalRef.current?.contains(event.target) &&
				!buttonRef.current?.contains(event.target)
			) {
				closeSortModal();
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, [isSortModalOpen]);

	return (
		<div className="relative">
			<div className="flex text-[#1A1A40] items-center justify-between">
				<div className="relative">
					<button
						ref={buttonRef}
						className="flex items-center  gap-2 px-3 py-2 border border-gray-300 rounded-md text-sm font-medium "
						onClick={openSortModal}
					>
						<span>All Transactions</span>
						<ChevronDown size={16} />
					</button>

					{isSortModalOpen && (
						<div ref={modalRef}>
							<SortModal closeModal={closeSortModal} />
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default TransactionHistoryModal;
