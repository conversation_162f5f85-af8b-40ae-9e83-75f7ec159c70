import React, { useState, useRef, useEffect } from "react";
import { Calendar } from "react-date-range";
import "react-date-range/dist/styles.css"; // main style file
import "react-date-range/dist/theme/default.css"; // theme css file
import { format } from "date-fns";

const DateRangePicker = () => {
	const [isOpen, setIsOpen] = useState(false);
	const [dateRange, setDateRange] = useState({
		startDate: new Date(),
		endDate: new Date(),
	});
	const pickerRef = useRef(null);

	// Close calendar when clicking outside
	useEffect(() => {
		const handleClickOutside = (event) => {
			if (pickerRef.current && !pickerRef.current.contains(event.target)) {
				setIsOpen(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, []);

	const handleSelect = (ranges) => {
		setDateRange({
			startDate: ranges.selection.startDate,
			endDate: ranges.selection.endDate,
		});
	};

	const toggleCalendar = () => setIsOpen(!isOpen);

	return (
		<div className="relative" ref={pickerRef}>
			<button
				onClick={toggleCalendar}
				className="p-2 border rounded-lg hover:bg-gray-50 transition-colors"
			>
				From: {format(dateRange.startDate, "dd/MM/yyyy")} To:{" "}
				{format(dateRange.endDate, "dd/MM/yyyy")}
			</button>

			{isOpen && (
				<div className="absolute z-50 mt-1 bg-white border rounded-lg shadow-lg">
					<Calendar
						editableDateInputs={true}
						onChange={handleSelect}
						moveRangeOnFirstSelection={false}
						ranges={[
							{
								startDate: dateRange.startDate,
								endDate: dateRange.endDate,
								key: "selection",
							},
						]}
						rangeColors={["#3B82F6"]}
						className="border-0"
					/>
					<div className="flex justify-between p-2 border-t">
						<button
							onClick={() => setIsOpen(false)}
							className="px-4 py-1 text-gray-600 hover:text-gray-800"
						>
							Cancel
						</button>
						<button
							onClick={() => setIsOpen(false)}
							className="px-4 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
						>
							Apply
						</button>
					</div>
				</div>
			)}
		</div>
	);
};

export default DateRangePicker;
