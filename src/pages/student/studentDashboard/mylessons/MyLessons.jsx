import React from "react";
import LessonsContainer from "./components/LessonsContainer";
import { useNavigate, useLocation, Link } from "react-router-dom";
import TotalClasses from "@/assets/svgs/studentDashboard/totalClasses";
import TotalNoOfTutorsIcon from "@/assets/svgs/studentDashboard/totalNoOfTutorsIcon";
import CalendarIcon from "@/assets/svgs/studentDashboard/calendar";
import Tutors from "./tutors/Tutors";
import Calender from "./calendar/Calender";

const MyLessons = () => {
	const navigate = useNavigate();
	const location = useLocation();
	const searchParams = new URLSearchParams(location.search);
	const activeTab = searchParams.get("tab") || "lessons";

	const navItems = [
		{
			id: "lessons",
			Icon: TotalClasses,
			name: "Lessons",
		},
		{
			id: "calendar",
			Icon: CalendarIcon,
			name: "Calendar",
		},
		{
			id: "tutors",
			Icon: TotalNoOfTutorsIcon,
			name: "Tu<PERSON>",
		},
	];

	const handleTabChange = (tabId) => {
		navigate(`?tab=${tabId}`, { replace: true });
	};

	return (
		<div className="">
			{/* Header section with responsive layout */}
			<div className="flex flex-col gap-4 sm:flex-row sm:justify-between border-b border-[#EBEDF0] pb-5 mb-8 bg-white">
				{/* Navigation tabs */}
				<div className="flex gap-2">
					{navItems.map((item) => (
						<button
							key={item.id}
							onClick={() => handleTabChange(item.id)}
							className={`px-4 flex gap-2 items-center w-full rounded-md py-2 transition-colors ${
								activeTab === item.id
									? "bg-primary text-white"
									: "bg-gray-100 hover:bg-gray-200"
							}`}
						>
							<item.Icon
								stroke={activeTab === item.id ? "#ffffff" : "#1A1A40"}
							/>
							<span className="whitespace-nowrap">{item.name}</span>
						</button>
					))}
				</div>
				<div className="border text-center gap-4 px-2 py-2 sm:mx-4 rounded-md border-primary text-primary">
					<Link to={`/student/my-lessons?tab=tutors`}>Schedule Lesson</Link>
				</div>
			</div>
			{/* Content section */}
			{/* Render content based on active tab */}
			<div>
				{activeTab === "lessons" && <LessonsContainer />}
				{activeTab === "calendar" && <Calender />}
				{activeTab === "tutors" && <Tutors />}
			</div>
		</div>
	);
};

export default MyLessons;
