import { But<PERSON> } from "@/components/button/button";
import { X } from "lucide-react";
import React, { useState } from "react";
import RescheduleFormModal from "./RescheduleFormModal";
import { format, isValid } from "date-fns";

const RescheduleModal = ({ show, lesson, onClose }) => {
	const [showForm, setShowForm] = useState(false);

	if (!show || !lesson) return null;

	const handleNext = () => {
		setShowForm(true);
	};

	const handleFormClose = () => {
		setShowForm(false);
		onClose(); // Close both modals
	};

	// Safely format the date
	const formatDate = (date) => {
		if (!date) return "";
		const parsedDate = new Date(date);
		return isValid(parsedDate) ? format(parsedDate, "MMM d, yyyy") : "";
	};

	return (
		<>
			<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
				<div className="bg-white rounded-xl p-6 w-[700px]">
					<div className="flex justify-between items-center pb-4 border-blue-300">
						<h2 className="text-2xl font-bold text-[#1A1A40]">
							Reschedule lesson
						</h2>
						<button
							className="text-gray-600 hover:text-gray-800"
							onClick={onClose}
						>
							<X size={30} />
						</button>
					</div>
					<div>
						<div className="mb-4 shadow-lg rounded-md border p-4">
							<p className="text-[#333333] font-semibold">
								Weekly on {lesson.day || "Friday"} at {lesson.time}
							</p>
							<p className="text-sm mt-2 text-[#4B5563]">
								Reschedule all lessons on {lesson.day || "Friday"} at{" "}
								{lesson.time} starting from {formatDate(lesson.date)}
							</p>
						</div>
						<div className="flex mt-6">
							<Button
								className="bg-primary h-50 font-semibold w-full text-white py-2 px-6 rounded-md"
								onClick={handleNext}
							>
								Continue
							</Button>
						</div>
					</div>
				</div>
			</div>

			{showForm && (
				<RescheduleFormModal onClose={handleFormClose} lesson={lesson} />
			)}
		</>
	);
};

export default RescheduleModal;
