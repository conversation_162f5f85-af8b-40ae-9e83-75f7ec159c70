import { X } from "lucide-react";
import React from "react";
import useGet from "@/hooks/useGet";
import { useGetTutorsQuery } from "@/redux/slices/student/findTutorApiSlice";
import img from "@/assets/svgs/userVector.svg";
import { useNavigate } from "react-router-dom";
import Loader from "@/components/loader/loader";
import { formatFullName } from "@/utils/utils";

const ScheduleTutorModal = ({ selectedSlot, onClose, onScheduleTutor }) => {
	const { data: tutors, isLoading } = useGet(useGetTutorsQuery, "");

	const navigate = useNavigate();

	return (
		<div
			className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-y-auto"
			onClick={onClose}
		>
			{isLoading && <Loader />}

			<div
				className="bg-white rounded-xl w-full max-w-4xl mx-auto max-h-[90vh] flex flex-col"
				onClick={(e) => e.stopPropagation()}
			>
				<div className="flex justify-between items-center p-4 md:p-6 border-b sticky top-0 bg-white z-10">
					<h3 className="text-[#1A1A40] text-sm md:text-xl lg:text-2xl font-bold">
						Select a tutor you want to schedule a class with
					</h3>
					<button
						className="text-gray-500 hover:text-gray-700 p-1"
						onClick={onClose}
						aria-label="Close modal"
					>
						<X size={24} />
					</button>
				</div>

				<div className="overflow-y-auto flex-1 px-4 md:px-6">
					{tutors?.map((tutor) => (
						<div
							key={tutor.id}
							className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-4 border-b last:border-b-0 cursor-pointer hover:bg-gray-50 transition-colors gap-4"
							onClick={() => navigate(`/student/my-lessons/tutors/${tutor.id}`)}
						>
							<div className="flex items-center gap-3 flex-1 min-w-0">
								<img
									src={tutor.image || img}
									alt={tutor.firstname}
									className="w-12 h-12 rounded-full object-cover flex-shrink-0"
								/>
								<div className="min-w-0">
									<p className="text-[#1A1A40] font-medium truncate">
										{formatFullName(tutor.fullname)}
									</p>
									<p className="text-gray-600 text-sm truncate">
										Teaches{" "}
										{tutor?.languages?.map((lang) => lang.name).join(", ")}
									</p>
								</div>
							</div>

							<div className="flex justify-between sm:justify-end gap-4 sm:gap-8">
								<div className="text-right sm:text-center min-w-[80px]">
									<p className="text-[#1A1A40] mt-4 sm:mt-0 text-sm">
										{tutor.totalLessons || 0} lessons
									</p>
								</div>

								<div className="text-right min-w-[80px]">
									<p className="text-gray-800 font-medium">
										{tutor.basePrice ? `$${tutor.basePrice}` : "-"}
									</p>
									<p className="text-gray-600 text-xs">Per lesson</p>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default ScheduleTutorModal;
