import { Button } from "@/components/button/button";
import { X } from "lucide-react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useAddPaymentMethodMutation } from "@/redux/slices/student/paymentMethodApiSlice";
import { toast } from "react-toastify";
import Loader from "@/components/loader/loader";

const AddBankCardModal = ({ onClose }) => {
	const {
		register,
		handleSubmit,
		formState: { errors },
		reset,
	} = useForm();

	const [addPaymentMethod, { isLoading }] = useAddPaymentMethodMutation();
	const [cardholderName, setCardholderName] = useState("");

	const onSubmit = async (data) => {
		try {
			const response = await addPaymentMethod({
				cardNumber: data.cardNumber,
				expiryDate: data.expiryDate,
				cvv: data.cvv,
				cardholderName: cardholderName,
			}).unwrap();

			if (response.success) {
				toast.success("Card added successfully!");
				reset();
				onClose();
			}
		} catch (error) {
			console.error("Error adding payment method:", error);
			toast.error(error?.data?.message || "Failed to add card. Please try again.");
		}
	};

	return (
		<div className="sm:w-[669px] w-sm mx-auto p-3 sm:p-6 bg-white rounded-lg shadow-lg">
			<div className="flex justify-between items-center pb-4 border-b border-gray-200">
				<p className="text-2xl font-bold text-[#1A1A40]">Add Card</p>
				<button onClick={onClose} className="text-gray-500 hover:text-gray-700">
					<X />
				</button>
			</div>

			<div className="mt-6">
				<form onSubmit={handleSubmit(onSubmit)}>
					<div className="mb-6">
						<label
							className="block text-md font-medium text-[#1A1A40] mb-1"
							htmlFor="cardholderName"
						>
							Cardholder Name
						</label>
						<input
							id="cardholderName"
							type="text"
							placeholder="Enter cardholder name"
							className="w-full h-10 rounded-md border border-gray-300 px-3 focus:outline-none focus:ring-2 mb-6"
							value={cardholderName}
							onChange={(e) => setCardholderName(e.target.value)}
							required
						/>
					</div>

					<div className="mb-6">
						<label
							className="block text-md font-medium text-[#1A1A40] mb-1"
							htmlFor="cardNumber"
						>
							Card Number
						</label>
						<input
							id="cardNumber"
							type="text"
							placeholder="Enter your card number"
							className="w-full h-10 rounded-md border border-gray-300 px-3 focus:outline-none focus:ring-2 "
							{...register("cardNumber", {
								required: "Card number is required",
								pattern: {
									value: /^[0-9]{16}$/,
									message: "Card number must be 16 digits",
								},
							})}
						/>
						{errors.cardNumber && (
							<p className="mt-1 text-sm text-red-600">
								{errors.cardNumber.message}
							</p>
						)}
					</div>

					<div className="flex gap-4 mb-6">
						<div className="w-1/2">
							<label
								className="block text-md font-medium text-[#1A1A40] mb-1"
								htmlFor="expiryDate"
							>
								Expiry Date
							</label>
							<input
								id="expiryDate"
								type="text"
								placeholder="MM/YY"
								className="w-full h-10 rounded-md border border-gray-300 px-3 focus:outline-none focus:ring-2 "
								{...register("expiryDate", {
									required: "Expiry date is required",
									pattern: {
										value: /^(0[1-9]|1[0-2])\/?([0-9]{2})$/,
										message: "Invalid expiry date format (MM/YY)",
									},
								})}
							/>
							{errors.expiryDate && (
								<p className="mt-1 text-sm text-red-600">
									{errors.expiryDate.message}
								</p>
							)}
						</div>
						<div className="w-1/2">
							<label
								className="block text-md font-medium text-[#1A1A40] mb-1"
								htmlFor="cvv"
							>
								Security Code
							</label>
							<input
								id="cvv"
								type="text"
								placeholder="123"
								className="w-full h-10 rounded-md border border-gray-300 px-3 focus:outline-none focus:ring-2 "
								{...register("cvv", {
									required: "CVV is required",
									pattern: {
										value: /^[0-9]{3,4}$/,
										message: "CVV must be 3 or 4 digits",
									},
								})}
							/>
							{errors.cvv && (
								<p className="mt-1 text-sm text-red-600">
									{errors.cvv.message}
								</p>
							)}
						</div>
					</div>

					<Button
						type="submit"
						className="w-full h-[50px] font-semibold text-md rounded-md"
						disabled={isLoading}
					>
						{isLoading ? <Loader size={24} /> : "Save Card"}
					</Button>
				</form>
			</div>
		</div>
	);
};

export default AddBankCardModal;
