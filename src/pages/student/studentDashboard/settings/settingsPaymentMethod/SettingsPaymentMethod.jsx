import React from "react";
import masterCardIcon from "../../../../../assets/svgs/payments/mastercard.svg";
import visaIcon from "../../../../../assets/svgs/payments/Visa-logo.svg";
import lockIcon from "../../../../../assets/svgs/payments/square-lock-password.png";
import AddBankCardModal from "./AddBankCardModal";
import { useGetPaymentMethodsQuery, useDeletePaymentMethodMutation, useSetDefaultPaymentMethodMutation } from "@/redux/slices/student/paymentMethodApiSlice";
import { toast } from "react-toastify";
import { Trash2, Star } from "lucide-react";
import Loader from "@/components/loader/loader";

const SettingsPaymentMethod = () => {
	const [isModalOpen, setIsModalOpen] = React.useState(false);

	const { data: paymentMethods, isLoading, refetch } = useGetPaymentMethodsQuery();
	const [deletePaymentMethod, { isLoading: isDeleting }] = useDeletePaymentMethodMutation();
	const [setDefaultPaymentMethod, { isLoading: isSettingDefault }] = useSetDefaultPaymentMethodMutation();

	const openModal = () => {
		setIsModalOpen(true);
	};

	const closeModal = () => {
		setIsModalOpen(false);
		refetch(); // Refresh payment methods after adding a new one
	};

	const handleDeleteCard = async (paymentMethodId) => {
		if (window.confirm("Are you sure you want to delete this payment method?")) {
			try {
				await deletePaymentMethod(paymentMethodId).unwrap();
				toast.success("Payment method deleted successfully");
			} catch (error) {
				toast.error("Failed to delete payment method");
			}
		}
	};

	const handleSetDefault = async (paymentMethodId) => {
		try {
			await setDefaultPaymentMethod(paymentMethodId).unwrap();
			toast.success("Default payment method updated");
		} catch (error) {
			toast.error("Failed to update default payment method");
		}
	};

	const getCardIcon = (brand) => {
		switch (brand?.toLowerCase()) {
			case 'visa':
				return visaIcon;
			case 'mastercard':
				return masterCardIcon;
			default:
				return visaIcon; // Default fallback
		}
	};

	return (
		<div className="">
			<div className="flex p-2 flex-col w-sm w-auto md:max-w-[516px]">
				<div className="flex shadow-md p-4 rounded-md justify-between">
					<div>
						<p className="text-[#1A1A40] mb-4 font-bold text-lg sm:text-2xl">
							Credit or debit card
						</p>
						<div className="flex gap-6">
							<img src={visaIcon} alt="icons" className="w-12 h-12" />
							<img src={masterCardIcon} alt="icons" className="w-12 h-12" />
						</div>
					</div>
					<div>
						<button
							onClick={openModal}
							className="bg-primary text-md font-semibold text-sm sm:text-xl rounded-md text-white py-3 px-6"
						>
							Add Card
						</button>
					</div>
				</div>

				{/* Saved Payment Methods */}
				{isLoading ? (
					<div className="flex justify-center py-8">
						<Loader size={32} />
					</div>
				) : (
					<div className="mt-4 space-y-3">
						{paymentMethods?.data?.length > 0 ? (
							paymentMethods.data.map((method) => (
								<div key={method.id} className="flex items-center justify-between p-4 border rounded-md bg-gray-50">
									<div className="flex items-center gap-4">
										<img
											src={getCardIcon(method.card.brand)}
											alt={method.card.brand}
											className="w-10 h-8"
										/>
										<div>
											<p className="font-medium text-[#1A1A40]">
												**** **** **** {method.card.last4}
											</p>
											<p className="text-sm text-gray-600">
												{method.card.brand.toUpperCase()} • Expires {method.card.expMonth}/{method.card.expYear}
											</p>
											{method.isDefault && (
												<span className="inline-flex items-center gap-1 text-xs text-green-600 font-medium">
													<Star className="w-3 h-3 fill-current" />
													Default
												</span>
											)}
										</div>
									</div>
									<div className="flex items-center gap-2">
										{!method.isDefault && (
											<button
												onClick={() => handleSetDefault(method.id)}
												disabled={isSettingDefault}
												className="text-sm text-blue-600 hover:text-blue-800 font-medium"
											>
												Set as Default
											</button>
										)}
										<button
											onClick={() => handleDeleteCard(method.id)}
											disabled={isDeleting}
											className="text-red-600 hover:text-red-800 p-1"
										>
											<Trash2 className="w-4 h-4" />
										</button>
									</div>
								</div>
							))
						) : (
							<div className="text-center py-8 text-gray-500">
								<p>No payment methods saved yet.</p>
								<p className="text-sm">Add a card to get started.</p>
							</div>
						)}
					</div>
				)}

				<div className="flex flex-row mt-4">
					<img src={lockIcon} alt="lock icon" className="w-18 h-8 pr-3" />
					<div>
						<p className="text-sm sm:text-md text-[#4B5563]">
							Convolly uses industry-standard encryption to protect your
							information
						</p>
					</div>
				</div>
			</div>

			{isModalOpen && (
				<div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30">
					<AddBankCardModal onClose={closeModal} />
				</div>
			)}
		</div>
	);
};

export default SettingsPaymentMethod;
