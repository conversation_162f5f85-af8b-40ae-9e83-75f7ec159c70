import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useGetNotificationSettingsQuery, useUpdateNotificationSettingsMutation } from "@/redux/slices/student/notificationSettingsApiSlice";
import { toast } from "react-toastify";
import { Button } from "@/components/button/button";
import Loader from "@/components/loader/loader";

const SettingsNotifications = () => {
	const { register, handleSubmit, watch, reset } = useForm({
		defaultValues: {
			lessonUpdates: true,
			lessonReminders: false,
			messageUpdates: true,
		},
	});

	const { data: notificationSettings, isLoading } = useGetNotificationSettingsQuery();
	const [updateNotificationSettings, { isLoading: isUpdating }] = useUpdateNotificationSettingsMutation();

	// Update form when data is loaded
	useEffect(() => {
		if (notificationSettings?.data) {
			reset({
				lessonUpdates: notificationSettings.data.lessonUpdates,
				lessonReminders: notificationSettings.data.lessonReminders,
				messageUpdates: notificationSettings.data.messageUpdates,
			});
		}
	}, [notificationSettings, reset]);

	const onSubmit = async (data) => {
		try {
			const response = await updateNotificationSettings({
				lessonUpdates: data.lessonUpdates,
				lessonReminders: data.lessonReminders,
				messageUpdates: data.messageUpdates,
			}).unwrap();

			if (response.success) {
				toast.success("Notification settings saved successfully!");
			}
		} catch (error) {
			console.error("Error updating notification settings:", error);
			toast.error(error?.data?.message || "Failed to update notification settings");
		}
	};

	if (isLoading) {
		return (
			<div className="flex justify-center py-8">
				<Loader size={32} />
			</div>
		);
	}

	return (
		<form
			onSubmit={handleSubmit(onSubmit)}
			className="flex flex-col w-auto md:max-w-[516px]"
		>
			<h2 className="text-sm sm:text-xl text-[#1A1A40] font-semibold mb-6">
				Email Notifications
			</h2>

			<div className="text-[#4B5563] space-y-4">
				<div className="flex items-center">
					<input
						type="checkbox"
						id="lessonUpdates"
						{...register("lessonUpdates")}
						className="w-4 h-4 text-primary rounded focus:ring-primary"
					/>
					<label
						htmlFor="lessonUpdates"
						className="ml-2 text-sm sm:text-[18px] font-medium"
					>
						Get updates about your lessons
					</label>
				</div>

				<div className="flex items-center">
					<input
						type="checkbox"
						id="lessonReminders"
						{...register("lessonReminders")}
						className="w-4 h-4 text-primary rounded focus:ring-primary"
					/>
					<label
						htmlFor="lessonReminders"
						className="ml-2 text-sm sm:text-[18px] font-medium"
					>
						Reminder emails - 5 minutes before lesson
					</label>
				</div>

				<div className="flex items-center">
					<input
						type="checkbox"
						id="messageUpdates"
						{...register("messageUpdates")}
						className="w-4 h-4 text-primary rounded focus:ring-primary"
					/>
					<label
						htmlFor="messageUpdates"
						className="ml-2 text-sm sm:text-[18px] font-medium"
					>
						Get updates on messages
					</label>
				</div>
			</div>

			<div className="mt-6">
				<Button
					type="submit"
					className="w-full h-[50px] mb-3"
					disabled={isUpdating}
				>
					{isUpdating ? <Loader size={24} /> : "Save Changes"}
				</Button>
			</div>
		</form>
	);
};

export default SettingsNotifications;
