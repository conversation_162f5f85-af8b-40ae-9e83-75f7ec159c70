import React, { useState } from "react";

const PauseSubscriptionModal = ({ onClose }) => {
	const [selectedReason, setSelectedReason] = useState(null);

	const reasons = ["30 Days", "60 Days", "90 Days"];

	const handleSubmit = () => {
		if (!selectedReason) {
			alert("Please select a reason before confirming");
			return;
		}
		console.log("Selected reason:", selectedReason);
		// Handle form submission here
		onClose();
	};

	return (
		<div className="bg-white p-4 rounded-md">
			<div className="w-full p-2 max-w-[570px]">
				<h1 className="font-semibold text-lg">Set pause length</h1>
				<p className="text-[#4B5563] my-2">
					We understand that sometimes you just need a little time off. Instead
					of cancelling, you can pause your subscription for up (30,60,90) days,
					and you will keep your progress saved.
				</p>

				{/* Radio selection for reasons */}
				<div className="mt-4 space-y-3 text-[#4B5563]">
					{reasons.map((reason, index) => (
						<div key={index} className="flex items-center">
							<input
								type="radio"
								id={`reason-${index}`}
								name="refund-reason"
								value={reason}
								checked={selectedReason === reason}
								onChange={() => setSelectedReason(reason)}
								className="h-4 w-4 text-primary focus:ring-primary border-gray-300 mr-2"
							/>
							<label htmlFor={`reason-${index}`} className="cursor-pointer">
								{reason}
							</label>
						</div>
					))}
				</div>

				{/* Additional reason field (shown only when "Other reason" is selected) */}
				{selectedReason === "Other reason" && (
					<div className="mt-4">
						<textarea
							placeholder="Please specify your reason"
							className="w-full p-3 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
							rows={3}
						/>
					</div>
				)}

				<div className="mt-6 pt-4 flex justify-end space-x-2 text-lg">
					<button
						onClick={onClose}
						className="border px-4 py-2 rounded-md hover:bg-gray-50 transition-colors"
					>
						Dismiss
					</button>
					<button
						onClick={handleSubmit}
						className="border text-white px-4 py-2 rounded-md bg-primary hover:bg-primary-dark transition-colors"
					>
						Confirm
					</button>
				</div>
			</div>
		</div>
	);
};

export default PauseSubscriptionModal;
