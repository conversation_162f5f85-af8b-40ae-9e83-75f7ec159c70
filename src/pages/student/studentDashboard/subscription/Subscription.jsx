import React from "react";
import SubsCard from "./components/SubsCard";
import { useGetStudentSubscriptionsQuery } from "@/redux/slices/student/subscriptionApiSlice";
import { useSelector } from "react-redux";

const Subscription = () => {
	// Get the user ID from Redux state
	const user = useSelector((state) => state?.app?.userInfo?.user);
	const id = user?.id;

	const {
		data: subscriptions,
		isLoading,
		error,
	} = useGetStudentSubscriptionsQuery(id, {
		skip: !id,
	});

	// Extract the data array from the API response
	const subsList = subscriptions?.data || [];

	return (
		<div className="px-1 sm:px-4">
			<div className="text-[#4B5563] text-md sm:text-[16px] gap-2">
				<p className="py-2">
					All subscription are set to renew automatically at the end of each
					billing cycle, using your saved payment method. You will receive a
					reminder email 3 days before renewal
				</p>
				<p className="pb-2">
					If you pause your subscription for more than 90 days, your
					subscription will expire.
				</p>
			</div>

			<div className="mt-4 space-y-4">
				{isLoading ? (
					<div className="space-y-4">
						<SubsCard subscription={null} />
						<SubsCard subscription={null} />
					</div>
				) : error ? (
					<div className="text-center py-8">
						<p className="text-red-500 mb-2">Error loading subscriptions</p>
						<p className="text-gray-500 text-sm">
							{error?.data?.error || error?.message || 'Please try again later'}
						</p>
					</div>
				) : subsList.length === 0 ? (
					<div className="text-center py-8">
						<p className="text-gray-500">No subscriptions found.</p>
						<p className="text-gray-400 text-sm mt-1">
							Subscribe to a tutor to see your subscriptions here.
						</p>
					</div>
				) : (
					subsList.map((sub) => <SubsCard key={sub._id} subscription={sub} />)
				)}
			</div>

			<div className="mt-4">
				<div className="mb-4">
					<p className="text-[#1A1A40] text-lg mb-2">Cancellation</p>
					<p className="text-[#4B5563]">
						You can cancel your subscription anytime. After cancellation, you
						will continue to have access to premium features until the end of
						your current billing period. No further charges will be made unless
						you reactivate your plan.
					</p>
				</div>
				<div>
					<p className="text-[#1A1A40] text-lg mb-2">Refund</p>
					<p className="text-[#4B5563]">
						We offer a 7-day money-back guarantee. After this period, payments
						are non-refundable.
					</p>
				</div>
			</div>
		</div>
	);
};

export default Subscription;
