import React from "react";
import { ChevronRight, ChevronLeft } from "lucide-react";

const DashboardPagination = ({
	currentPage = 1,
	totalPages = 20,
	onPageChange,
	visiblePageCount = 7,
}) => {
	const handlePageChange = (newPage) => {
		if (newPage >= 1 && newPage <= totalPages && newPage !== currentPage) {
			onPageChange(newPage);
		}
	};

	const generatePageNumbers = () => {
		const pages = [];
		const halfVisible = Math.floor(visiblePageCount / 2);
		let startPage = Math.max(1, currentPage - halfVisible);
		let endPage = Math.min(totalPages, startPage + visiblePageCount - 1);

		if (endPage - startPage + 1 < visiblePageCount) {
			startPage = Math.max(1, endPage - visiblePageCount + 1);
		}

		// Always show first page
		if (startPage > 1) {
			pages.push(1);
			if (startPage > 2) {
				pages.push("...");
			}
		}

		// Middle pages
		for (let i = startPage; i <= endPage; i++) {
			if (i > 0 && i <= totalPages && !pages.includes(i)) {
				pages.push(i);
			}
		}

		// Always show last page
		if (endPage < totalPages) {
			if (endPage < totalPages - 1) {
				pages.push("...");
			}
			pages.push(totalPages);
		}

		return pages;
	};

	return (
		<div className="flex items-center justify-between pt-4 text-sm">
			<span className="text-xl">
				Page {currentPage} of {totalPages}
			</span>
			<div className="flex items-center gap-1">
				<button
					className={`p-2 rounded-md ${
						currentPage === 1
							? "bg-gray-200 cursor-not-allowed"
							: "bg-[#1A1A40] hover:bg-[#2A2A60]"
					}`}
					onClick={() => handlePageChange(currentPage - 1)}
					disabled={currentPage === 1}
				>
					<ChevronLeft className="text-white" />
				</button>

				{generatePageNumbers().map((n, i) =>
					n === "..." ? (
						<span key={i} className="px-2">
							...
						</span>
					) : (
						<button
							key={i}
							className={`px-3 py-1 rounded-full ${
								currentPage === n
									? "bg-green-100 text-green-600"
									: "hover:bg-gray-100"
							}`}
							onClick={() => handlePageChange(n)}
						>
							{n}
						</button>
					)
				)}

				<button
					className={`p-2 rounded-md ${
						currentPage === totalPages
							? "bg-gray-200 cursor-not-allowed"
							: "bg-[#1A1A40] hover:bg-[#2A2A60]"
					}`}
					onClick={() => handlePageChange(currentPage + 1)}
					disabled={currentPage === totalPages}
				>
					<ChevronRight className="text-white" />
				</button>
			</div>
		</div>
	);
};

export default DashboardPagination;
