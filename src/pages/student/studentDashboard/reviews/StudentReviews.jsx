import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { Search, Filter, ChevronDown, ChevronUp } from "lucide-react";
import ReviewCard from "./components/ReviewCard";
import { useGetMyReviewsMutation } from "@/redux/slices/student/reviewApiSlice";
import Loader from "@/components/loader/loader";

const StudentReviews = () => {
	const [reviews, setReviews] = useState([]);
	const [page, setPage] = useState(1);
	const [hasMore, setHasMore] = useState(true);
	const [loading, setLoading] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [sortOrder, setSortOrder] = useState("desc");
	const [ratingFilter, setRatingFilter] = useState("");
	const [showFilters, setShowFilters] = useState(false);

	const userInfo = useSelector((state) => state.app.userInfo);
	const currentUser = userInfo?.user;
	const [getMyReviews, { isLoading, error }] = useGetMyReviewsMutation();

	useEffect(() => {
		if (currentUser?.id) {
			fetchReviews();
		}
	}, [currentUser?.id, sortOrder, ratingFilter]);

	useEffect(() => {
		const timeoutId = setTimeout(() => {
			if (currentUser?.id) {
				fetchReviews(1, true);
			}
		}, 500);

		return () => clearTimeout(timeoutId);
	}, [searchTerm]);

	const fetchReviews = async (pageNum = 1, reset = true) => {
		if (!currentUser?.id) return;

		setLoading(true);
		try {
			const response = await getMyReviews({
				userId: currentUser.id,
				page: pageNum,
				limit: 10,
				sortOrder: sortOrder,
			}).unwrap();

			if (response?.data) {
				let newReviews = response.data;

				if (ratingFilter) {
					newReviews = newReviews.filter(
						(review) => review.rating === parseInt(ratingFilter)
					);
				}

				if (searchTerm) {
					newReviews = newReviews.filter(
						(review) =>
							review.targetUser?.fullname
								?.toLowerCase()
								.includes(searchTerm.toLowerCase()) ||
							review.comment?.toLowerCase().includes(searchTerm.toLowerCase())
					);
				}

				if (reset) {
					setReviews(newReviews);
				} else {
					setReviews((prev) => [...prev, ...newReviews]);
				}

				setHasMore(response.data.length === 10);
				setPage(pageNum);
			}
		} catch (err) {
			console.error("Error fetching reviews:", err);
		} finally {
			setLoading(false);
		}
	};

	const loadMoreReviews = () => {
		if (!loading && hasMore) {
			fetchReviews(page + 1, false);
		}
	};

	const handleReviewUpdate = () => {
		fetchReviews(1, true);
	};

	if (!currentUser) {
		return (
			<div className="flex justify-center items-center h-64">
				<p className="text-gray-500">Please log in to view your reviews.</p>
			</div>
		);
	}

	if (isLoading && reviews.length === 0) {
		return (
			<div className="flex justify-center items-center h-64">
				<Loader />
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex justify-center items-center h-64">
				<p className="text-red-500">Error loading reviews. Please try again.</p>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Search and Filter Controls */}
			<div className="bg-white pb-4 rounded-lg">
				<div className="flex sm:flex-row gap-4 items-start sm:items-center justify-between">
					{/* Search Bar */}
					<div className="relative flex-1 w-80% sm:max-w-md">
						<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							<Search className="text-gray-400" size={18} />
						</div>
						<input
							type="text"
							placeholder="Search reviews by tutor name or comment..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="block w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
						/>
					</div>

					{/* Filter Button */}
					<div className="w-20% sm:w-auto">
						<button
							onClick={() => setShowFilters(!showFilters)}
							className="flex items-center gap-2 px-4 py-2.5 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors w-full sm:w-auto justify-center"
						>
							<Filter size={24} className="text-gray-600" />
							<span className="hidden sm:block text-gray-700 font-medium">
								Filters
							</span>
							{showFilters ? (
								<ChevronUp size={16} className="text-gray-600" />
							) : (
								<ChevronDown size={16} className="text-gray-600" />
							)}
						</button>
					</div>
				</div>

				{/* Expanded Filters */}
				{showFilters && (
					<div className="mt-4 pt-4 border-t border-gray-200">
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							{/* Sort Order */}
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1.5">
									Sort by Date
								</label>
								<select
									value={sortOrder}
									onChange={(e) => setSortOrder(e.target.value)}
									className="w-full px-3 py-2.5 border border-gray-300 rounded-lg bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
								>
									<option value="desc">Newest First</option>
									<option value="asc">Oldest First</option>
								</select>
							</div>

							{/* Rating Filter */}
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1.5">
									Filter by Rating
								</label>
								<select
									value={ratingFilter}
									onChange={(e) => setRatingFilter(e.target.value)}
									className="w-full px-3 py-2.5 border border-gray-300 rounded-lg bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
								>
									<option value="">All Ratings</option>
									{[5, 4, 3, 2, 1].map((rating) => (
										<option key={rating} value={rating}>
											{rating} Star{rating !== 1 ? "s" : ""}
										</option>
									))}
								</select>
							</div>

							{/* Clear Filters */}
							<div className="flex  items-end">
								<button
									onClick={() => {
										setSearchTerm("");
										setSortOrder("desc");
										setRatingFilter("");
									}}
									className="px-4 py-2.5 text-gray-600 hover:text-gray-800 font-medium transition-colors"
								>
									Reset Filters
								</button>
							</div>
						</div>
					</div>
				)}
			</div>

			{/* Reviews List */}
			{isLoading && reviews.length === 0 ? (
				<div className="flex justify-center items-center h-64">
					<Loader />
				</div>
			) : error ? (
				<div className="flex justify-center items-center h-64">
					<p className="text-red-500">
						Error loading reviews. Please try again.
					</p>
				</div>
			) : reviews.length === 0 ? (
				<div className="text-center py-16 bg-white rounded-lg border border-gray-100 shadow-sm">
					<div className="max-w-md mx-auto">
						<svg
							className="mx-auto h-12 w-12 text-gray-400"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={1.5}
								d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
							/>
						</svg>
						<h3 className="mt-2 text-sm font-medium text-gray-900">
							{searchTerm || ratingFilter
								? "No matching reviews found"
								: "No reviews yet"}
						</h3>
						<p className="mt-1 text-xs text-gray-500">
							{searchTerm || ratingFilter
								? "Try adjusting your search or filter criteria."
								: "Your reviews will appear here once you submit them."}
						</p>
					</div>
				</div>
			) : (
				<div className="space-y-4">
					{reviews.map((review) => (
						<ReviewCard
							key={review._id}
							review={review}
							onUpdate={handleReviewUpdate}
						/>
					))}

					{hasMore && !searchTerm && !ratingFilter && (
						<div className="flex justify-center mt-6">
							<button
								onClick={loadMoreReviews}
								disabled={loading}
								className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
							>
								{loading ? (
									<span className="flex items-center justify-center gap-2">
										<Loader size="sm" />
										Loading...
									</span>
								) : (
									"Load More Reviews"
								)}
							</button>
						</div>
					)}
				</div>
			)}
		</div>
	);
};

export default StudentReviews;
