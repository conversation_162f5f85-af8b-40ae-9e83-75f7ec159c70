import { X } from "lucide-react";
import React, { useState } from "react";
import StarRating from "../../../../../assets/images/studentDashboard/Star 5.png";
import { Button } from "@/components/button/button";
import { toast } from "react-toastify";

const RateTutorModal = ({ onClose, tutorName, onSubmitReview }) => {
	const [rating, setRating] = useState(0);
	const [comment, setComment] = useState("");
	const [hoverRating, setHoverRating] = useState(0);

	const handleSubmit = () => {
		if (rating === 0) {
			toast.error("Please select a rating");

			return;
		}
		onSubmitReview({ rating, comment });
		toast.success(`rating ${tutorName} was successful`);
		onClose();
	};

	return (
		<div className="sm:w-[669px] w-sm mx-auto p-2 sm:p-6 bg-white rounded-lg shadow-lg">
			<div className="flex gap-[145px] pt-2 justify-between">
				<p className="text-2xl font-bold text-[#1A1A40]"></p>
				<button onClick={onClose}>
					<X />
				</button>
			</div>

			<div className="my-2">
				<div className="items-center justify-center text-center">
					<div>
						<h1 className="text-xl sm:text-[30px] font-semibold text-[#1A1A40] pb-2">
							Rate your experience
						</h1>
						<p className="text-sm sm:text-[18px] text-[#4B5563]">
							We highly value your feedback! Kindly take a moment to rate your
							experience with {tutorName} and provide us with your valuable
							feedback
						</p>
						<br />
						<div className="flex items-center justify-center gap-5 flex-row pr-4">
							<div className="flex space-x-2">
								{[...Array(5)].map((_, i) => {
									const ratingValue = i + 1;
									return (
										<button
											key={i}
											onClick={() => setRating(ratingValue)}
											onMouseEnter={() => setHoverRating(ratingValue)}
											onMouseLeave={() => setHoverRating(0)}
											className="focus:outline-none"
										>
											<img
												src={StarRating}
												alt="Star"
												className={`w-8 h-8 ${
													(hoverRating || rating) >= ratingValue
														? "opacity-100"
														: "opacity-30"
												}`}
											/>
										</button>
									);
								})}
							</div>
						</div>
					</div>
				</div>
				<div>
					<div className="mt-6">
						<label className="block text-[#1A1A40] text-md font-medium mb-2">
							Comment
						</label>
						<textarea
							className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							rows={6}
							placeholder="Tell us about your experience"
							value={comment}
							onChange={(e) => setComment(e.target.value)}
						/>
					</div>
				</div>
				<Button className="mt-4 w-full h-[50px]" onClick={handleSubmit}>
					Submit review
				</Button>
			</div>
		</div>
	);
};

export default RateTutorModal;
