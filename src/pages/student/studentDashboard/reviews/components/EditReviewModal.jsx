import { Button } from "@/components/button/button";
import { X, Star } from "lucide-react";
import React, { useState, useEffect } from "react";
import { useUpdateReviewMutation } from "@/redux/slices/student/reviewApiSlice";
import defaultAvatar from "../../../../../assets/images/tutor1.png";
import { useToast } from "@/context/toastContext/toastContext";
import { formatFullName } from "@/utils/utils";

const EditReviewModal = ({ review, onClose, onUpdate }) => {
	const [rating, setRating] = useState(review?.rating || 0);
	const [comment, setComment] = useState(review?.comment || "");
	const [hoveredRating, setHoveredRating] = useState(0);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const [updateReview] = useUpdateReviewMutation();
	const { showToast } = useToast();

	const targetUser = review?.targetUser || {};

	useEffect(() => {
		if (review) {
			setRating(review.rating || 0);
			setComment(review.comment || "");
		}
	}, [review]);

	const handleRatingClick = (newRating) => {
		setRating(newRating);
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		if (rating === 0) {
			showToast("Please select a rating", "error");
			return;
		}

		if (!comment.trim()) {
			showToast("Please provide a comment", "error");
			return;
		}

		setIsSubmitting(true);

		try {
			await updateReview({
				reviewId: review._id,
				rating,
				comment: comment.trim(),
			}).unwrap();

			showToast("Review updated successfully!", "success");
			onUpdate();
			onClose();
		} catch (error) {
			console.error("Error updating review:", error);
			showToast(
				error?.data?.message || "Failed to update review. Please try again.",
				"error"
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	const renderStars = () => {
		return Array.from({ length: 5 }, (_, index) => {
			const starValue = index + 1;
			const isActive = starValue <= (hoveredRating || rating);

			return (
				<Star
					key={index}
					size={24}
					className={`cursor-pointer transition-colors ${
						isActive
							? "fill-yellow-400 text-yellow-400"
							: "fill-gray-200 text-gray-200 hover:fill-yellow-300 hover:text-yellow-300"
					}`}
					onClick={() => handleRatingClick(starValue)}
					onMouseEnter={() => setHoveredRating(starValue)}
					onMouseLeave={() => setHoveredRating(0)}
				/>
			);
		});
	};

	return (
		<div>
			<div className="sm:w-[600px] w-sm mx-auto p-3 sm:p-6 bg-white rounded-lg shadow-lg">
				<div className="flex justify-between items-center border-gray-200">
					<p className="text-2xl font-bold text-[#1A1A40]"></p>
					<button
						onClick={onClose}
						className="text-gray-500 hover:text-gray-700"
					>
						<X size={30} />
					</button>
				</div>

				<div className="mt-6">
					<div className="flex">
						<div className="flex pr-6 w-[220px] ">
							<img
								src={tutor.avatar}
								alt=""
								className="object-cover w-16 h-16 mr-6 rounded-md"
							/>
							<div>
								<p className="text-lg font-semibold text-[#1A1A40]">
									{formatFullName(targetUser.fullname) || "Unknown Tutor"}
								</p>
								<div className="flex flex-row">
									<p className="text-sm text-[#4B5563] ">
										{" "}
										Total Review: {tutor.totalReview}
									</p>
								</div>
							</div>
						</div>
						<div className="flex gap-5 flex-row pr-4">
							<p className="text-[#1A1A40] text-md flex font-semibold">
								{[...Array(5)].map((_, i) => (
									<img
										key={i}
										src={StarRating}
										alt="Star"
										className="w-6 h-6"
									/>
								))}
							</p>
						</div>
					</div>
					<br />
					<div>
						<div className="mt-6">
							<label className="block text-[#1A1A40] text-md font-medium mb-2">
								Comment
							</label>
							<textarea
								className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								rows={6}
								placeholder="Write your review here..."
							/>
						</div>
					</div>
					<Button className="mt-4 w-full h-[50px]">Submit review</Button>
				</div>
			</div>
		</div>
	);
};

export default EditReviewModal;
