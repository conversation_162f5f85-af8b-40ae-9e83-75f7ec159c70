import Navbar from "../../../components/navbar/navbar";
import React, { useEffect, useState } from "react";
import useGet from "@/hooks/useGet";
import { useGetStudentDetailsQuery } from "@/redux/slices/onboardingApiSlice";
import { useSelector } from "react-redux";
import Loader from "@/components/loader/loader";
import PageOne from "./tabs/pageOne/pageOne";
import PageTwo from "./tabs/pageTwo/pageTwo";

const StudentOnboarding = () => {
  const studentId = useSelector((state) => state?.app?.userInfo?.user?.id);

  const [activeTab, setActiveTab] = useState("pageOne");
  const {
    data: studentDetails,
    isLoading,
    refetch
  } = useGet(useGetStudentDetailsQuery, studentId, !!studentId);

  return (
    <div className="text-secondary sm:pb-14 pb-10 relative">
      <Navbar />
      {isLoading && <Loader />}

      <div className="max-w-[640px] w-[93%] mx-auto sm:mt-14 mt-10">
        <h2 className="sm:text-4xl text-3xl font-bold mb-5 sm:mt-8 lg:mt-0">
          Learning goals and Objectives
        </h2>
        <p className="text-[#4B5563] sm:text-lg mb-8">
          Start creating your public tutor profile. Your progress will be
          automatically saved as you complete each section. You can return at
          any time to finish your registration.
        </p>

        {activeTab === "pageOne" && (
          <PageOne
            setActiveTab={setActiveTab}
            studentDetails={studentDetails}
            refetchStudentDetails={refetch}
          />
        )}
        {activeTab === "pageTwo" && (
          <PageTwo
            setActiveTab={setActiveTab}
            studentDetails={studentDetails}
            refetchStudentDetails={refetch}
          />
        )}
      </div>
    </div>
  );
};

export default StudentOnboarding;
