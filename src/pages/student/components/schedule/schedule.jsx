import React, { useState } from "react";
import "./CalendarComponent.css";

const CalendarComponent = () => {
  const [currentDate] = useState(new Date("2025-05-04"));
  const days = ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>hu", "<PERSON><PERSON>", "<PERSON>t", "Sun"];
  const dates = [4, 5, 6, 7, 8, 9, 10];
  const timeSlots = Array.from({ length: 16 }, (_, i) => {
    const hour = Math.floor(i / 2) + 7;
    const minute = (i % 2) * 30;
    return `${hour.toString().padStart(2, "0")}:${minute
      .toString()
      .padStart(2, "0")}`;
  });

  return (
    <div className="calendar-container">
      <div className="calendar-header">
        <button>Today</button>
        <span>
          {currentDate.toLocaleDateString("en-US", {
            month: "long",
            day: "numeric"
          }) +
            " - " +
            new Date("2025-05-10").toLocaleDateString("en-US", {
              day: "numeric",
              year: "numeric"
            })}
        </span>
        <div>
          <button>&lt;</button>
          <button>&gt;</button>
        </div>
      </div>
      <table>
        <thead>
          <tr>
            {days.map((day, index) => (
              <th key={index} className={index === 0 ? "highlight" : ""}>
                {day} {index === 0 && <br />} {dates[index]}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {Array.from({ length: 8 }, (_, rowIndex) => (
            <tr key={rowIndex}>
              {days.map((_, colIndex) => {
                const timeSlot =
                  timeSlots[rowIndex * 2 + (colIndex === 0 ? 0 : 1)];
                const isHighlighted = colIndex === 0 && rowIndex === 0;
                return (
                  <td
                    key={colIndex}
                    className={isHighlighted ? "highlight" : ""}
                  >
                    {timeSlot}
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default CalendarComponent;
