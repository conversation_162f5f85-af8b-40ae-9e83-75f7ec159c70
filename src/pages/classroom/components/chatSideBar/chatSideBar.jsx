import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import send from "@/assets/svgs/send.svg";
import AC from "agora-chat";

const ChatSideBar = ({
  setShowChat,
  chatLoggedIn,
  conn,
  setUnreadMessages,
  setMessages,
  messages,
  roomInfo,
}) => {
  const [message, setMessage] = useState("");

  const sendMessage = async (e) => {
    e.preventDefault();
    if (!message.trim() || !chatLoggedIn) return;

    const msg = AC.message.create({
      chatType: "groupChat",
      type: "txt",
      to: roomInfo?.chatGroup?.id,
      msg: message
    });

    try {
      await conn.current.send(msg);
      setMessages((prev) => [...prev, { senderId: "You", text: message }]);
      setMessage("");
    } catch (e) {
      console.error("Send message error:", e);
    }
  };

  useEffect(() => {
    if (!conn.current) return;

    const messageHandler = {
      onTextMessage: (msg) => {
        if (
          msg.msg !== "ai-quiz" &&
          msg.msg !== "screen-share-start" &&
          msg.msg !== "screen-share-end" &&
          msg.msg !== "introducing-myself"
        ) {
          console.log("Received chat message:", msg);
          setMessages((prev) => [
            ...prev,
            { senderId: msg.from, text: msg.msg }
          ]);
          if (!setShowChat) setUnreadMessages((prev) => prev + 1);
        }
      }
    };

    conn.current.addEventHandler("message", messageHandler);

    return () => {
      conn.current.removeEventHandler("message", messageHandler);
    };
  }, [conn, setMessages, setUnreadMessages, setShowChat]);

  useEffect(() => {
    if (setUnreadMessages) {
      setUnreadMessages(0);
    }
  }, [setUnreadMessages]);

  return (
    <div className="absolute top-0 right-0 max-w-[350px] w-full h-[calc(100vh-100px)] bg-white shadow-md rounded-lg flex flex-col">
      <div className="p-2 border-b font-bold text-sm bg-gray-100 flex justify-between">
        <p>Chat</p>
        <X
          className="w-5 h-5 cursor-pointer"
          onClick={() => setShowChat(false)}
        />
      </div>

      <div className="flex-1 overflow-y-auto p-2 space-y-1 text-sm">
        {messages.map((msg, idx) => (
          <div
            key={idx}
            className={`p-2 rounded w-fit ${
              msg.senderId === "You"
                ? "bg-[#1FC16B1A] text-secondary text-right ml-auto"
                : "bg-[#EBEDF0] text-[#4B5563]"
            }`}
          >
            {msg.text}
          </div>
        ))}
      </div>

      <form onSubmit={sendMessage} className="border mt-auto flex flex-col">
        <textarea
          rows={4}
          placeholder="Type a message..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          className="text-sm outline-none p-2 w-full resize-none"
        ></textarea>
        <div className="p-2">
          <button
            type="submit"
            className="h-6 w-6 flex justify-center items-center ml-auto rounded-md bg-[#E8E8E8]"
          >
            <img id="send-icon" src={send} alt="send icon" />
          </button>
        </div>
      </form>
    </div>
  );
};

export default ChatSideBar;
