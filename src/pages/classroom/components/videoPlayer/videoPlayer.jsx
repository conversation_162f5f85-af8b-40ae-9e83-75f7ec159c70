
import React, { useState, useRef, useEffect, useCallback } from "react";
import YouTube from "react-youtube";
import { X } from "lucide-react";

// Constants for synchronization
const SYNC_INTERVAL = 3000; // Sync every 3 seconds
const SYNC_THRESHOLD = 1.5; // Sync if difference > 1.5 seconds
const SEEK_THRESHOLD = 0.5; // Seek if difference > 0.5 seconds

const VideoPlayer = ({
  video,
  socket,
  isSocketConnected,
  roomInfo,
  rtcUid,
  setBroadcastedVideo,
}) => {
  const playerRef = useRef(null);
  const lastActionRef = useRef(null);
  const syncIntervalRef = useRef(null);
  const lastSyncTimeRef = useRef(0);
  const isSeekingRef = useRef(false);
  const isInitializedRef = useRef(false);

  // Handle YouTube player ready
  const onReady = useCallback((event) => {
    playerRef.current = event.target;
    isInitializedRef.current = true;

    // Request current video state from host
    if (socket && isSocketConnected) {
      socket.emit("room-broadcast", roomInfo?.channel?.id, {
        roomId: roomInfo?.channel?.id,
        action: "request-video-state",
        senderId: rtcUid.current,
        timestamp: Date.now(),
      });
    }
  }, [socket, isSocketConnected, roomInfo?.channel?.id, rtcUid]);

  // Broadcast action to other participants
  const broadcastAction = useCallback((actionType, currentTime = null) => {
    if (!socket || !isSocketConnected || !playerRef.current) return;

    const action = {
      roomId: roomInfo?.channel?.id,
      action: actionType,
      senderId: rtcUid.current,
      currentTime: currentTime ?? playerRef.current.getCurrentTime(),
      timestamp: Date.now(),
      isPlaying: playerRef.current.getPlayerState() === 1,
    };

    lastActionRef.current = action;
    socket.emit("room-broadcast", roomInfo?.channel?.id, action);
  }, [socket, isSocketConnected, roomInfo?.channel?.id, rtcUid]);

  // Handle player state changes
  const onStateChange = useCallback((event) => {
    if (!playerRef.current || !socket || !isSocketConnected) return;

    // Ignore events during seeking
    if (isSeekingRef.current) return;

    const state = event.data;
    const currentTime = playerRef.current.getCurrentTime();

    if (state === YouTube.PlayerState.PLAYING) {
      broadcastAction("play", currentTime);
    } else if (state === YouTube.PlayerState.PAUSED) {
      broadcastAction("pause", currentTime);
    }
  }, [broadcastAction, socket, isSocketConnected]);

  // Handle seeking (detected via interval)
  const checkForSeek = useCallback(() => {
    if (!playerRef.current || isSeekingRef.current) return;

    const currentTime = playerRef.current.getCurrentTime();
    const lastSyncTime = lastSyncTimeRef.current;

    // Detect significant time jumps (seeking)
    if (Math.abs(currentTime - lastSyncTime) > SEEK_THRESHOLD) {
      broadcastAction("seek", currentTime);
      lastSyncTimeRef.current = currentTime;
    }
  }, [broadcastAction]);

  // Synchronize player state with others
  const synchronizePlayer = useCallback(async () => {
    if (!playerRef.current || !socket || !isSocketConnected) return;

    const currentTime = await playerRef.current.getCurrentTime();
    const isPlaying = await playerRef.current.getPlayerState() === 1;

    broadcastAction("sync-time", currentTime);
    lastSyncTimeRef.current = currentTime;
  }, [broadcastAction, socket, isSocketConnected]);

  // Close video player
  const closeVideoPlayer = useCallback(() => {
    if (socket && isSocketConnected) {
      broadcastAction("clear-video");
    }
    setBroadcastedVideo(null);
  }, [broadcastAction, setBroadcastedVideo, socket, isSocketConnected]);

  // Set up synchronization interval
  useEffect(() => {
    if (isSocketConnected && socket && playerRef.current) {
      syncIntervalRef.current = setInterval(() => {
        checkForSeek();
        synchronizePlayer();
      }, SYNC_INTERVAL);
    }

    return () => {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
      }
    };
  }, [checkForSeek, synchronizePlayer, isSocketConnected, socket]);

  // Handle incoming socket events
  useEffect(() => {
    if (!socket || !isSocketConnected) return;

    const handleRoomBroadcast = async (data) => {
      if (!playerRef.current || !isInitializedRef.current) return;

      // Ignore our own actions or outdated messages
      if (data.senderId === rtcUid.current ||
          (lastActionRef.current && data.timestamp <= lastActionRef.current.timestamp)) {
        return;
      }

      try {
        const currentTime = await playerRef.current.getCurrentTime();
        const timeDiff = Math.abs(currentTime - data.currentTime);
        const playerState = await playerRef.current.getPlayerState();

        // Handle different action types
        switch (data.action) {
          case "play":
            if (timeDiff > SYNC_THRESHOLD) {
              isSeekingRef.current = true;
              await playerRef.current.seekTo(data.currentTime, true);
              isSeekingRef.current = false;
            }
            if (playerState !== 1) await playerRef.current.playVideo();
            break;

          case "pause":
            if (timeDiff > SYNC_THRESHOLD) {
              isSeekingRef.current = true;
              await playerRef.current.seekTo(data.currentTime, true);
              isSeekingRef.current = false;
            }
            if (playerState !== 2) await playerRef.current.pauseVideo();
            break;

          case "seek":
            if (timeDiff > SEEK_THRESHOLD) {
              isSeekingRef.current = true;
              await playerRef.current.seekTo(data.currentTime, true);
              isSeekingRef.current = false;
            }
            break;

          case "sync-time":
            if (timeDiff > SYNC_THRESHOLD) {
              isSeekingRef.current = true;
              await playerRef.current.seekTo(data.currentTime, true);
              isSeekingRef.current = false;
            }
            break;

          case "video-state-response":
            isSeekingRef.current = true;
            await playerRef.current.seekTo(data.currentTime, true);
            if (data.isPlaying) await playerRef.current.playVideo();
            else await playerRef.current.pauseVideo();
            isSeekingRef.current = false;
            break;

          default:
            break;
        }

        lastActionRef.current = data;
      } catch (error) {
        console.error("Error handling player action:", error);
        isSeekingRef.current = false;
      }
    };

    socket.on("room-broadcast", handleRoomBroadcast);

    return () => {
      socket.off("room-broadcast", handleRoomBroadcast);
    };
  }, [socket, isSocketConnected, rtcUid]);

  if (!video || !video.id) {
    return null;
  }

  return (
    <div className="relative w-full h-full bg-black flex flex-col items-center justify-center p-4">
      <div className="w-full max-w-4xl h-full">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-white text-lg font-semibold truncate">
            {video.title || "Shared Video"}
          </h3>
          <button
            onClick={closeVideoPlayer}
            className="text-white hover:text-gray-300"
            title="Close video"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
        
        <div className="aspect-w-16 aspect-h-9 h-[90%] bg-black rounded-lg overflow-hidden">
          <YouTube
            videoId={video.id}
            opts={{
              width: "100%",
              height: "100%",
              playerVars: {
                modestbranding: 1,
                rel: 0,
                controls: 1,
                autoplay: 0,
              },
            }}
            className="w-full h-full"
            onReady={onReady}
            onStateChange={onStateChange}
            onError={(e) => console.error("YouTube Player Error:", e)}
          />
        </div>

        {video.channel && (
          <p className="text-white text-sm mt-2">Channel: {video.channel}</p>
        )}
      </div>
    </div>
  );
};

export default VideoPlayer;