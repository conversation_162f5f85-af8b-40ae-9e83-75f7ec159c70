import React, { useEffect, useRef } from "react";

const VideoContainer = ({
  isAnyScreenSharing,
  isScreenSharing,
  screenShareUid,
  members,
  isVideoOn,
  isMuted,
  remoteVideoTracks,
  localVideoTrack,
  localVideoRef,
  videoRefs,
  screenVideoRef,
  remoteScreenTrack,
  localVideoKey,
  getVideoLayoutClass,
}) => {
  useEffect(() => {
    if (isScreenSharing && screenVideoRef.current) {
      console.log("Playing local screen share");
      screenVideoRef.current.srcObject = null; // Clear previous stream
      screenVideoRef.current.play();
    } else if (remoteScreenTrack && screenVideoRef.current) {
      console.log("Playing remote screen share");
      remoteScreenTrack.play(screenVideoRef.current);
    }
  }, [isScreenSharing, remoteScreenTrack]);

  useEffect(() => {
    if (!isAnyScreenSharing()) {
      Object.entries(remoteVideoTracks).forEach(([uid, track]) => {
        if (!videoRefs.current[uid]) {
          videoRefs.current[uid] = React.createRef();
        }
        const videoElement = videoRefs.current[uid]?.current;
        if (videoElement && track) {
          console.log(`Playing remote video for UID: ${uid}`);
          track.play(videoElement)?.catch((error) => {
            console.error(`Error playing remote video for UID ${uid}:`, error);
          });
        }
      });
    }
  }, [remoteVideoTracks, isAnyScreenSharing]);

  return (
    <div className="w-full h-full p-4 bg-gray-900">
      {isAnyScreenSharing() ? (
        <div className="relative w-full h-full bg-gray-800 rounded-lg overflow-hidden">
          <video
            ref={screenVideoRef}
            autoPlay
            playsInline
            className="w-full h-full object-contain"
          />
          <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white p-1 rounded">
            {isScreenSharing
              ? "Your Screen Share"
              : `${
                  members.find((m) => m.uid === screenShareUid)?.name ||
                  `User ${screenShareUid}`
                }'s Screen Share`}
          </div>
        </div>
      ) : (
        <div className={`w-full h-full ${getVideoLayoutClass()}`}>
          {isVideoOn && localVideoTrack && (
            <div
              key={`local-video-${localVideoKey}`}
              className="relative bg-gray-800 rounded-lg overflow-hidden"
              id="local-video-container"
            >
              <video
                ref={localVideoRef}
                autoPlay
                playsInline
                className="w-full h-full object-cover"
              />
              <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white p-1 rounded">
                You
              </div>
              <img
                src={isMuted ? "/svgs/micOff.svg" : "/svgs/micOn.svg"}
                alt="Mic status"
                className="absolute top-2 right-2 w-4 h-4"
              />
            </div>
          )}

          {Object.entries(remoteVideoTracks).map(([uid, track]) => (
            <div
              key={uid}
              className="relative bg-gray-800 rounded-lg overflow-hidden"
              id={`remote-video-container-${uid}`}
            >
              <video
                autoPlay
                playsInline
                ref={(el) => {
                  if (el) {
                    videoRefs.current[uid] = { current: el };
                    track.play(el);
                  }
                }}
                className="w-full h-full object-cover"
              />
              <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white p-1 rounded">
                {members.find((m) => m.uid === parseInt(uid))?.name ||
                  `User ${uid}`}
              </div>
            </div>
          ))}

          {!isVideoOn && Object.keys(remoteVideoTracks).length === 0 && (
            <div className="w-full h-full flex items-center justify-center text-white text-xl">
              Waiting for participants to join...
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default VideoContainer;