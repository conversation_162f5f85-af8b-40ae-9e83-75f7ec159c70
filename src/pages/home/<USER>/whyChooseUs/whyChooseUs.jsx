import React, { useEffect } from "react";
import { Button } from "../../../../components/button/button";
import { Card, CardContent } from "../../../../components/card/card";
import industrySpecific from "../../../../assets/svgs/industrySpecific.svg";
import professionalNetwork from "../../../../assets/svgs/professionalNetwork.svg";
import qualityAssurance from "../../../../assets/svgs/qualityAssurance.svg";
import tailoredCurriculum from "../../../../assets/svgs/tailoredCurriculum.svg";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

export const WhyChooseUs = () => {
	const user = useSelector((state) => state?.app?.userInfo?.user);
	const navigate = useNavigate();

	const bookFreeTrial = (e) => {
		e.stopPropagation();

		if (!user) {
			navigate("/signin");
			return;
		}

		if (user.role === "student") {
			navigate(`/student/my-lessons?tab=tutors`);
		} else if (user.role === "tutor") {
			navigate("/tutor/dashboard");
		}
	};

	const featureCards = [
		{
			id: 1,
			icon: industrySpecific,
			title: "Industry-Specific Learning",
			description:
				"Learn vocabulary, phrases, and communication styles relevant to your specific professional field from tutors with real industry experience.",
		},
		{
			id: 2,
			icon: professionalNetwork,
			title: "Professional Network",
			description:
				"Connect with tutors and fellow learners in your industry to build valuable international professional relationships.",
		},
		{
			id: 3,
			icon: tailoredCurriculum,
			title: "Tailored Curriculum",
			description:
				"Get personalized learning plans designed for your specific career goals, timeline, and learning style.",
		},
		{
			id: 4,
			icon: qualityAssurance,
			title: "Quality Assurance",
			description:
				"Every tutor is thoroughly vetted for industry expertise and teaching experience, ensuring high-quality language instruction.",
		},
	];

	return (
		<section className="w-full py-[50px] sm:px-8 px-3 bg-[#f2f7ff99]">
			<div className="flex flex-col items-center gap-12 max-w-[1440px] mx-auto">
				<div className="flex flex-col items-center gap-12 w-full">
					<div className="flex flex-col sm:items-center gap-3 max-w-[704px]">
						<h2 className="font-bold text-secondary sm:text-[46px] text-3xl sm:text-center tracking-normal leading-tight">
							Why Choose Convolly?
						</h2>
						<p className="text-gray-600 sm:text-lg text-base sm:text-center leading-relaxed">
							Experience the perfect blend of industry expertise and language
							learning, tailored specifically for professionals like you.
						</p>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 w-full">
						{featureCards.map((card) => (
							<Card
								key={card.id}
								className="bg-white rounded-2xl pb-4 hover:shadow-xl hover:scale-105 transition-all cursor-pointer"
							>
								<CardContent className="flex flex-col items-start gap-3 p-5 pt-10 h-full">
									<div className="flex w-16 h-16 items-center justify-center bg-[#1fc16b1a] rounded-3xl">
										<img
											className="w-[33.34px] h-8"
											alt="Icon"
											src={card.icon}
										/>
									</div>

									<div className="flex flex-col items-start gap-3 flex-1 w-full">
										<h3 className="font-bold text-secondary sm:text-[26px] text-xl tracking-tight leading-snug">
											{card.title}
										</h3>
										<p className="text-gray-600 sm:text-lg text-base tracking-normal leading-relaxed flex-1">
											{card.description}
										</p>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</div>

				<Button onClick={bookFreeTrial} className="h-[50px] w-[258px]">
					<span className="font-semibold text-base text-center tracking-normal leading-snug">
						Book Free Trial
					</span>
				</Button>
			</div>
		</section>
	);
};
