import React, { useEffect, useState } from "react";
import logo from "../../assets/svgs/logo.svg";
import signWithGoogle from "../../assets/svgs/signWithGoogle.svg";
import signWithLinkedIn from "../../assets/svgs/signInWithLinkedin.svg";
import loginBackground from "../../assets/images/loginBackground.png";
import { Button } from "../../components/button/button";
import { useNavigate } from "react-router-dom";
import usePost from "../../hooks/usePost";
import { useLoginMutation } from "../../redux/slices/authApiSlice";
import { jwtDecode } from "jwt-decode";
import { useDispatch } from "react-redux";
import { setUserInfo } from "@/redux/appSlice";
import Loader from "@/components/loader/loader";
import { useForm } from "react-hook-form";
import { usePostHog } from "@/hooks/usePosthog";
import InputField from "@/components/inputs";
import eyeOpen from "../../assets/svgs/eyeOpen.svg";
import eyeClosed from "../../assets/svgs/eyeClosed.svg";

const LoginPage = () => {
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [rememberMe, setRememberMe] = useState(false);
	const [isFormValid, setIsFormValid] = useState(false);
	const [touched, setTouched] = useState({
		email: false,
		password: false,
	});
	const [showPassword, setShowPassword] = useState(false);

	const {
		register,
		handleSubmit,
		control,
		reset,
		formState: { errors },
	} = useForm();

	const navigate = useNavigate();
	const dispatch = useDispatch();
	const { identify } = usePostHog();

	const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID;
	const LINKEDIN_CLIENT_ID = import.meta.env.VITE_LINKEDIN_CLIENT_ID;

	// use the generic post mutation hook to handle the login mutation
	const { handlePost: handleLogin, isLoading: loggingIn } =
		usePost(useLoginMutation);

	const loginUser = async (data) => {
		const res = await handleLogin(data);
		console.log(res?.data?.user?.role);

		if (res) {
			reset();
			// dispatch(setUserInfo({ ...res?.data }));
			dispatch(setUserInfo({ ...res?.data }));
			// send user details to posthog
			identify(res?.data?.user?.email, {
				email: res?.data?.user?.email,
				name: res?.data?.user?.fullname,
			});

			console.log(res?.data?.user?.role);

			if (res?.data?.user?.role == "student") {
				if (res?.data?.user?.onBoardingStatus == "new") {
					navigate("/student-onboarding");
				} else {
					navigate("/student/dashboard");
				}
			} else if (res?.data?.user?.role == "admin") {
				navigate("/admin/dashboard");
			} else {
				if (res?.data?.user?.approvalStatus == "approved") {
					navigate("/tutor/dashboard");
				} else {
					navigate("/tutor-onboarding");
				}
			}
		}
	};

	const handleGoogleCallback = async (response) => {
		// Decode the ID token to get user info
		const decodedToken = jwtDecode(response?.credential);

		// Extract user info from the decoded token
		const { given_name, family_name, email } = decodedToken;

		const userData = {
			firstname: given_name,
			lastname: family_name,
			email: email,
			provider: "google",
		};

		try {
			await loginUser(userData);
		} catch (err) {
			console.error("Error during Google signin:", err);
		}
	};

	const handleLinkedInCallback = async (response) => {
		// Extract access token from LinkedIn response
		const accessToken = response?.code;

		if (accessToken) {
			try {
				// Fetch user info from LinkedIn API
				const userResponse = await fetch("https://api.linkedin.com/v2/me", {
					headers: {
						Authorization: `Bearer ${accessToken}`,
						"cache-control": "no-cache",
						"X-Restli-Protocol-Version": "2.0.0",
					},
				});
				const userData = await userResponse.json();

				const emailResponse = await fetch(
					"https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))",
					{
						headers: {
							Authorization: `Bearer ${accessToken}`,
							"cache-control": "no-cache",
							"X-Restli-Protocol-Version": "2.0.0",
						},
					}
				);
				const emailData = await emailResponse.json();

				const userInfo = {
					firstname: userData.firstName.localized.en_US,
					lastname: userData.lastName.localized.en_US,
					email: emailData.elements[0]["handle~"].emailAddress,
					provider: "linkedin",
				};

				await loginUser(userInfo);
			} catch (err) {
				console.error("Error during LinkedIn signin:", err);
			}
		}
	};

	useEffect(() => {
		const loadGoogleScript = () => {
			const script = document.createElement("script");
			script.src = "https://accounts.google.com/gsi/client";
			script.async = true;
			script.defer = true;
			script.onload = () => {
				if (window.google && GOOGLE_CLIENT_ID) {
					window.google.accounts.id.initialize({
						client_id: GOOGLE_CLIENT_ID,
						callback: handleGoogleCallback,
					});

					window.google.accounts.id.renderButton(
						document.getElementById("googleSignInBtn"),
						{
							theme: "outline",
							size: "large",
							shape: "pill",
							width: "100%",
						}
					);
				}
			};
			script.onerror = () => {
				console.error("Failed to load Google script");
			};
			document.body.appendChild(script);
			return script;
		};

		const googleScript = loadGoogleScript();

		// LinkedIn script loading remains the same
		const linkedInScript = document.createElement("script");
		linkedInScript.src = "https://platform.linkedin.com/in.js";
		linkedInScript.async = true;
		linkedInScript.onload = () => {
			if (window.IN && LINKEDIN_CLIENT_ID) {
				window.IN.init({
					api_key: LINKEDIN_CLIENT_ID,
					authorize: true,
					onLoad: "onLinkedInLoad",
				});

				window.onLinkedInLoad = () => {
					document.getElementById("linkedinSignInBtn").onclick = () => {
						window.IN.User.authorize((response) => {
							handleLinkedInCallback(response);
						}, {});
					};
				};
			}
		};
		document.body.appendChild(linkedInScript);

		return () => {
			document.body.removeChild(googleScript);
			document.body.removeChild(linkedInScript);
		};
	}, [GOOGLE_CLIENT_ID, LINKEDIN_CLIENT_ID]);

	return (
		<div className="flex min-h-screen overflow-hidden relative">
			{loggingIn && <Loader />}

			<div
				className="hidden lg:flex fixed left-0 top-0 w-1/2 h-screen flex-col z-10"
				style={{
					backgroundImage: `url(${loginBackground})`,
					backgroundSize: "cover",
					backgroundPosition: "center",
				}}
			>
				<div className="absolute inset-0 bg-black/30 z-0" />
				<div className="relative z-10 w-full h-full p-12 flex flex-col justify-end">
					<h2 className="text-2xl text-white font-bold mb-2 leading-[50px]">
						Share Your Knowledge. Inspire Learners Worldwide.
					</h2>
					<p className="text-white max-w-[600px]">
						Join our global network of English tutors, connect with eager
						students, and earn by doing what you love—teaching.
					</p>
				</div>
			</div>

			<form
				className="ml-auto w-full lg:w-1/2 overflow-y-auto flex flex-col justify-center lg:p-12 md:p-8 p-5 min-h-screen"
				onSubmit={handleSubmit(loginUser)}
			>
				<img
					src={logo}
					alt="convolly logo"
					className="sm:w-[190px] w-[120px] mb-10"
				/>

				<h2 className="md:text-4xl text-2xl font-bold mb-2 sm:leading-[50px] leading-[30px]">
					Login to your account
				</h2>
				<p className="text-[#4B5563] sm:text-lg mb-8">Login to continue</p>

				{/* Social login buttons */}
				<div className="sm:flex items-center gap-5 mb-5">
					<div
						className="flex border-[1px] border-[#E8E8E8] rounded-[30px] gap-2 cursor-pointer grow p-1 px-3 justify-center max-sm:mb-5"
						id="googleSignInBtn"
					/>
				</div>

				<div className="flex items-center text-center mb-5">
					<hr className="flex-1 border-none h-[1px] bg-[#E8E8E8]" />
					<span className="py-0 px-[10px] text-xs border-[#E8E8E8] border rounded-md text-[#A4A4A4] bg-[#FAFAFA] p-1">
						OR
					</span>
					<hr className="flex-1 border-none h-[1px] bg-[#E8E8E8]" />
				</div>

				{/* Email Input */}
				<div className="mb-5">
					<label
						htmlFor="email"
						className="block text-sm font-medium text-gray-700 mb-1"
					>
						Email address
					</label>
					<input
						id="email"
						type="email"
						placeholder="Enter your email address"
						{...register("email", { required: "Email is required" })}
						disabled={loggingIn}
						className={`w-full border border-gray-300 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
							errors.email ? "border-red-500" : ""
						}`}
					/>
					{errors.email && (
						<p className="text-red-600 text-sm mt-1">{errors.email.message}</p>
					)}
				</div>

				{/* Password Input */}
				<div className="mb-5 relative">
					<label
						htmlFor="password"
						className="block text-sm font-medium text-gray-700 mb-1"
					>
						Password
					</label>
					<input
						id="password"
						type={showPassword ? "text" : "password"}
						placeholder="Enter your password"
						{...register("password", { required: "Password is required" })}
						disabled={loggingIn}
						className={`w-full border border-gray-300 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
							errors.password ? "border-red-500" : ""
						}`}
					/>
					<button
						type="button"
						onClick={() => setShowPassword(!showPassword)}
						className="absolute right-3 top-[38px] text-sm text-gray-600 hover:text-primary focus:outline-none"
					>
						{/* {showPassword ? eyeOpen : eyeClosed} */}
						<img src={showPassword ? eyeOpen : eyeClosed} alt="eye icon" />
					</button>
					{errors.password && (
						<p className="text-red-600 text-sm mt-1">
							{errors.password.message}
						</p>
					)}
				</div>

				<div className="flex justify-between items-center mb-7">
					<div className="flex items-center gap-2">
						<input
							type="checkbox"
							id="rememberMe"
							checked={rememberMe}
							onChange={(e) => setRememberMe(e.target.checked)}
							className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
						/>
						<label
							htmlFor="rememberMe"
							className="text-[#1A1A40] mb-1 max-sm:text-sm"
						>
							Remember me
						</label>
					</div>

					<a className="text-primary max-sm:text-sm" href="/reset-password">
						Forgot password?
					</a>
				</div>

				{errors.submit && (
					<p className="mb-3 text-sm text-red-600">{errors.submit}</p>
				)}

				<Button
					type="submit"
					className="w-full h-[50px] mb-3"
					disabled={loggingIn}
				>
					Sign in
				</Button>

				<p className="sm:text-lg">
					Don't have an account?{" "}
					<a className="text-primary font-semibold" href="/signup/student">
						Sign up
					</a>
				</p>
			</form>
		</div>
	);
};

export default LoginPage;
