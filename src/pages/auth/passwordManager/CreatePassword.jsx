import React, { useState } from "react";
import SuccessModal from "./SuccessModal";
import Top<PERSON>ogo from "./TopLogo";
import { useForm } from "react-hook-form";
import InputField from "../../../components/inputs";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/button/button";

const CreatePassword = () => {
	const [showModal, setShowModal] = useState(false);
	const navigate = useNavigate();

	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm();

	const onSubmit = (data) => {
		if (data.password !== data.confirmPassword) {
			// Handle mismatch
			alert("Passwords do not match");
			return;
		}
		setShowModal(true);
	};

	const handleContinue = () => {
		setShowModal(false);
		navigate("/");
	};

	return (
		<div className="min-h-screen bg-white flex flex-col">
			<TopLogo />

			<div className="flex flex-1 flex-col items-center justify-center h-[571px] px-4 py-8 sm:px-6">
				<div className="w-full max-w-[584px] space-y-8">
					<div className="text-center">
						<h1 className="text-xl sm:text-4xl text-[38px] font-bold text-[#1A1A40]">
							Create new password
						</h1>
						<p className="mt-2 font-medium sm:text-[18px] text-[#4B5563]">
							Your new password must be different from your previously used
							password
						</p>
					</div>

					<form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
						<div className="h-[278px] gap-7">
							{/* New password field */}
							<div className="h-[89px] gap-3">
								<div className="relative">
									<label
										htmlFor="password"
										className="block text-[18px] text-[#1A1A40] font-medium"
									>
										New password
									</label>
									<InputField
										register={register}
										fieldName="password"
										fieldType="password"
										placeHolder="Enter your password"
										isRequired={true}
										// validate={true}

										error={errors?.password?.message}
									/>
								</div>
							</div>
							<br />

							{/* Confirm password field */}
							<div className="h-[89px] gap-3">
								<div className="relative">
									<label
										htmlFor="confirmPassword"
										className="block text-[18px] text-[#1A1A40] font-medium"
									>
										Confirm new password
									</label>
									<InputField
										register={register}
										fieldName="confirmPassword"
										fieldType="password"
										placeHolder="Enter your password again"
										isRequired={true}
										validate={true}
										registerOptions={{
											pattern: {
												value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\W).{8,}$/,
												message:
													"Password must be at least 8 characters, include uppercase, lowercase, and a special character",
											},
										}}
										error={errors?.confirmPassword?.message}
									/>
								</div>
							</div>
						</div>

						<Button
							className="w-full h-[50px] mb-3"
							//   disabled={forgotPasswordLoading}
						>
							Reset password
						</Button>
					</form>
				</div>
			</div>

			<SuccessModal
				isOpen={showModal}
				onClose={() => setShowModal(false)}
				onContinue={handleContinue}
			/>
		</div>
	);
};

export default CreatePassword;
