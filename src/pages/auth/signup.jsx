import React, { useEffect } from "react";
import logo from "../../assets/svgs/logo.svg";
import InputField from "../../components/inputs";
import tutorSignupBackground from "../../assets/images/tutorSignupBackground.png";
import studentSignupBackground from "../../assets/images/studentSignupBackground.png";
import { Button } from "../../components/button/button";
import { useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { useLocalSignUpMutation } from "../../redux/slices/authApiSlice";
import usePost from "../../hooks/usePost";
import { jwtDecode } from "jwt-decode";
import Loader from "@/components/loader/loader";
import { generateKeyPair } from "@/utils/crypto";

const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID;

const SignupPage = () => {
	const {
		register,
		handleSubmit,
		reset,
		formState: { errors },
	} = useForm();

	const navigate = useNavigate();
	const { role } = useParams();

	const { handlePost: handleSignup, isLoading: signingUp } = usePost(
		useLocalSignUpMutation
	);

	const registerUser = async (data) => {
		const keyPair = await generateKeyPair();

		const res = await handleSignup({
			...data,
			role: role || "student",
			provider: data.provider || "local",
			encryptedData: keyPair,
		});

		if (res) {
			reset();
			navigate("/signin");
		}
	};

	const handleGoogleCallback = async (response) => {
		const decodedToken = jwtDecode(response?.credential);
		const { given_name, family_name, email } = decodedToken;

		const userData = {
			firstname: given_name,
			lastname: family_name,
			email,
			provider: "google",
		};

		try {
			await registerUser(userData);
		} catch (err) {
			console.error("Google signup error:", err);
		}
	};

	useEffect(() => {
		if (window.google) {
			window.google.accounts.id.initialize({
				client_id: GOOGLE_CLIENT_ID,
				callback: handleGoogleCallback,
			});
			window.google.accounts.id.renderButton(
				document.getElementById("googleSignUpBtn"),
				{
					theme: "outline",
					size: "large",
					shape: "pill",
					width: "100%",
				}
			);
		}
	}, []);

	return (
		<div className="flex min-h-screen overflow-hidden relative">
			{signingUp && <Loader />}

			{/* Left background section */}
			<div
				className="hidden lg:flex fixed left-0 top-0 w-1/2 h-full z-10 bg-cover bg-center"
				style={{
					backgroundImage: `url(${
						role === "student" ? studentSignupBackground : tutorSignupBackground
					})`,
				}}
			>
				<div className="absolute inset-0 bg-black/30 z-0" />
				<div className="relative z-10 w-full h-full p-12 flex flex-col justify-end">
					<h2 className="text-2xl text-white font-bold mb-2 leading-[50px]">
						{role === "student"
							? "Unlock Fluent English — One Lesson at a Time"
							: "Share Your Knowledge. Inspire Learners Worldwide."}
					</h2>
					<p className="text-white max-w-[600px]">
						{role === "student"
							? "Join thousands of learners improving their English skills with interactive lessons, real-time practice, and personalized guidance."
							: "Join our global network of English tutors, connect with eager students, and earn by doing what you love—teaching."}
					</p>
				</div>
			</div>

			{/* Signup form section */}
			<div className="ml-auto w-full lg:w-1/2 min-h-screen flex flex-col justify-center lg:p-12 md:p-8 p-5">
				<img src={logo} alt="logo" className="sm:w-[190px] w-[120px] mb-10" />

				<h2 className="md:text-4xl text-2xl font-bold mb-2 leading-[50px]">
					Create account
				</h2>
				<p className="text-[#4B5563] sm:text-lg mb-8">
					Start creating your tutor profile
				</p>

				{/* Google sign in */}
				<div className="mb-5 w-full">
					<div
						id="googleSignUpBtn"
						className="flex border border-[#E8E8E8] rounded-[30px] p-1 px-3 justify-center cursor-pointer"
					></div>
				</div>

				{/* OR Divider */}
				<div className="flex items-center text-center mb-5">
					<hr className="flex-1 h-[1px] bg-[#E8E8E8] border-none" />
					<span className="px-3 text-xs border border-[#E8E8E8] rounded-md text-[#A4A4A4] bg-[#FAFAFA]">
						OR
					</span>
					<hr className="flex-1 h-[1px] bg-[#E8E8E8] border-none" />
				</div>

				<form onSubmit={handleSubmit(registerUser)} className="space-y-4">
					<div className="sm:flex gap-5">
						<InputField
							label="First name"
							register={register}
							fieldName="firstname"
							placeHolder="Enter your first name"
							isRequired
							error={errors?.firstname?.message}
							disabled={signingUp}
						/>
						<InputField
							label="Last name"
							register={register}
							fieldName="lastname"
							placeHolder="Enter your last name"
							isRequired
							error={errors?.lastname?.message}
							disabled={signingUp}
						/>
					</div>

					<InputField
						label="Email address"
						register={register}
						fieldName="email"
						fieldType="email"
						placeHolder="Enter your email address"
						isRequired
						error={errors?.email?.message}
						disabled={signingUp}
					/>

					<InputField
						label="Password"
						register={register}
						fieldName="password"
						fieldType="password"
						placeHolder="Enter your password"
						isRequired
						registerOptions={{
							pattern: {
								value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\W).{8,}$/,
								message:
									"Password must be at least 8 characters, include uppercase, lowercase, and a special character",
							},
						}}
						error={errors?.password?.message}
						disabled={signingUp}
					/>

					<Button className="w-full h-[50px]" disabled={signingUp}>
						Save And Continue
					</Button>
				</form>

				<p className="sm:text-lg mt-6">
					Already have an account?{" "}
					<a className="text-primary font-semibold" href="/signin">
						Sign in
					</a>
				</p>
			</div>
		</div>
	);
};

export default SignupPage;
