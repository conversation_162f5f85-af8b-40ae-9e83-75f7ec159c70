import React from "react";

const Submenu = ({ isOpen, onClose, buttonRef }) => {
	if (!isOpen) return null;

	// Get button position to position the overlay
	const buttonRect = buttonRef.current.getBoundingClientRect();
	const style = {
		position: "fixed",
		top: buttonRect.bottom + window.scrollY,
		right: window.innerWidth - buttonRect.right - window.scrollX,
		transform: "translateX(50%)", // This helps align the right edge
	};

	return (
		<div className="submenu-container">
			<button
				onClick={onClose}
				className="fixed inset-0 bg-black bg-opacity-30 z-40"
			></button>
			<div
				className="absolute bg-white rounded-md shadow-lg mr-20 p-4 z-50 flex flex-col gap-3 w-[197px]"
				style={style}
			>
				<button className="text-left hover:text-primary hover:bg-gray-100 p-2 rounded">
					Message tutor
				</button>
				<button className="text-left hover:text-primary hover:bg-gray-100 p-2 rounded">
					Share tutor
				</button>
				<button className="text-left hover:text-primary hover:bg-gray-100 p-2 rounded">
					See tutor's profile
				</button>
			</div>
		</div>
	);
};

export default Submenu;
