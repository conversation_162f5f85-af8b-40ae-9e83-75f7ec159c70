import React from "react";
import tutor from "../../../assets/images/tutor1.png";
import Submenu from "./Submenu";

const MainCard = () => {
	const [showSubmenuOverlay, setShowSubmenuOverlay] = React.useState(false);
	const buttonRef = React.useRef(null);

	const toggleOverlay = (e) => {
		e.stopPropagation();
		setShowSubmenuOverlay(!showSubmenuOverlay);
	};

	const closeOverlay = () => {
		setShowSubmenuOverlay(false);
	};

	React.useEffect(() => {
		const handleClickOutside = (event) => {
			if (showSubmenuOverlay && !event.target.closest(".submenu-container")) {
				closeOverlay();
			}
		};

		document.addEventListener("click", handleClickOutside);
		return () => {
			document.removeEventListener("click", handleClickOutside);
		};
	}, [showSubmenuOverlay]);

	return (
		<div className="h-[177px] gap-5 w-full mx-auto sm:w-[528px] relative">
			<div className="h-[9px]">
				<div className="flex flex-row justify-between">
					<div className="flex">
						<img
							src={tutor}
							alt="tutor img"
							className="w-[78px] h-[90px] border-[#E8E8E8] rounded-md object-cover"
						/>
						<p className="px-4 ml-4 bg-[#1FC16B1A] h-6 rounded-lg text-primary">
							Join in 23 hours
						</p>
					</div>
					<div className="top-0 relative mr-2">
						<button
							className="sm:text-3xl text-2xl"
							onClick={toggleOverlay}
							ref={buttonRef}
						>
							...
						</button>
					</div>
				</div>
				<br />

				<div className="flex justify-between">
					<div>
						<p className="text-md sm:text-xl test-[#1A1A40] font-bold">
							May 10, Saturday, 10:00-12:30
						</p>
						<p className="text-md font-medium">English with John</p>
					</div>
					<button className="bg-primary px-3 py-1 rounded-md text-white font-medium text-lg">
						Join class
					</button>
				</div>
			</div>
			{showSubmenuOverlay && (
				<Submenu
					isOpen={showSubmenuOverlay}
					onClose={closeOverlay}
					buttonRef={buttonRef}
				/>
			)}
		</div>
	);
};

export default MainCard;
