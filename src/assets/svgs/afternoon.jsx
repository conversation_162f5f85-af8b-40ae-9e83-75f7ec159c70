import React from "react";

const Afternoon = ({ active = false }) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.1999 3.5999C13.1999 3.28164 13.0735 2.97642 12.8484 2.75137C12.6234 2.52633 12.3182 2.3999 11.9999 2.3999C11.6816 2.3999 11.3764 2.52633 11.1514 2.75137C10.9263 2.97642 10.7999 3.28164 10.7999 3.5999V4.7999C10.7999 5.11816 10.9263 5.42339 11.1514 5.64843C11.3764 5.87347 11.6816 5.9999 11.9999 5.9999C12.3182 5.9999 12.6234 5.87347 12.8484 5.64843C13.0735 5.42339 13.1999 5.11816 13.1999 4.7999V3.5999ZM18.7883 6.9083C19.0069 6.68198 19.1278 6.37886 19.1251 6.06422C19.1224 5.74958 18.9962 5.44861 18.7737 5.22612C18.5512 5.00363 18.2502 4.87743 17.9356 4.87469C17.6209 4.87196 17.3178 4.99291 17.0915 5.2115L16.2431 6.0599C16.0245 6.28623 15.9036 6.58935 15.9063 6.90398C15.909 7.21862 16.0352 7.5196 16.2577 7.74208C16.4802 7.96457 16.7812 8.09078 17.0958 8.09351C17.4105 8.09625 17.7136 7.97529 17.9399 7.7567L18.7883 6.9083ZM21.5999 11.9999C21.5999 12.3182 21.4735 12.6234 21.2484 12.8484C21.0234 13.0735 20.7182 13.1999 20.3999 13.1999H19.1999C18.8816 13.1999 18.5764 13.0735 18.3514 12.8484C18.1263 12.6234 17.9999 12.3182 17.9999 11.9999C17.9999 11.6816 18.1263 11.3764 18.3514 11.1514C18.5764 10.9263 18.8816 10.7999 19.1999 10.7999H20.3999C20.7182 10.7999 21.0234 10.9263 21.2484 11.1514C21.4735 11.3764 21.5999 11.6816 21.5999 11.9999ZM6.0599 7.7567C6.1706 7.87132 6.30301 7.96273 6.44942 8.02562C6.59582 8.08851 6.75329 8.12162 6.91262 8.123C7.07196 8.12439 7.22997 8.09403 7.37745 8.03369C7.52492 7.97335 7.65891 7.88425 7.77158 7.77158C7.88425 7.65891 7.97335 7.52492 8.03369 7.37745C8.09403 7.22997 8.12439 7.07196 8.123 6.91262C8.12162 6.75329 8.08851 6.59582 8.02562 6.44942C7.96273 6.30301 7.87132 6.1706 7.7567 6.0599L6.9083 5.2115C6.68198 4.99291 6.37886 4.87196 6.06422 4.87469C5.74958 4.87743 5.44861 5.00363 5.22612 5.22612C5.00363 5.44861 4.87743 5.74958 4.87469 6.06422C4.87196 6.37886 4.99291 6.68198 5.2115 6.9083L6.0599 7.7567ZM5.9999 11.9999C5.9999 12.3182 5.87347 12.6234 5.64843 12.8484C5.42339 13.0735 5.11816 13.1999 4.7999 13.1999H3.5999C3.28164 13.1999 2.97642 13.0735 2.75137 12.8484C2.52633 12.6234 2.3999 12.3182 2.3999 11.9999C2.3999 11.6816 2.52633 11.3764 2.75137 11.1514C2.97642 10.9263 3.28164 10.7999 3.5999 10.7999H4.7999C5.11816 10.7999 5.42339 10.9263 5.64843 11.1514C5.87347 11.3764 5.9999 11.6816 5.9999 11.9999ZM9.5999 19.1999V17.9999H14.3999V19.1999C14.3999 19.8364 14.147 20.4469 13.697 20.897C13.2469 21.347 12.6364 21.5999 11.9999 21.5999C11.3634 21.5999 10.7529 21.347 10.3028 20.897C9.85276 20.4469 9.5999 19.8364 9.5999 19.1999ZM14.3999 16.7999C14.4179 16.3919 14.6495 16.0247 14.9723 15.7691C15.7555 15.1514 16.3268 14.3051 16.6069 13.3477C16.8869 12.3903 16.8617 11.3695 16.5348 10.4271C16.2079 9.4847 15.5955 8.66756 14.7828 8.08924C13.9701 7.51093 12.9974 7.20017 11.9999 7.20017C11.0024 7.20017 10.0297 7.51093 9.21699 8.08924C8.40426 8.66756 7.79189 9.4847 7.46499 10.4271C7.13809 11.3695 7.1129 12.3903 7.39293 13.3477C7.67296 14.3051 8.24428 15.1514 9.0275 15.7691C9.3515 16.0247 9.5819 16.3919 9.5987 16.7999H14.4011H14.3999Z"
        fill={active ? "#3bb273" : "#A4A4A4"}
      />
    </svg>
  );
};

export default Afternoon;
