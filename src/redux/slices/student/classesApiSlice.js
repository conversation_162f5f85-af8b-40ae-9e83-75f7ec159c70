import { generalApiSlice } from "../../apiSlice";

export const classesApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		getClasses: builder.query({
			query: () => ({
				url: "/api/bookings/student",
				method: "GET",
			}),
		}),

		getTutorClasses: builder.query({
			query: () => ({
				url: `/api/bookings/tutor-detailed`,
				method: "GET",
			}),
		}),
	}),
	overrideExisting: false,
});

export const { useGetClassesQuery, useGetTutorClassesQuery } = classesApiSlice;
