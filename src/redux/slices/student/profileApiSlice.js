import { generalApiSlice } from "../../apiSlice";

export const profileApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		// Student Profile Management
		updateStudentProfile: builder.mutation({
			query: ({ id, payload }) => ({
				url: `/api/profile/students/${id}`,
				method: "PUT",
				body: payload,
			}),
			invalidatesTags: (result, error, { id }) => [
				{ type: "Profile", id: `student-${id}` },
				{ type: "Profile", id: "LIST" },
			],
		}),

		getStudentProfile: builder.query({
			query: (id) => ({
				url: `/api/profile/students/${id}`,
				method: "GET",
			}),
			providesTags: (result, error, id) => [
				{ type: "Profile", id: `student-${id}` },
			],
		}),

		// Tutor Profile Management
		updateTutorProfile: builder.mutation({
			query: ({ id, payload }) => ({
				url: `/api/profile/tutors/${id}`,
				method: "PUT",
				body: payload,
			}),
			invalidatesTags: (result, error, { id }) => [
				{ type: "Profile", id: `tutor-${id}` },
				{ type: "Profile", id: "LIST" },
			],
		}),

		getTutorProfile: builder.query({
			query: (id) => ({
				url: `/api/profile/tutors/${id}`,
				method: "GET",
			}),
			providesTags: (result, error, id) => [
				{ type: "Profile", id: `tutor-${id}` },
			],
		}),

		// Get complete tutor profile with statistics (for authenticated tutors)
		getTutorCompleteProfile: builder.query({
			query: () => ({
				url: `/api/profile/tutors/my-profile`,
				method: "GET",
			}),
			providesTags: ["TutorCompleteProfile"],
		}),
	}),

	overrideExisting: false,
});

export const {
	useUpdateStudentProfileMutation,
	useUpdateTutorProfileMutation,
	useGetStudentProfileQuery,
	useGetTutorProfileQuery,
	useGetTutorCompleteProfileQuery,
} = profileApiSlice;
