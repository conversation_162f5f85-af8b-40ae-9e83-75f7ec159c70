import { generalApiSlice } from "../../apiSlice";

const notificationSettingsApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get user's notification settings
    getNotificationSettings: builder.query({
      query: () => ({
        url: `/api/notification-settings`,
        method: "GET",
      }),
      providesTags: ["NotificationSettings"],
    }),

    // Update user's notification settings
    updateNotificationSettings: builder.mutation({
      query: (body) => ({
        url: `/api/notification-settings`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["NotificationSettings"],
    }),

    // Update lesson reminder timing
    updateLessonReminderTiming: builder.mutation({
      query: (body) => ({
        url: `/api/notification-settings/reminder-timing`,
        method: "PATCH",
        body,
      }),
      invalidatesTags: ["NotificationSettings"],
    }),
  }),

  overrideExisting: false,
});

export const {
  useGetNotificationSettingsQuery,
  useUpdateNotificationSettingsMutation,
  useUpdateLessonReminderTimingMutation,
} = notificationSettingsApiSlice;
