import { generalApiSlice } from "../../apiSlice";

export const findTutorApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getTutors: builder.query({
      query: () => ({
        url: "/api/profile/tutors",
        method: "GET"
      })
    }),

    getTutorDetails: builder.query({
      query: (id) => ({
        url: `/api/profile/tutors/${id}`,
        method: "GET"
      })
    })
  }),
  overrideExisting: false
});

// This automatically generates useGetTutorDetailsQuery
export const { useGetTutorDetailsQuery, useGetTutorsQuery } = findTutorApiSlice;
