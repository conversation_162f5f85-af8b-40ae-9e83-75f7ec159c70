import { generalApiSlice } from "../../apiSlice";

const studentSettingsApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		// Update student profile settings using the specified endpoint
		updateStudentProfileSettings: builder.mutation({
			query: ({ studentId, ...body }) => ({
				url: `/api/profile/students/${studentId}`,
				method: "PUT",
				body,
			}),
			invalidatesTags: (result, error, { studentId }) => [
				{ type: "Profile", id: `student-${studentId}` },
				{ type: "Profile", id: "LIST" },
			],
		}),

		// Legacy mutation for backward compatibility
		getUpdateStudentProfile: builder.mutation({
			query: ({ userId, ...body }) => ({
				url: `/api/profile/students/${userId}`,
				method: "PUT",
				body,
			}),
			invalidatesTags: (result, error, { userId }) => [
				{ type: "Profile", id: `student-${userId}` },
				{ type: "Profile", id: "LIST" },
			],
		}),

		// Get student profile for settings
		getStudentProfileSettings: builder.query({
			query: (studentId) => ({
				url: `/api/profile/students/${studentId}`,
				method: "GET",
			}),
			providesTags: (result, error, studentId) => [
				{ type: "Profile", id: `student-${studentId}` },
			],
		}),
	}),

	overrideExisting: false,
});

export const {
	useUpdateStudentProfileSettingsMutation,
	useGetUpdateStudentProfileMutation,
	useGetStudentProfileSettingsQuery,
} = studentSettingsApiSlice;
