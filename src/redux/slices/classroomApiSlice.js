import { generalApiSlice } from "../apiSlice";

const classroomApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // get rtc token
    retrieveClassToken: builder.mutation({
      query: (body) => ({
        url: `/api/classroom/rtc`,
        method: "POST",
        body
      })
    }),

    // get tutor details
    getTutorDetails: builder.query({
      query: (id) => ({
        url: `/api/profile/tutors/${id}`,
        method: "GET"
      })
    }),

    // get student details
    getStudentDetails: builder.query({
      query: (id) => ({
        url: `/api/profile/students/${id}`,
        method: "GET"
      })
    })
  }),

  overrideExisting: false
});

export const { useRetrieveClassTokenMutation } = classroomApiSlice;
