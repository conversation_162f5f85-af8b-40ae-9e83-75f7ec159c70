import { generalApiSlice } from "../apiSlice";

const chatApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // get chats
    getUserChats: builder.mutation({
      query: ({ userId, body }) => ({
        url: `/api/chat/user-conversations/${userId}`,
        method: "POST",
        body
      })
    }),

    // get messages of a chat
    getUserConversations: builder.mutation({
      query: ({ userId, conversationId, body }) => ({
        url: `/api/chat/conversation-messages/${userId}/${conversationId}`,
        method: "POST",
        body
      })
    }),

    // get chat user ......... will be deleted later
    getChatUsers: builder.query({
      query: () => ({
        url: `/api/chat-users`,
        method: "GET"
      })
    })
  }),

  overrideExisting: false
});

export const {
  useGetUserChatsMutation,
  useGetUserConversationsMutation,
  useGetChatUsersQuery
} = chatApiSlice;
