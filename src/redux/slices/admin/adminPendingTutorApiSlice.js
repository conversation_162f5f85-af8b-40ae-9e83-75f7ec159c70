import { generalApiSlice } from "../../apiSlice";

const adminPendingTutorApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		getPendingTutors: builder.query({
			query: () => ({
				url: `/api/admin/tutors/pending`,
				method: "GET",
			}),
		}),
		getPendingTutorDetails: builder.query({
			query: (id) => ({
				url: `/api/admin/tutors/${id}`,
				method: "GET",
			}),
		}),
		rejectPendingTutor: builder.mutation({
			query: ({ id, rejectionReason }) => ({
				url: `/api/admin/tutors/${id}/reject`,
				method: "PATCH",
				body: { rejectionReason },
			}),
		}),

		approvePendingTutor: builder.mutation({
			query: (id) => ({
				url: `/api/admin/tutors/${id}/approve`,
				method: "PATCH",
			}),
		}),
	}),
	overrideExisting: false,
});

export const {
	useGetPendingTutorsQuery,
	useGetPendingTutorDetailsQuery,
	useApprovePendingTutorMutation, // Note the Mutation suffix
	useRejectPendingTutorMutation, // Note the Mutation suffix
} = adminPendingTutorApiSlice;
