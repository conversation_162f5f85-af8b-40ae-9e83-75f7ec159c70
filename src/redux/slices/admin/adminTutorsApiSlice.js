import { generalApiSlice } from "../../apiSlice";

const adminTutorsApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getAllTutors: builder.query({
      query: () => ({
        url: `/api/admin/tutors`,
        method: "GET"
      })
    }),

    getTutorDetails: builder.query({
      query: (id) => ({
        url: `/api/admin/tutors/${id}`,
        method: "GET"
      })
    })
  }),

  overrideExisting: false
});

export const { useGetTutorDetailsQuery, useGetAllTutorsQuery } =
  adminTutorsApiSlice;
