import { generalApiSlice } from "../../apiSlice";

const adminSubAccountsApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getSubAdmins: builder.query({
      query: () => ({
        url: `/api/admin/admins`,
        method: "GET"
      })
    }),

    createSubAdmins: builder.mutation({
      query: (body) => ({
        url: `/api/admin/admins`,
        method: "POST",
        body
      })
    })
  }),

  overrideExisting: false
});

export const { useGetSubAdminsQuery, useCreateSubAdminsMutation } = adminSubAccountsApiSlice;
