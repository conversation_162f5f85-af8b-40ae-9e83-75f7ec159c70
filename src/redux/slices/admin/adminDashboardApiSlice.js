import { generalApiSlice } from "../../apiSlice";

const adminDashboardApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getDashboardStats: builder.query({
      query: () => ({
        url: `/api/admin/dashboard`,
        method: "GET"
      })
    }),

    getDashboardAnalytics: builder.query({
      query: () => ({
        url: `/api/admin/analytics`,
        method: "GET"
      })
    }),

    getDashboardTransactiona: builder.query({
      query: () => ({
        url: `/api/admin/financial/transactions`,
        method: "GET"
      })
    })
  }),

  overrideExisting: false
});

export const { useGetDashboardAnalyticsQuery, useGetDashboardStatsQuery, useGetDashboardTransactionaQuery } =
  adminDashboardApiSlice;
