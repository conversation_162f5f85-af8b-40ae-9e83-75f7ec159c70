import { NextFunction, Request, Response } from "express";
import postHogClient from "../config/posthog";
import { getReqSessionId } from "../utils/misc";

export const trackPageView = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const sessionId = getReqSessionId(req);

    postHogClient.capture({
      distinctId: sessionId,
      event: "page_view",
      properties: {
        path: req.originalUrl,
        method: req.method,
        userAgent: req.headers["user-agent"],
      },
    });
    next();
  } catch (err) {
    next(err);
  }
};
