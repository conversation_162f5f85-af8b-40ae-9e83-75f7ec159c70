import rateLimit from "express-rate-limit";
import { RateLimiterMemory } from "rate-limiter-flexible";

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000000, // max requests
  message: "Too many requests, try again later.",
  headers: true,
});

export const socketIOLimiter = new RateLimiterMemory({
  keyPrefix: "socket-io-limit",
  points: 8, // 8 connections
  duration: 60, // per 60 seconds
});

export default limiter;
