import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { createErrorResponse } from "./errorHandler";
import { getProfile } from "../utils/profile";
import { IProfile } from "../models/profile";
import { ERROR_MISSING_PROVIDER } from "../config/constants";

export interface JwtSignPayload {
  id: string;
  role: string;
  iat?: number;
  exp?: number;
  jti?: string; // JWT ID for token tracking
}

export interface AuthOptions {
  strictVerification?: boolean;
  role?: IProfile["role"] | IProfile["role"][];
  withUserId?: boolean;
  optional?: boolean; // Allow requests without authentication
  skipProfileFetch?: boolean; // Skip fetching full profile for performance
  requireActiveSession?: boolean; // Check if user session is active
}

// Token blacklist for logout functionality (in production, use Redis)
const tokenBlacklist = new Set<string>();

// Rate limiting for failed auth attempts
const failedAttempts = new Map<
  string,
  { count: number; lastAttempt: number }
>();
const MAX_FAILED_ATTEMPTS = 5;
const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes

// Helper function to check rate limiting
const checkRateLimit = (identifier: string): boolean => {
  const now = Date.now();
  const attempts = failedAttempts.get(identifier);

  if (attempts) {
    if (now - attempts.lastAttempt > LOCKOUT_DURATION) {
      failedAttempts.delete(identifier);
      return true;
    }
    return attempts.count < MAX_FAILED_ATTEMPTS;
  }
  return true;
};

// Helper function to record failed attempt
const recordFailedAttempt = (identifier: string): void => {
  const now = Date.now();
  const attempts = failedAttempts.get(identifier) || {
    count: 0,
    lastAttempt: now,
  };

  attempts.count += 1;
  attempts.lastAttempt = now;
  failedAttempts.set(identifier, attempts);
};

// Helper function to clear failed attempts on success
const clearFailedAttempts = (identifier: string): void => {
  failedAttempts.delete(identifier);
};

// Add token to blacklist
export const blacklistToken = (token: string): void => {
  tokenBlacklist.add(token);
};

// Check if token is blacklisted
const isTokenBlacklisted = (token: string): boolean => {
  return tokenBlacklist.has(token);
};

export const decodeJWT = (token: string) => {
  const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JwtSignPayload;

  return decoded;
};

export const verifyJWT = async (
  req: Request,
  res: Response,
  next: NextFunction,
  options: { optional?: boolean } = {}
) => {
  const authHeader = req.headers.authorization;
  const clientIp = req.ip || req.socket.remoteAddress || "unknown";

  // If authentication is optional and no header provided
  if (options.optional && (!authHeader || !authHeader.startsWith("Bearer "))) {
    req.user = null;
    next();
    return;
  }

  // Check rate limiting
  if (!checkRateLimit(clientIp)) {
    createErrorResponse(
      res,
      {
        message:
          "Too many failed authentication attempts. Please try again later.",
        details: { lockoutDuration: LOCKOUT_DURATION / 1000 / 60 }, // minutes
      },
      429
    );
    return;
  }

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    recordFailedAttempt(clientIp);
    createErrorResponse(
      res,
      {
        message: "Unauthorized: Missing or invalid authorization header",
        details: { expected: "Bearer <token>" },
      },
      401
    );
    return;
  }

  const token = authHeader.split(" ")[1];

  if (!token) {
    recordFailedAttempt(clientIp);
    createErrorResponse(res, "Unauthorized: Token not provided", 401);
    return;
  }

  // Check if token is blacklisted
  if (isTokenBlacklisted(token)) {
    recordFailedAttempt(clientIp);
    createErrorResponse(
      res,
      {
        message: "Unauthorized: Token has been revoked",
        details: { reason: "Token blacklisted" },
      },
      401
    );
    return;
  }

  try {
    // Verify JWT secret exists
    if (!process.env.JWT_SECRET) {
      createErrorResponse(res, "Authentication service unavailable", 503);
      return;
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET) as JwtSignPayload;

    if (!decoded || !decoded.id || !decoded.role) {
      recordFailedAttempt(clientIp);
      createErrorResponse(
        res,
        {
          message: "Invalid token: Missing required claims",
          details: { required: ["id", "role"] },
        },
        401
      );
      return;
    }

    // Check token expiration (additional check)
    if (decoded.exp && decoded.exp < Math.floor(Date.now() / 1000)) {
      recordFailedAttempt(clientIp);
      createErrorResponse(
        res,
        {
          message: "Token expired",
          details: { expiredAt: new Date(decoded.exp * 1000) },
        },
        401
      );
      return;
    }

    // Clear failed attempts on successful verification
    clearFailedAttempts(clientIp);

    // Store basic user info from token
    req.user = {
      id: decoded.id,
      role: decoded.role,
      tokenId: decoded.jti,
      issuedAt: decoded.iat ? new Date(decoded.iat * 1000) : undefined,
      expiresAt: decoded.exp ? new Date(decoded.exp * 1000) : undefined,
    };

    next();
  } catch (err: any) {
    recordFailedAttempt(clientIp);

    let errorMessage = "Invalid or expired token";
    let errorDetails: any = {};

    if (err.name === "TokenExpiredError") {
      errorMessage = "Token has expired";
      errorDetails = { expiredAt: err.expiredAt };
    } else if (err.name === "JsonWebTokenError") {
      errorMessage = "Invalid token format";
      errorDetails = { reason: err.message };
    } else if (err.name === "NotBeforeError") {
      errorMessage = "Token not active yet";
      errorDetails = { activeAt: err.date };
    }

    createErrorResponse(
      res,
      {
        message: errorMessage,
        details: errorDetails,
      },
      401
    );
    return;
  }
};

export const isAuthenticated = (opts?: AuthOptions) => [
  (req: Request, res: Response, next: NextFunction) =>
    verifyJWT(req, res, next, { optional: opts?.optional }),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const {
        strictVerification = false, // Changed default to false for flexibility
        role,
        withUserId = false, // Changed default to false
        optional = false,
        skipProfileFetch = false,
        requireActiveSession = true,
      } = opts || {};

      // If optional and no user, continue
      if (optional && !req.user) {
        next();
        return;
      }

      // Ensure user exists after JWT verification
      if (!req.user) {
        createErrorResponse(
          res,
          {
            message: "Authentication required",
            details: { reason: "No user found after token verification" },
          },
          401
        );
        return;
      }

      // Role-based access control
      if (role) {
        const allowedRoles = Array.isArray(role) ? role : [role];
        if (!allowedRoles.includes(req.user.role as any)) {
          createErrorResponse(
            res,
            {
              message: "Insufficient permissions",
              details: {
                required: allowedRoles,
                current: req.user.role,
              },
            },
            403
          );
          return;
        }
      }

      // User ID verification (if required)
      if (withUserId && strictVerification) {
        const expectedUserId =
          req.params.id || req.params.userId || req.body.userId;
        if (expectedUserId && req.user.id !== expectedUserId) {
          createErrorResponse(
            res,
            {
              message: "Access denied: User ID mismatch",
              details: {
                reason: "Cannot access another user's resources",
              },
            },
            403
          );
          return;
        }
      }

      // Skip profile fetch for performance if requested
      if (skipProfileFetch) {
        next();
        return;
      }

      // Fetch full profile from database
      try {
        const fullProfile = await getProfile({
          id: req.user.id,
          role: req.user.role,
        });

        if (!fullProfile) {
          createErrorResponse(
            res,
            {
              message: "User profile not found",
              details: {
                userId: req.user.id,
                role: req.user.role,
              },
            },
            404
          );
          return;
        }

        if (!fullProfile.provider) {
          createErrorResponse(res, ERROR_MISSING_PROVIDER);
          return;
        }

        // Check if user session is active (if required)
        if (requireActiveSession && !fullProfile.isLoggedIn) {
          createErrorResponse(
            res,
            {
              message: "Session expired: Please log in again",
              details: {
                reason: "User session is not active",
                lastLogin: fullProfile.lastLoggedAt,
              },
            },
            401
          );
          return;
        }

        // Replace basic user info with full profile
        req.user = fullProfile;
        next();
      } catch (profileError: any) {
        createErrorResponse(
          res,
          {
            message: "Failed to load user profile",
            details: { error: profileError.message },
          },
          500
        );
        return;
      }
    } catch (err: any) {
      next(err);
    }
  },
];

// Convenience middleware for optional authentication
export const optionalAuth = (opts?: Omit<AuthOptions, "optional">) =>
  isAuthenticated({ ...opts, optional: true });

// Convenience middleware for role-based authentication
export const requireRole = (
  role: IProfile["role"] | IProfile["role"][],
  opts?: Omit<AuthOptions, "role">
) => isAuthenticated({ ...opts, role });

// Convenience middleware for admin-only access
export const requireAdmin = (opts?: Omit<AuthOptions, "role">) =>
  isAuthenticated({ ...opts, role: "admin" });

// Convenience middleware for student-only access
export const requireStudent = (opts?: Omit<AuthOptions, "role">) =>
  isAuthenticated({ ...opts, role: "student" });

// Convenience middleware for tutor-only access
export const requireTutor = (opts?: Omit<AuthOptions, "role">) =>
  isAuthenticated({ ...opts, role: "tutor" });

// Middleware for high-performance routes that don't need full profile
export const lightAuth = (opts?: AuthOptions) =>
  isAuthenticated({ ...opts, skipProfileFetch: true });

// Logout middleware that blacklists the current token
export const logout = (req: Request, _res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;

  if (authHeader && authHeader.startsWith("Bearer ")) {
    const token = authHeader.split(" ")[1];
    if (token) {
      blacklistToken(token);
    }
  }
  next();
};
