import express from "express";
import { isAuthenticated, verifyJWT } from "../middlewares/auth";
import {
  createClassroom,
  getClassroomData,
  getClassroomRTCData,
  updateTimeLog,
} from "../controllers/classroom";
import { withRequestBody } from "../middlewares/misc";

const classRoomRouter = express.Router();

classRoomRouter.post(
  "/rtc",
  withRequestBody(),
  isAuthenticated({ strictVerification: false }),
  getClassroomRTCData
);

classRoomRouter.patch(
  "/:sessionId/time-log",
  verifyJWT,
  withRequestBody(),
  updateTimeLog
);

classRoomRouter.post(
  "/create",
  withRequestBody(),
  isAuthenticated({ role: "tutor", withUserId: false }),
  createClassroom
);

classRoomRouter.get("/:id", getClassroomData);

export default classRoomRouter;
