import { Router } from 'express';
import {
  getUserTransactions,
  getTransactionById,
  getUserFinancialSummary,
  createManualTransaction,
  getTransactionStatistics
} from '../controllers/transactionController';
import { isAuthenticated } from '../middlewares/auth';
import { withRequestBody } from '../middlewares/misc';

const transactionRouter = Router();

// ============================================================================
// USER TRANSACTION ROUTES
// ============================================================================

// Get user's transaction history
transactionRouter.get(
  '/my-transactions',
  isAuthenticated(),
  getUserTransactions
);

// Get user's financial summary
transactionRouter.get(
  '/my-summary',
  isAuthenticated(),
  getUserFinancialSummary
);

// Get specific transaction by ID
transactionRouter.get(
  '/:transactionId',
  isAuthenticated(),
  getTransactionById
);

// ============================================================================
// ADMIN TRANSACTION ROUTES
// ============================================================================

// Create manual transaction (Admin only)
transactionRouter.post(
  '/manual',
  withRequestBody(),
  isAuthenticated({ role: 'admin' }),
  createManualTransaction
);

// Get transaction statistics (Admin only)
transactionRouter.get(
  '/admin/statistics',
  isAuthenticated({ role: 'admin' }),
  getTransactionStatistics
);

export default transactionRouter;
