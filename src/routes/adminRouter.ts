import { Router, Response, NextFunction } from "express";
import {
  // Dashboard & Analytics
  getDashboardStats,
  getAnalytics,

  // Tutor Management
  approveTutor,
  rejectTutor,
  getPendingTutors,
  getAllTutors,
  getTutorById,
  flagTutor,
  unflagTutor,
  suspendTutor,
  deleteTutor,

  // Student Management
  getAllStudents,
  getStudentById,
  suspendStudent,
  deleteStudent,

  // Subscription Management
  getAllSubscriptions,
  getSubscriptionById,
  cancelSubscription,

  // Content Moderation
  getFlaggedItems,
  moderateContent,

  // Admin Management
  getAllAdmins,
  createAdmin,
  updateAdminPermissions,

  // Financial Management
  getFinancialOverview,
  getAllTransactions,
  getTransactionById,
  getAllEscrowTransactions,
  releaseEscrowFunds,
  processRefund,
  getFinancialReports,
  getPaymentMethodAnalytics,
  getTutorPayoutSummary,

  // Withdrawal Management
  getAllWithdrawalRequests,
  getWithdrawalRequestById,
  approveWithdrawalRequest,
  rejectWithdrawalRequest,
  processWithdrawalRequest,
  getWithdrawalStatistics,
  updatePayoutStatus,
  bulkUpdatePayoutStatus,
  getPayoutStatusAnalytics,

  // Refund Management
  getAllRefundRequests,
  getRefundRequestByIdAdmin,
  approveRefundRequest,
  rejectRefundRequest,
  processRefundRequest,
  getRefundStatistics,

  // Transaction Management
  getAllTransactionsAdmin,
  getTransactionByIdAdmin,
  createManualTransactionAdmin,
  updateTransactionStatus,
  getTransactionAnalytics,
  bulkUpdateTransactions,
  getTransactionsByUser,
  exportTransactions,
} from "../controllers/admin";
import { requireAdmin } from "../middlewares/auth";
import { withRequestBody } from "../middlewares/misc";
import { AuthRequest } from "../types/AuthRequest";
import Admin, { AdminPermission } from "../models/admin";
import { logError } from "../utils/logger";
import { generateEncryptionData } from "../utils/hashing";

// ============================================================================
// ADMIN LOGGING MIDDLEWARE
// ============================================================================

interface AdminLogEntry {
  adminId: string;
  adminEmail: string;
  action: string;
  resource: string;
  resourceId?: string;
  method: string;
  path: string;
  userAgent?: string;
  ip: string;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
  requestBody?: any;
  responseStatus?: number;
}

// Admin activity logger middleware
const logAdminActivity = (action: string, resource: string) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    const originalSend = res.send;
    const startTime = Date.now();

    res.send = function (body: any) {
      const duration = Date.now() - startTime;
      const success = res.statusCode < 400;

      const logEntry: AdminLogEntry = {
        adminId: req.user?._id?.toString() || "unknown",
        adminEmail: req.user?.email || "unknown",
        action,
        resource,
        resourceId:
          req.params.tutorId ||
          req.params.studentId ||
          req.params.subscriptionId ||
          req.params.adminId,
        method: req.method,
        path: req.path,
        userAgent: req.get("User-Agent"),
        ip: req.ip || req.socket?.remoteAddress || "unknown",
        timestamp: new Date(),
        success,
        errorMessage: success
          ? undefined
          : typeof body === "string"
          ? body
          : JSON.stringify(body),
        requestBody: req.method !== "GET" ? req.body : undefined,
        responseStatus: res.statusCode,
      };

      // Log to file with appropriate action type
      const actionType = req.method.toLowerCase() as any;
      logError(`Admin Activity: ${action} on ${resource}`, actionType, {
        ...logEntry,
        duration: `${duration}ms`,
        success: success ? "YES" : "NO",
      });

      // Log errors to console for immediate visibility
      if (!success) {
        console.error(`Admin Action Failed:`, {
          admin: `${logEntry.adminEmail} (${logEntry.adminId})`,
          action: `${action} on ${resource}`,
          path: req.path,
          method: req.method,
          status: res.statusCode,
          error: logEntry.errorMessage,
          duration: `${duration}ms`,
          timestamp: logEntry.timestamp.toISOString(),
        });
      } else {
        console.log(`🟢 Admin Action Success:`, {
          admin: `${logEntry.adminEmail} (${logEntry.adminId})`,
          action: `${action} on ${resource}`,
          path: req.path,
          method: req.method,
          duration: `${duration}ms`,
        });
      }

      return originalSend.call(this, body);
    };

    next();
  };
};

// Request logging middleware for admin routes
const logAdminRequest = (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  const startTime = Date.now();
  const adminInfo = req.user
    ? `${req.user.email} (${req.user._id})`
    : "Unknown Admin";

  console.log(`📋 Admin Request: ${req.method} ${req.path}`, {
    admin: adminInfo,
    ip: req.ip || req.socket?.remoteAddress,
    userAgent: req.get("User-Agent"),
    timestamp: new Date().toISOString(),
    query: Object.keys(req.query).length > 0 ? req.query : undefined,
    body: req.method !== "GET" && req.body ? "***BODY_PRESENT***" : undefined,
  });

  const originalSend = res.send;
  res.send = function (body: any) {
    const duration = Date.now() - startTime;
    console.log(
      `📋 Admin Response: ${req.method} ${req.path} - ${res.statusCode}`,
      {
        admin: adminInfo,
        duration: `${duration}ms`,
        status: res.statusCode >= 400 ? "ERROR" : "SUCCESS",
      }
    );
    return originalSend.call(this, body);
  };

  next();
};

const adminRouter = Router();

// ============================================================================
// BOOTSTRAP ROUTE (No Authentication Required)
// ============================================================================

// Special route for creating the first super admin - bypasses authentication
adminRouter.post(
  "/bootstrap/super-admin",
  withRequestBody(),
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      // Check if any admin already exists
      const existingAdminCount = await Admin.countDocuments();

      if (existingAdminCount > 0) {
        res.status(403).json({
          success: false,
          message: "Bootstrap denied: Admin users already exist",
          details: {
            reason: "This endpoint can only be used when no admin users exist",
            existingAdmins: existingAdminCount,
          },
        });
        return;
      }

      const {
        firstname,
        lastname,
        email,
        password,
        department = "System Administration",
      } = req.body;

      // Validate required fields
      if (!firstname || !lastname || !email || !password) {
        res.status(400).json({
          success: false,
          message: "Missing required fields",
          details: {
            required: ["firstname", "lastname", "email", "password"],
          },
        });
        return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        res.status(400).json({
          success: false,
          message: "Invalid email format",
        });
        return;
      }

      // Validate password strength
      if (password.length < 8) {
        res.status(400).json({
          success: false,
          message: "Password must be at least 8 characters long",
        });
        return;
      }

      // Generate encryption data for the admin profile
      const encryptionData = generateEncryptionData();

      // Create super admin with all permissions
      const superAdmin = new Admin({
        firstname,
        lastname,
        email,
        password, // Will be hashed by pre-save middleware
        role: "admin",
        isSuperAdmin: true,
        adminLevel: 5, // Highest level
        department,
        permissions: Object.values(AdminPermission), // All permissions
        isActive: true,
        createdBy: null, // Bootstrap user has no creator
        encryptedData: encryptionData, // Add required encryption data
        settings: {
          theme: "light",
          language: "en",
          timezone: "UTC",
          emailNotifications: {
            newTutorApplications: true,
            flaggedContent: true,
            systemAlerts: true,
            financialReports: true,
          },
          dashboardPreferences: {
            defaultView: "overview",
            autoRefresh: true,
            refreshInterval: 300,
          },
        },
      });

      await superAdmin.save();

      // Remove password from response
      const adminResponse = superAdmin.toObject();
      delete adminResponse.password;
      delete adminResponse.twoFactorSecret;

      // Log the bootstrap action
      console.log(`🚀 Super Admin Bootstrap: ${email} created successfully`);
      logError("Super Admin Bootstrap", "post", {
        adminEmail: email,
        adminId: superAdmin._id,
        timestamp: new Date().toISOString(),
        success: true,
      });

      res.status(201).json({
        success: true,
        message:
          "Super Admin created successfully! You can now login with these credentials.",
        data: {
          admin: adminResponse,
          loginEndpoint: "/api/auth/login",
          nextSteps: [
            "Login using the email and password you provided",
            "Access admin dashboard at /api/admin/dashboard",
            "Create additional admin users if needed",
            "Configure system settings",
          ],
        },
      });
    } catch (error: any) {
      console.error("❌ Error creating super admin:", error);
      logError("Failed to create super admin", "post", {
        error: error?.message || "Unknown error",
        stack: error?.stack,
      });

      res.status(500).json({
        success: false,
        message: "Failed to create super admin",
        details: {
          error: error?.message || "Unknown error",
        },
      });
    }
  }
);

// Apply admin authentication to all routes AFTER bootstrap
adminRouter.use(requireAdmin());

// Apply request logging to all admin routes
adminRouter.use(logAdminRequest);

// ============================================================================
// DASHBOARD & ANALYTICS ROUTES
// ============================================================================

adminRouter.get(
  "/dashboard",
  logAdminActivity("VIEW_DASHBOARD", "dashboard"),
  getDashboardStats
);

adminRouter.get(
  "/analytics",
  logAdminActivity("VIEW_ANALYTICS", "analytics"),
  getAnalytics
);

// ============================================================================
// TUTOR MANAGEMENT ROUTES
// ============================================================================

// Get tutors
adminRouter.get(
  "/tutors",
  logAdminActivity("VIEW_ALL_TUTORS", "tutors"),
  getAllTutors
);

adminRouter.get(
  "/tutors/pending",
  logAdminActivity("VIEW_PENDING_TUTORS", "tutors"),
  getPendingTutors
);

adminRouter.get(
  "/tutors/:tutorId",
  logAdminActivity("VIEW_TUTOR_DETAILS", "tutor"),
  getTutorById
);

// Tutor approval
adminRouter.patch(
  "/tutors/:tutorId/approve",
  logAdminActivity("APPROVE_TUTOR", "tutor"),
  approveTutor
);

adminRouter.patch(
  "/tutors/:tutorId/reject",
  withRequestBody(),
  logAdminActivity("REJECT_TUTOR", "tutor"),
  rejectTutor
);

// Tutor moderation
adminRouter.patch(
  "/tutors/:tutorId/flag",
  // withRequestBody(),
  logAdminActivity("FLAG_TUTOR", "tutor"),
  flagTutor
);

adminRouter.patch(
  "/tutors/:tutorId/unflag",
  // withRequestBody(),
  logAdminActivity("UNFLAG_TUTOR", "tutor"),
  unflagTutor
);

adminRouter.patch(
  "/tutors/:tutorId/suspend",
  withRequestBody(),
  logAdminActivity("SUSPEND_TUTOR", "tutor"),
  suspendTutor
);

adminRouter.delete(
  "/tutors/:tutorId",
  withRequestBody(),
  logAdminActivity("DELETE_TUTOR", "tutor"),
  deleteTutor
);

// ============================================================================
// STUDENT MANAGEMENT ROUTES
// ============================================================================

adminRouter.get(
  "/students",
  logAdminActivity("VIEW_ALL_STUDENTS", "students"),
  getAllStudents
);

adminRouter.get(
  "/students/:studentId",
  logAdminActivity("VIEW_STUDENT_DETAILS", "student"),
  getStudentById
);

adminRouter.patch(
  "/students/:studentId/suspend",
  withRequestBody(),
  logAdminActivity("SUSPEND_STUDENT", "student"),
  suspendStudent
);

adminRouter.delete(
  "/students/:studentId",
  withRequestBody(),
  logAdminActivity("DELETE_STUDENT", "student"),
  deleteStudent
);

// ============================================================================
// SUBSCRIPTION MANAGEMENT ROUTES
// ============================================================================

adminRouter.get(
  "/subscriptions",
  logAdminActivity("VIEW_ALL_SUBSCRIPTIONS", "subscriptions"),
  getAllSubscriptions
);

adminRouter.get(
  "/subscriptions/:subscriptionId",
  logAdminActivity("VIEW_SUBSCRIPTION_DETAILS", "subscription"),
  getSubscriptionById
);

adminRouter.patch(
  "/subscriptions/:subscriptionId/cancel",
  // withRequestBody(),
  logAdminActivity("CANCEL_SUBSCRIPTION", "subscription"),
  cancelSubscription
);

// ============================================================================
// CONTENT MODERATION ROUTES
// ============================================================================

adminRouter.get(
  "/flagged",
  logAdminActivity("VIEW_FLAGGED_CONTENT", "flagged_content"),
  getFlaggedItems
);

adminRouter.post(
  "/moderate",
  withRequestBody(),
  logAdminActivity("MODERATE_CONTENT", "content"),
  moderateContent
);

// ============================================================================
// ADMIN MANAGEMENT ROUTES
// ============================================================================

adminRouter.get(
  "/admins",
  logAdminActivity("VIEW_ALL_ADMINS", "admins"),
  getAllAdmins
);

adminRouter.post(
  "/admins",
  withRequestBody(),
  logAdminActivity("CREATE_ADMIN", "admin"),
  createAdmin
);

adminRouter.patch(
  "/admins/:adminId/permissions",
  withRequestBody(),
  logAdminActivity("UPDATE_ADMIN_PERMISSIONS", "admin"),
  updateAdminPermissions
);

// ============================================================================
// FINANCIAL MANAGEMENT ROUTES
// ============================================================================

// Financial Overview & Analytics
adminRouter.get(
  "/financial/overview",
  logAdminActivity("VIEW_FINANCIAL_OVERVIEW", "financial"),
  getFinancialOverview
);

adminRouter.get(
  "/financial/reports",
  logAdminActivity("VIEW_FINANCIAL_REPORTS", "financial"),
  getFinancialReports
);

// Transaction Management
adminRouter.get(
  "/financial/transactions",
  logAdminActivity("VIEW_ALL_TRANSACTIONS", "transactions"),
  getAllTransactions
);

adminRouter.get(
  "/financial/transactions/:transactionId",
  logAdminActivity("VIEW_TRANSACTION_DETAILS", "transaction"),
  getTransactionById
);

// Escrow Management
adminRouter.get(
  "/financial/escrow",
  logAdminActivity("VIEW_ESCROW_TRANSACTIONS", "escrow"),
  getAllEscrowTransactions
);

adminRouter.patch(
  "/financial/escrow/:escrowId/release",
  withRequestBody(),
  logAdminActivity("RELEASE_ESCROW_FUNDS", "escrow"),
  releaseEscrowFunds
);

// Refund Management
adminRouter.post(
  "/financial/refunds/:subscriptionId",
  withRequestBody(),
  logAdminActivity("PROCESS_REFUND", "refund"),
  processRefund
);

// Payment Analytics
adminRouter.get(
  "/financial/payment-analytics",
  logAdminActivity("VIEW_PAYMENT_ANALYTICS", "financial"),
  getPaymentMethodAnalytics
);

// Tutor Payout Management
adminRouter.get(
  "/financial/tutor-payouts",
  logAdminActivity("VIEW_TUTOR_PAYOUTS", "payouts"),
  getTutorPayoutSummary
);



// ============================================================================
// ADDITIONAL ADMIN ROUTES
// ============================================================================

// System health and logs
adminRouter.get(
  "/system/health",
  logAdminActivity("VIEW_SYSTEM_HEALTH", "system"),
  (_req: AuthRequest, res: Response): void => {
    try {
      const healthData = {
        status: "healthy",
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.version,
        environment: process.env.NODE_ENV || "development",
      };

      res.json({
        success: true,
        data: healthData,
      });
    } catch (error: any) {
      console.error("Error fetching system health:", error);
      logError("Failed to fetch system health", "get", {
        error: error?.message || "Unknown error",
      });
      res.status(500).json({
        success: false,
        message: "Failed to fetch system health",
      });
    }
  }
);

// Admin activity logs
adminRouter.get(
  "/logs/activity",
  logAdminActivity("VIEW_ACTIVITY_LOGS", "logs"),
  (_req: AuthRequest, res: Response): void => {
    try {
      // This would typically read from a proper logging service
      // For now, return a placeholder response
      res.json({
        success: true,
        message:
          "Activity logs endpoint - implement with proper logging service",
        data: {
          note: "Logs are currently written to src/logs/error-log.json",
          suggestion: "Implement proper log reading functionality",
        },
      });
    } catch (error: any) {
      console.error("Error fetching activity logs:", error);
      logError("Failed to fetch activity logs", "get", {
        error: error?.message || "Unknown error",
      });
      res.status(500).json({
        success: false,
        message: "Failed to fetch activity logs",
      });
    }
  }
);

// Bulk operations
adminRouter.post(
  "/bulk/approve-tutors",
  withRequestBody(),
  logAdminActivity("BULK_APPROVE_TUTORS", "tutors"),
  (req: AuthRequest, res: Response): void => {
    try {
      const { tutorIds } = req.body;

      if (!tutorIds || !Array.isArray(tutorIds) || tutorIds.length === 0) {
        res.status(400).json({
          success: false,
          message: "tutorIds array is required",
        });
        return;
      }

      // This would implement bulk approval logic
      res.json({
        success: true,
        message: `Bulk approval initiated for ${tutorIds.length} tutors`,
        data: {
          tutorIds,
          note: "Implement bulk approval logic in controller",
        },
      });
    } catch (error: any) {
      console.error("Error in bulk tutor approval:", error);
      logError("Failed bulk tutor approval", "post", {
        error: error?.message || "Unknown error",
      });
      res.status(500).json({
        success: false,
        message: "Failed to process bulk approval",
      });
    }
  }
);

// ============================================================================
// WITHDRAWAL MANAGEMENT ROUTES
// ============================================================================

// Get all withdrawal requests
adminRouter.get(
  "/withdrawals",
  logAdminActivity("VIEW_ALL_WITHDRAWAL_REQUESTS", "withdrawals"),
  getAllWithdrawalRequests
);

// Get withdrawal statistics
adminRouter.get(
  "/withdrawals/statistics",
  logAdminActivity("VIEW_WITHDRAWAL_STATISTICS", "withdrawals"),
  getWithdrawalStatistics
);

// Enhanced payout status management
adminRouter.patch(
  "/withdrawals/:requestId/status",
  withRequestBody(),
  logAdminActivity("UPDATE_PAYOUT_STATUS", "withdrawal"),
  updatePayoutStatus
);

adminRouter.post(
  "/withdrawals/bulk-status-update",
  withRequestBody(),
  logAdminActivity("BULK_UPDATE_PAYOUT_STATUS", "withdrawal"),
  bulkUpdatePayoutStatus
);

adminRouter.get(
  "/withdrawals/payout-analytics",
  logAdminActivity("VIEW_PAYOUT_ANALYTICS", "withdrawal"),
  getPayoutStatusAnalytics
);

// Get specific withdrawal request
adminRouter.get(
  "/withdrawals/:requestId",
  logAdminActivity("VIEW_WITHDRAWAL_REQUEST", "withdrawal"),
  getWithdrawalRequestById
);

// Approve withdrawal request
adminRouter.patch(
  "/withdrawals/:requestId/approve",
  withRequestBody(),
  logAdminActivity("APPROVE_WITHDRAWAL_REQUEST", "withdrawal"),
  approveWithdrawalRequest
);

// Reject withdrawal request
adminRouter.patch(
  "/withdrawals/:requestId/reject",
  withRequestBody(),
  logAdminActivity("REJECT_WITHDRAWAL_REQUEST", "withdrawal"),
  rejectWithdrawalRequest
);

// Process withdrawal request (mark as completed)
adminRouter.patch(
  "/withdrawals/:requestId/process",
  withRequestBody(),
  logAdminActivity("PROCESS_WITHDRAWAL_REQUEST", "withdrawal"),
  processWithdrawalRequest
);

// ============================================================================
// REFUND REQUEST MANAGEMENT
// ============================================================================

// Get all refund requests
adminRouter.get(
  "/refunds",
  logAdminActivity("VIEW_ALL_REFUND_REQUESTS", "refunds"),
  getAllRefundRequests
);

// Get refund statistics
adminRouter.get(
  "/refunds/statistics",
  logAdminActivity("VIEW_REFUND_STATISTICS", "refunds"),
  getRefundStatistics
);

// Get specific refund request
adminRouter.get(
  "/refunds/:requestId",
  logAdminActivity("VIEW_REFUND_REQUEST", "refund"),
  getRefundRequestByIdAdmin
);

// Approve refund request
adminRouter.patch(
  "/refunds/:requestId/approve",
  withRequestBody(),
  logAdminActivity("APPROVE_REFUND_REQUEST", "refund"),
  approveRefundRequest
);

// Reject refund request
adminRouter.patch(
  "/refunds/:requestId/reject",
  withRequestBody(),
  logAdminActivity("REJECT_REFUND_REQUEST", "refund"),
  rejectRefundRequest
);

// Process refund request (mark as completed)
adminRouter.patch(
  "/refunds/:requestId/process",
  withRequestBody(),
  logAdminActivity("PROCESS_REFUND_REQUEST", "refund"),
  processRefundRequest
);

// ============================================================================
// TRANSACTION MANAGEMENT
// ============================================================================

// Get all transactions
adminRouter.get(
  "/transactions",
  logAdminActivity("VIEW_ALL_TRANSACTIONS", "transactions"),
  getAllTransactionsAdmin
);

// Get transaction analytics
adminRouter.get(
  "/transactions/analytics",
  logAdminActivity("VIEW_TRANSACTION_ANALYTICS", "transactions"),
  getTransactionAnalytics
);

// Export transactions
adminRouter.get(
  "/transactions/export",
  logAdminActivity("EXPORT_TRANSACTIONS", "transactions"),
  exportTransactions
);

// Create manual transaction
adminRouter.post(
  "/transactions/manual",
  withRequestBody(),
  logAdminActivity("CREATE_MANUAL_TRANSACTION", "transaction"),
  createManualTransactionAdmin
);

// Bulk update transactions
adminRouter.patch(
  "/transactions/bulk-update",
  withRequestBody(),
  logAdminActivity("BULK_UPDATE_TRANSACTIONS", "transaction"),
  bulkUpdateTransactions
);

// Get transactions by user
adminRouter.get(
  "/transactions/user/:userId",
  logAdminActivity("VIEW_USER_TRANSACTIONS", "transaction"),
  getTransactionsByUser
);

// Get specific transaction
adminRouter.get(
  "/transactions/:transactionId",
  logAdminActivity("VIEW_TRANSACTION", "transaction"),
  getTransactionByIdAdmin
);

// Update transaction status
adminRouter.patch(
  "/transactions/:transactionId/status",
  withRequestBody(),
  logAdminActivity("UPDATE_TRANSACTION_STATUS", "transaction"),
  updateTransactionStatus
);

// ============================================================================
// SESSION COMPLETION MANAGEMENT
// ============================================================================

// Import session completion routes
import sessionCompletionRoutes from "./sessionCompletionRoutes";

// Mount session completion routes
adminRouter.use("/sessions", sessionCompletionRoutes);

// ============================================================================
// LEGACY ROUTES (for backward compatibility)
// ============================================================================

adminRouter.patch(
  "/approve-tutor/:tutorId",
  logAdminActivity("APPROVE_TUTOR_LEGACY", "tutor"),
  approveTutor
);

export default adminRouter;
