import { Router } from 'express';
import {
  createBooking,
  getBookingsForTutor,
  updateBookingStatus,
  createEnhancedBooking,
  getStudentBookings,
  getTutorBookings,
  getUpcomingBookings,
  getTutorAvailability,
  rescheduleBooking,
  cancelBooking,
  getTutorBookedSlots
} from '../controllers/bookingController';
import { isAuthenticated } from '../middlewares/auth';
import { withRequestBody } from '../middlewares/misc';

const router = Router();

// Legacy routes (keeping for backward compatibility)
router.post('/', createBooking);
router.get('/tutor', getBookingsForTutor);
router.put('/:id/status', updateBookingStatus);

// Enhanced booking route - creates events in both tutor and student calendars
router.post('/enhanced', withRequestBody(), isAuthenticated(), createEnhancedBooking);

// New detailed booking routes with time and date information
// Get student bookings (student can get their own, admin can get any student's)
router.get('/student', isAuthenticated(), getStudentBookings);
router.get('/student/:studentId', isAuthenticated(), getStudentBookings);

// Get tutor bookings (tutor can get their own, admin can get any tutor's)
router.get('/tutor-detailed', isAuthenticated(), getTutorBookings);
router.get('/tutor-detailed/:tutorId', isAuthenticated(), getTutorBookings);

// Get upcoming bookings for authenticated user
router.get('/upcoming', isAuthenticated(), getUpcomingBookings);

// Get tutor availability for students to view available time slots
router.get('/tutor-availability/:tutorId', isAuthenticated(), getTutorAvailability);

// Get tutor's booked time slots (for students to see when tutors are unavailable)
router.get('/tutor-booked-slots/:tutorId', isAuthenticated(), getTutorBookedSlots);

// Reschedule a booking/lesson
router.put('/reschedule/:lessonId', withRequestBody(), isAuthenticated(), rescheduleBooking);

// Cancel a booking/lesson
router.put('/cancel/:lessonId', withRequestBody(), isAuthenticated(), cancelBooking);

export default router;
