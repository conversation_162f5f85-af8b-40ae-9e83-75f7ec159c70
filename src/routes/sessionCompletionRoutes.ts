import { Router } from 'express';
import {
  processAllCompletedSessions,
  processSpecificSessionEndpoint,
  getSessionCompletionStats,
  sessionProcessingHealthCheck
} from '../controllers/sessionCompletionController';
import { isAuthenticated } from '../middlewares/auth';
import { withRequestBody } from '../middlewares/misc';

const router = Router();

// Apply authentication middleware to all routes
router.use(isAuthenticated());

// Admin routes for session completion processing
router.post('/process-all', processAllCompletedSessions);
router.post('/process/:eventId', processSpecificSessionEndpoint);
router.get('/stats', getSessionCompletionStats);
router.get('/health', sessionProcessingHealthCheck);

export default router;
