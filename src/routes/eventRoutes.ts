import { Router } from 'express';
import { createEvent, getEventsByCalendar, updateEvent, deleteEvent } from '../controllers/eventController';
import { isAuthenticated } from '../middlewares/auth';
import { withRequestBody } from '../middlewares/misc';

const router = Router();

// Protected routes
router.post('/', withRequestBody(), isAuthenticated(), createEvent);
router.get('/calendar/:calendarId', getEventsByCalendar);
router.put('/:id', withRequestBody(), isAuthenticated(), updateEvent);
router.delete('/:id', isAuthenticated(), deleteEvent);

export default router;
