import { Router } from "express";
import tutorRouter from "./tutor";
import studentRouter from "./student";
import { withRequestBody } from "../middlewares/misc";
import { isAuthenticated } from "../middlewares/auth";
import { onBoardUser, updatePassword } from "../hooks/profile";
import adminRouter from "./adminRouter";

const profileRouter = Router();

profileRouter
  .route("/onboard")
  .put(withRequestBody(), isAuthenticated(), onBoardUser);

profileRouter
  .use("/students", studentRouter)
  .use("/tutors", tutorRouter)
  .use("/admin", adminRouter)
  .put(
    "/:userId",
    withRequestBody(),
    isAuthenticated({
      withUserId: true,
      strictVerification: true,
    }),
    updatePassword
  );

export default profileRouter;
