import { Router } from "express";
import { updateStudentProfile } from "../controllers/student";
import { deleteProfileHook, getProfileById } from "../hooks/profile";
import Student from "../models/student";
import { AuthOptions, isAuthenticated } from "../middlewares/auth";
import { withRequestBody } from "../middlewares/misc";

const studentRouter = Router();

const strictConfig: AuthOptions = {
  role: "student",
  withUserId: true,
  strictVerification: true,
};

studentRouter
  .route("/:id")
  .get(getProfileById(Student))
  .put(withRequestBody(), isAuthenticated(strictConfig), updateStudentProfile)
  .delete(isAuthenticated(strictConfig), deleteProfileHook);

export default studentRouter;
