import { Router } from 'express';
import { 
  createRecurringSchedule, 
  getUserSchedule, 
  getTutorAvailability, 
  bookSession 
} from '../controllers/scheduleController';
import { isAuthenticated } from '../middlewares/auth';
import { withRequestBody } from '../middlewares/misc';

const scheduleRouter = Router();

// Protected routes - require authentication
scheduleRouter.post(
  '/recurring', 
  withRequestBody(), 
  isAuthenticated(), 
  createRecurringSchedule
);

scheduleRouter.get(
  '/my-schedule', 
  isAuthenticated(), 
  getUserSchedule
);

scheduleRouter.get(
  '/tutor/:tutorId/availability', 
  getTutorAvailability
);

scheduleRouter.post(
  '/book-session', 
  withRequestBody(), 
  isAuthenticated({ role: 'student' }), 
  bookSession
);

export default scheduleRouter;
