import { Router } from "express";
import {
  getConversationMessages,
  getUserConversations,
} from "../controllers/chat";
import { isAuthenticated } from "../middlewares/auth";
import {
  validateGetUserConversations,
  validateGetConversationMessages,
} from "../middlewares/chatValidation";
import {
  chatRateLimit,
  verifyConversationAccess,
  verifyUserProfileAccess,
  sanitizeChatResponse,
  logChatOperation,
} from "../middlewares/chatSecurity";

const chatRouter = Router();

const authConfig = {
  withUserId: true,
  skipProfileFetch: false, // We need full profile for authorization
  strictVerification: true,
};

/**
 * @route GET /api/chat/user-conversations/:userId
 * @desc Get conversations for a specific user with pagination
 * @access Private (User can only access their own conversations, admins can access any)
 * @param {string} userId - User ID to get conversations for
 * @query {number} limit - Number of conversations to return (1-100, default: 20)
 * @query {string} sortOrder - Sort order: 'asc' or 'desc' (default: 'desc')
 * @query {string} cursor - Cursor for pagination (ObjectId)
 * @security Rate limited, input validation, authorization checks, response sanitization
 */
chatRouter.get(
  "/user-conversations/:userId",
  chatRateLimit(50, 60000), // 50 requests per minute
  logChatOperation("GET_USER_CONVERSATIONS"),
  isAuthenticated(authConfig),
  ...validateGetUserConversations,
  verifyUserProfileAccess,
  sanitizeChatResponse,
  getUserConversations
);

/**
 * @route GET /api/chat/conversation-messages/:conversationId
 * @desc Get messages for a specific conversation with pagination
 * @access Private (User must be a participant in the conversation)
 * @param {string} conversationId - Conversation ID to get messages for
 * @query {number} limit - Number of messages to return (1-100, default: 20)
 * @query {string} sortOrder - Sort order: 'asc' or 'desc' (default: 'asc')
 * @query {string} cursor - Cursor for pagination (ObjectId)
 * @security Rate limited, input validation, conversation access verification, response sanitization
 */
chatRouter.get(
  "/conversation-messages/:conversationId",
  chatRateLimit(100, 60000), // 100 requests per minute (higher limit for messages)
  logChatOperation("GET_CONVERSATION_MESSAGES"),
  isAuthenticated(authConfig),
  ...validateGetConversationMessages,
  verifyConversationAccess,
  sanitizeChatResponse,
  getConversationMessages
);

export default chatRouter;
