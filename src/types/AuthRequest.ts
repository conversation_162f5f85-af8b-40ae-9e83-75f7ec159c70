import { Request } from "express";
import { USER_PROFILE_TYPE } from "../utils/profile";

// Basic auth user from JWT token
export interface AuthUser {
  id: string; // User ID from JWT token
  role: "student" | "tutor" | "admin";
}

// Extended interface for authenticated requests
// After isAuthenticated middleware, req.user will be a full profile object
export interface AuthRequest extends Request {
  user?: USER_PROFILE_TYPE; // Full profile object (Student/Tutor/Admin)
}
