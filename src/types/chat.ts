/**
 * TypeScript type definitions for chat-related operations
 */

import { Document, Types } from "mongoose";

/**
 * Base pagination query parameters
 */
export interface PaginationQuery {
  /** Number of items to return (1-100) */
  limit?: string;
  /** Sort order: 'asc' or 'desc' */
  sortOrder?: "asc" | "desc";
  /** Cursor for pagination (ObjectId or timestamp) */
  cursor?: string;
}

/**
 * Chat message query parameters
 */
export interface MessageQueryParams extends PaginationQuery {
  /** Conversation ID */
  conversationId: string;
}

/**
 * User conversations query parameters
 */
export interface ConversationQueryParams extends PaginationQuery {
  /** User ID to get conversations for */
  userId: string;
}

/**
 * Pagination response details
 */
export interface PaginationDetails {
  pagination: {
    type: "cursor";
    hasMore: boolean;
    nextCursor?: string | null;
  };
}

/**
 * Enhanced message response with populated sender
 */
export interface MessageResponse {
  _id: string;
  conversation: string;
  sender: {
    _id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    role: string;
  };
  senderModel: string;
  recipients: Record<string, string>;
  files?: Array<{
    name: string;
    mimetype: string;
    size: number;
    extension: string;
    recipients: Record<string, {
      encryptedFile: string;
      encryptedKey: string;
      iv: string;
    }>;
  }>;
  deliveredAt?: Date;
  readAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Enhanced conversation response with populated participants and last message
 */
export interface ConversationResponse {
  _id: string;
  participants: Record<string, {
    _id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    role: string;
    isActive: boolean;
  }>;
  lastMessage?: {
    _id: string;
    recipients: Record<string, string>;
    sender: {
      _id: string;
      firstName: string;
      lastName: string;
      profilePicture?: string;
      role: string;
    };
    createdAt: Date;
    deliveredAt?: Date;
    readAt?: Date;
  };
  unreadCount: number;
  lastActivity: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * API response for conversation messages
 */
export interface GetConversationMessagesResponse {
  data: MessageResponse[];
  details: PaginationDetails & {
    conversationId: string;
    participantCount: number;
    requestedBy: string;
    timestamp: string;
  };
}

/**
 * API response for user conversations
 */
export interface GetUserConversationsResponse {
  data: ConversationResponse[];
  details: PaginationDetails & {
    userId: string;
    totalConversations: number;
    requestedBy: string;
    isOwnProfile: boolean;
    timestamp: string;
  };
}

/**
 * Error response structure
 */
export interface ChatErrorResponse {
  success: false;
  status: number;
  code: string;
  message: string;
  details?: Record<string, any>;
  datetime: string;
}

/**
 * Success response structure
 */
export interface ChatSuccessResponse<T = any> {
  success: true;
  status: 200;
  code: string;
  message: string;
  data: T;
  details?: Record<string, any>;
  datetime: string;
}

/**
 * Request context for logging and monitoring
 */
export interface ChatRequestContext {
  operation: string;
  userId?: string;
  conversationId?: string;
  userRole?: string;
  ip?: string;
  userAgent?: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

/**
 * Performance metrics for chat operations
 */
export interface ChatPerformanceMetrics {
  operation: string;
  duration: number;
  itemCount?: number;
  hasMore?: boolean;
  cacheHit?: boolean;
  queryComplexity?: "simple" | "medium" | "complex";
}

/**
 * Chat operation audit log entry
 */
export interface ChatAuditLog {
  operation: string;
  userId: string;
  userRole: string;
  targetResource: string;
  targetId: string;
  action: "read" | "write" | "delete";
  success: boolean;
  ip: string;
  userAgent: string;
  timestamp: Date;
  duration: number;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Rate limiting configuration
 */
export interface ChatRateLimitConfig {
  maxRequests: number;
  windowMs: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

/**
 * Cache configuration for chat operations
 */
export interface ChatCacheOptions {
  key: string;
  ttl: number;
  tags?: string[];
  compress?: boolean;
}

/**
 * Database query optimization hints
 */
export interface QueryOptimizationHints {
  useIndex?: string;
  maxTimeMS?: number;
  readPreference?: "primary" | "secondary" | "primaryPreferred" | "secondaryPreferred";
  readConcern?: "local" | "available" | "majority" | "linearizable" | "snapshot";
}

/**
 * Extended Express Request interface for chat operations
 */
export interface ChatRequest extends Request {
  user: {
    id: string;
    role: "student" | "tutor" | "admin";
    firstName?: string;
    lastName?: string;
    email?: string;
    isActive?: boolean;
  };
  conversation?: {
    _id: string;
    participants: Array<{
      userModel: string;
      user: string;
    }>;
  };
  chatContext?: ChatRequestContext;
  performanceStart?: number;
}

/**
 * Validation schema for chat endpoints
 */
export interface ChatValidationSchema {
  params?: Record<string, {
    type: "string" | "number" | "boolean";
    required?: boolean;
    pattern?: RegExp;
    min?: number;
    max?: number;
  }>;
  query?: Record<string, {
    type: "string" | "number" | "boolean";
    required?: boolean;
    pattern?: RegExp;
    min?: number;
    max?: number;
    enum?: string[];
  }>;
  body?: Record<string, {
    type: "string" | "number" | "boolean" | "object" | "array";
    required?: boolean;
    pattern?: RegExp;
    min?: number;
    max?: number;
  }>;
}

/**
 * Chat feature flags for A/B testing and gradual rollouts
 */
export interface ChatFeatureFlags {
  enableOptimizedQueries: boolean;
  enableMessageCaching: boolean;
  enableRealTimeUpdates: boolean;
  enableMessageEncryption: boolean;
  enableFileSharing: boolean;
  enableMessageReactions: boolean;
  enableTypingIndicators: boolean;
  enableReadReceipts: boolean;
  maxFileSize: number;
  allowedFileTypes: string[];
}

/**
 * Chat analytics event
 */
export interface ChatAnalyticsEvent {
  event: string;
  userId: string;
  conversationId?: string;
  messageId?: string;
  properties: Record<string, any>;
  timestamp: Date;
}
