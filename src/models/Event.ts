import { Schema, model, Document, Types } from 'mongoose';

export interface IEvent extends Document {
        calendarId: Types.ObjectId;
        title: string;
        description?: string;
        location?: string;
        startDateTime: Date;
        endDateTime: Date;
        allDay?: boolean;
        // createdBy: Types.ObjectId; // tutor's userId
        status?: 'confirmed' | 'cancelled' | 'tentative';
        visibility?: 'public' | 'private' | 'confidential';
        priority?: number;
        processed?: boolean; // Track if session completion has been processed
        createdAt: Date;
        updatedAt: Date;
}

const eventSchema = new Schema<IEvent>({
    calendarId: { type: Schema.Types.ObjectId, ref: 'Calendar', required: true },
    title: { type: String, required: true },
    description: String,
    location: String,
    startDateTime: { type: Date, required: true },
    endDateTime: { type: Date, required: true },
    allDay: { type: Boolean, default: false },
    // createdBy: { type: Schema.Types.ObjectId, ref: 'Student', required: true },
    status: { type: String, enum: ['confirmed', 'cancelled', 'tentative'], default: 'confirmed' },
    visibility: { type: String, enum: ['public', 'private', 'confidential'], default: 'public' },
    priority: { type: Number, default: 3 },
    processed: { type: Boolean, default: false },
    createdAt: { type: Date, default: () => new Date() },
    updatedAt: { type: Date, default: () => new Date() }
});

export const Event = model<IEvent>('Event', eventSchema);
