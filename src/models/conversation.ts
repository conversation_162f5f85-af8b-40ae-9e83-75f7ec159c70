import mongoose, { Schema, Document, Types } from "mongoose";
import { DEFAULT_SCHEMA_OPTS } from "./utils";
import { PROFILE_REGISTRATION_MODELS, ProfileModelType } from "./profile";

export interface IConversation extends Document {
  participants: {
    userModel: ProfileModelType;
    user: Types.ObjectId;
  }[];
  lastMessage?: Types.ObjectId;
}

const ConversationSchema = new Schema<IConversation>(
  {
    participants: [
      {
        userModel: {
          type: String,
          enum: PROFILE_REGISTRATION_MODELS,
          required: true,
        },
        user: {
          type: Schema.Types.ObjectId,
          required: true,
          refPath: "participants.userModel",
        },
      },
    ],
    lastMessage: { type: Schema.Types.ObjectId, ref: "Message" },
  },
  DEFAULT_SCHEMA_OPTS
);

const Conversation = mongoose.model<IConversation>(
  "Conversation",
  ConversationSchema
);

ConversationSchema.index({ "participants.user": 1, updatedAt: -1 });

export default Conversation;
