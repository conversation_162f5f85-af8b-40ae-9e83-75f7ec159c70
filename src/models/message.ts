import mongoose, { Schema, Document } from "mongoose";
import { DEFAULT_SCHEMA_OPTS } from "./utils";
import { KeyValuePair } from "../types/misc";
import { IConversation } from "./conversation";
import { PROFILE_REGISTRATION_MODELS, ProfileModelType } from "./profile";

export interface IMessage extends Document {
  conversation: mongoose.Types.ObjectId | IConversation;
  senderModel: ProfileModelType;
  sender: mongoose.Types.ObjectId;
  recipients?: KeyValuePair;
  //   mediaUrl?: string;
  //   mediaType?: "image" | "video" | "audio" | "document";
  deliveredAt: Date;
  readAt: Date;
  createdAt: Date;
  updatedAt: Date;
  files?: {
    recipients: KeyValuePair;
    name: string;
    mimetype: string;
    extension: string;
    size: number;
  }[];
}

const MessageSchema = new Schema<IMessage>(
  {
    conversation: {
      type: Schema.Types.ObjectId,
      ref: "Conversation",
      required: true,
    },
    senderModel: {
      type: String,
      enum: PROFILE_REGISTRATION_MODELS,
      required: true,
    },
    sender: {
      type: Schema.Types.ObjectId,
      required: true,
      refPath: "senderModel",
    },
    recipients: {
      type: Map,
      of: String,
      required: true,
    },
    files: [
      {
        name: { type: String, required: [true, "file name is required"] },
        mimetype: {
          type: String,
          required: [true, "file mimetype is required"],
        },
        size: { type: Number, required: [true, "file size is required"] },
        extention: {
          type: String,
          required: [true, "File extension is required"],
        },
        recipients: {
          type: Map,
          of: {
            encryptedFile: {
              type: String,
              required: [true, "encrypted file is required"],
            },
            encryptedKey: {
              type: String,
              required: [true, "encryption key is required"],
            },
            iv: { type: String, required: [true, "encryption iv is required"] },
          },
          required: true,
        },
      },
    ],
    deliveredAt: { type: Date },
    readAt: { type: Date },
  },
  DEFAULT_SCHEMA_OPTS
);

const Message = mongoose.model<IMessage>("Message", MessageSchema);

MessageSchema.index({ createdAt: 1 });

export default Message;
