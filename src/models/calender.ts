import { Schema, model, Document, Types } from 'mongoose';

export interface ICalendar extends Document {
  ownerUserId: Types.ObjectId; // Can be tutor or student
  ownerType: 'Tutor' | 'Student'; // Discriminator for the owner type
  name: string;
  description?: string;
  color?: string;
  isShared?: boolean; // whether this calendar is visible to learners or public
  createdAt: Date;
  updatedAt: Date;
}

const calendarSchema = new Schema<ICalendar>({
  ownerUserId: { type: Schema.Types.ObjectId, required: true, refPath: 'ownerType' },
  ownerType: { type: String, enum: ['Tutor', 'Student'], required: true },
  name: { type: String, required: true },
  description: String,
  color: { type: String, default: '#0000FF' },
  isShared: { type: Boolean, default: true }, // Tutors want learners to see their calendar by default
  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

export const Calendar = model<ICalendar>('Calendar', calendarSchema);
