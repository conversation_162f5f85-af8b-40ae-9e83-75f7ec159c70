import mongoose, { Schema, Document } from "mongoose";
import { DEFAULT_SCHEMA_OPTS } from "./utils";
import { IProfile, PROFILE_ROLES } from "./profile";

export const COMPLAINT_PROIVDERS = ["zendesk"];

export const DEFAULT_COMPLAINT_PROVIDER = "zendesk";

export interface IComplaintTicket extends Document {
  subject: string;
  description: string;
  requester: {
    email: IProfile["email"];
    role: IProfile["role"];
  };
  ticketId: number;
  createdAt: Date;
  updatedAt: Date;
  provider: "zendesk";
  status: "open" | "closed" | "pending";
  arbitrator: {
    email: IProfile["email"];
    role: "admin";
  };
}

const ComplaintTicketSchema = new Schema<IComplaintTicket>(
  {
    subject: { type: String, required: true },
    description: { type: String, required: true },
    requester: {
      type: new Schema<IComplaintTicket["requester"]>(
        {
          email: { type: String, required: true },
          role: { type: String, enum: PROFILE_ROLES, required: true },
        },
        { _id: false }
      ),
      required: true,
    },
    arbitrator: {
      type: new Schema<IComplaintTicket["arbitrator"]>(
        {
          email: { type: String, required: true },
          role: { type: String, enum: ["admin"], required: true },
        },
        { _id: false }
      ),
      required: true,
    },
    ticketId: { type: Number, required: true },
    provider: {
      type: String,
      enum: COMPLAINT_PROIVDERS,
      default: DEFAULT_COMPLAINT_PROVIDER,
    },
    status: {
      type: String,
      enum: ["open", "closed", "pending"],
      required: true,
    },
  },
  DEFAULT_SCHEMA_OPTS
);

const ComplaintTicket = mongoose.model<IComplaintTicket>(
  "ComplaintTicket",
  ComplaintTicketSchema
);

export default ComplaintTicket;
