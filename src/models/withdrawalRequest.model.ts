import mongoose, { Document, Schema } from 'mongoose';

export interface IWithdrawalRequest extends Document {
  tutorId: mongoose.Types.ObjectId;
  amount: number; // in cents
  requestedAmount: number; // original requested amount in dollars
  bankDetails: {
    accountHolderName: string;
    bankName: string;
    accountNumber: string;
    routingNumber?: string;
    iban?: string;
    swiftCode?: string;
    accountType: 'checking' | 'savings';
  };
  status: 'pending' | 'approved' | 'rejected' | 'processed' | 'cancelled';
  requestReason?: string;
  adminNotes?: string;
  rejectionReason?: string;
  processedBy?: mongoose.Types.ObjectId; // Admin who processed the request
  processedAt?: Date;
  stripeTransferId?: string;
  transactionId?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  canBeCancelled(): boolean;
  canBeApproved(): boolean;
  canBeRejected(): boolean;
  markAsProcessed(adminId: mongoose.Types.ObjectId, transactionId?: mongoose.Types.ObjectId): Promise<IWithdrawalRequest>;
}

const withdrawalRequestSchema = new Schema<IWithdrawalRequest>({
  tutorId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Tutor', 
    required: true 
  },
  amount: { 
    type: Number, 
    required: true,
    min: 0
  },
  requestedAmount: {
    type: Number,
    required: true,
    min: 0
  },
  bankDetails: {
    accountHolderName: { 
      type: String, 
      required: true,
      trim: true
    },
    bankName: { 
      type: String, 
      required: true,
      trim: true
    },
    accountNumber: { 
      type: String, 
      required: true,
      trim: true
    },
    routingNumber: { 
      type: String,
      trim: true
    },
    iban: { 
      type: String,
      trim: true
    },
    swiftCode: { 
      type: String,
      trim: true
    },
    accountType: {
      type: String,
      enum: ['checking', 'savings'],
      required: true
    }
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'processed', 'cancelled'],
    default: 'pending'
  },
  requestReason: {
    type: String,
    trim: true
  },
  adminNotes: {
    type: String,
    trim: true
  },
  rejectionReason: {
    type: String,
    trim: true
  },
  processedBy: {
    type: Schema.Types.ObjectId,
    ref: 'Admin'
  },
  processedAt: {
    type: Date
  },
  stripeTransferId: {
    type: String
  },
  transactionId: {
    type: Schema.Types.ObjectId,
    ref: 'Transaction'
  }
}, {
  timestamps: true
});

// Indexes for better query performance
withdrawalRequestSchema.index({ tutorId: 1, status: 1 });
withdrawalRequestSchema.index({ status: 1, createdAt: -1 });
withdrawalRequestSchema.index({ processedBy: 1 });

// Virtual for tutor details
withdrawalRequestSchema.virtual('tutor', {
  ref: 'Tutor',
  localField: 'tutorId',
  foreignField: '_id',
  justOne: true
});

// Virtual for admin details
withdrawalRequestSchema.virtual('admin', {
  ref: 'Admin',
  localField: 'processedBy',
  foreignField: '_id',
  justOne: true
});

// Virtual for transaction details
withdrawalRequestSchema.virtual('transaction', {
  ref: 'Transaction',
  localField: 'transactionId',
  foreignField: '_id',
  justOne: true
});

// Instance methods
withdrawalRequestSchema.methods.canBeCancelled = function(): boolean {
  return this.status === 'pending';
};

withdrawalRequestSchema.methods.canBeApproved = function(): boolean {
  return this.status === 'pending';
};

withdrawalRequestSchema.methods.canBeRejected = function(): boolean {
  return this.status === 'pending';
};

withdrawalRequestSchema.methods.markAsProcessed = function(adminId: mongoose.Types.ObjectId, transactionId?: mongoose.Types.ObjectId) {
  this.status = 'processed';
  this.processedBy = adminId;
  this.processedAt = new Date();
  if (transactionId) {
    this.transactionId = transactionId;
  }
  return this.save();
};

// Static methods
withdrawalRequestSchema.statics.findPending = function() {
  return this.find({ status: 'pending' })
    .populate('tutorId', 'firstname lastname email availableBalance')
    .sort({ createdAt: -1 });
};

withdrawalRequestSchema.statics.findByTutor = function(tutorId: string) {
  return this.find({ tutorId })
    .populate('processedBy', 'firstname lastname')
    .sort({ createdAt: -1 });
};

withdrawalRequestSchema.statics.findByStatus = function(status: string) {
  return this.find({ status })
    .populate('tutorId', 'firstname lastname email')
    .populate('processedBy', 'firstname lastname')
    .sort({ createdAt: -1 });
};

// Pre-save middleware
withdrawalRequestSchema.pre('save', function(next) {
  // Convert requested amount to cents for storage
  if (this.isModified('requestedAmount')) {
    this.amount = Math.round(this.requestedAmount * 100);
  }
  
  // Set processedAt when status changes to processed, approved, or rejected
  if (this.isModified('status') && ['processed', 'approved', 'rejected'].includes(this.status)) {
    if (!this.processedAt) {
      this.processedAt = new Date();
    }
  }
  
  next();
});

// Serialize virtuals
withdrawalRequestSchema.set('toJSON', { virtuals: true });
withdrawalRequestSchema.set('toObject', { virtuals: true });

const WithdrawalRequest = mongoose.model<IWithdrawalRequest>('WithdrawalRequest', withdrawalRequestSchema);

export default WithdrawalRequest;
