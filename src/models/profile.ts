import mongoose, { Document, Schema } from "mongoose";
import { AUTH_PROVIDERS, DEFAULT_AUTH_PROVIDER } from "../config/constants";
import { LocationSchema } from "./location";
import { normalizeDatetime } from "../utils/datetime";
import { DEFAULT_SCHEMA_OPTS } from "./utils";

const transform = function (doc: any, ret: any) {
  ret = DEFAULT_SCHEMA_OPTS.toJSON.transform(doc, ret);

  delete ret.password;
  delete ret.resetCode;
  delete ret.resetCodeExpires;

  return ret;
};

export const PROFILE_SCHEMA_OPTS = {
  timestamps: true,
  toJSON: { transform, virtuals: true },
  toObject: { transform, virtuals: true },
};

export const SUB_SCHEMA_OPTS = {
  ...DEFAULT_SCHEMA_OPTS,
  timestamps: false,
};

export const PROFILE_REGISTRATION_ROLES = ["tutor", "student"];

export const PROFILE_REGISTRATION_MODELS = ["Tutor", "Student"];

export const PROFILE_ROLES = PROFILE_REGISTRATION_ROLES.concat(["admin"]);

export const DEFAULT_ONBOARDING_STATUS = "new";

type LanguageType = { name: string; level: string };

type ProfileRole = "student" | "tutor" | "admin";

export type ProfileModelType = "Student" | "Tutor";

export interface IProfileView {
  date: Date;
  viewerId?: mongoose.Types.ObjectId;
  role?: ProfileRole;
}

export interface IProfile extends Document {
  firstname: string;
  lastname: string;
  email: string;
  aboutMe?: string;
  role: ProfileRole;
  referredBy?: mongoose.Types.ObjectId;
  referrals?: mongoose.Types.ObjectId[];
  createdAt: Date;
  updatedAt: Date;
  password?: string;
  provider: string;
  encryptedData: {
    encryptedPrivateKey: string;
    publicKey: string;
    iv: string;
    salt: string;
  };
  timezone?: string;
  phoneno?: string;
  resetCode?: string;
  resetCodeExpires?: Date;
  isLoggedIn: boolean;
  lastLoggedAt?: Date;
  countryOfBirth: string;
  countryOfResidence: string;
  languages: LanguageType[];
  image?: string;
  onBoardingStatus: "new" | "ongoing" | "complete";
  nativeLanguage: LanguageType;
  username: string;
  location?: {
    country: string;
    state: string;
    lga: string;
    county: string;
    continent: string;
    datetime: string;
    address: string[];
  };
  timeAvailable: { label: string; from: String; to: String }[];
  daysAvailable: string[];
  profileViews: IProfileView[];
  fullname: string;
  settingsPreference: {
    withLessonUpdates: boolean;
    withLessonReminders: boolean;
    lessonReminderMS: number;
    withMessageUpdates: boolean;
  };
  reviewStats: {
    avgRating: number;
    totalRating: number;
    totalReview: number;
    ratingCount: {
      [key: string]: number;
    };
  };
}

export const ONBOARDING_STATUS = [
  DEFAULT_ONBOARDING_STATUS,
  "ongoing",
  "complete",
];

const LanguageSchema = new Schema(
  {
    name: {
      type: String,
      required: "Language name is required",
    },
    level: String,
    createdAt: {
      type: Date,
      default: normalizeDatetime(),
    },
    updatedAt: Date,
  },
  SUB_SCHEMA_OPTS
);

export const TimeAvailableSchema = new Schema(
  {
    label: {
      type: String,
      required: "Availability label required",
    },
    from: {
      type: String,
      // required: "Availability start time required",
    },
    to: {
      type: String,
      // required: "Availability end time required",
    },
    createdAt: {
      type: Date,
      default: normalizeDatetime(),
    },
    updatedAt: Date,
  },
  SUB_SCHEMA_OPTS
);

export const ProfileSchema = new Schema<IProfile>(
  {
    firstname: { type: String, required: [true, "Firstname is required."] },
    lastname: { type: String, required: [true, "Lastname is required."] },
    email: {
      type: String,
      unique: true,
      required: [true, "Email is required."],
      trim: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        "Please enter a valid email address.",
      ],
      lowercase: true,
    },
    role: {
      type: String,
      enum: PROFILE_ROLES,
      required: [true, "Profile role is required"],
    },
    aboutMe: {
      type: String,
      // required: [true, "About me is required"],
    },
    password: {
      type: String,
      required: [
        function () {
          return this.provider === DEFAULT_AUTH_PROVIDER || !this.provider;
        },
        "Password is required.",
      ],
      minlength: [8, "Password must be at least 8 characters long."],
    },
    timezone: {
      type: String,
      trim: true,
    },
    phoneno: {
      type: String,
      // required: [true, "Phone number is required"],
      trim: true,
    },
    countryOfBirth: {
      type: String,
      // required: [true, "Country of birth is required"],
    },
    countryOfResidence: {
      type: String,
      // required: [true, "Country of residence is required"],
    },
    nativeLanguage: {
      type: LanguageSchema,
      // required: [true, "Native language is required"],
    },
    location: {
      type: LocationSchema,
      // required: [true, "Location is required"],
    },
    timeAvailable: {
      type: [{ type: TimeAvailableSchema }],
      // required: [true, "Time availability is required"],
    },
    daysAvailable: {
      type: [{ type: String }],
      // required: [true, "Days availability is required"],
    },
    image: {
      type: String,
      // required: "Profile picture is required",
    },
    languages: [
      {
        type: LanguageSchema,
      },
    ],
    encryptedData: {
      type: new Schema<IProfile["encryptedData"]>(
        {
          encryptedPrivateKey: {
            type: String,
            required: [true, "Encrypted private key is required"],
          },
          publicKey: {
            type: String,
            required: [true, "Encrypted public key is required"],
          },
          iv: {
            type: String,
            required: [true, "Encryption iv is required"],
          },
          salt: {
            type: String,
            required: [true, "Encryption salt is required"],
          },
        },
        { _id: false }
      ),

      required: [true, "encryptedData is required"],
    },
    provider: {
      type: String,
      enum: AUTH_PROVIDERS,
      default: DEFAULT_AUTH_PROVIDER,
    },
    referredBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    referrals: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    ],
    isLoggedIn: {
      type: Boolean,
      set(this: any, value: boolean) {
        if (value) this.lastLoggedAt = new Date();
        return value;
      },
      default: false,
    },
    lastLoggedAt: Date,
    resetCode: String,
    resetCodeExpires: Date,
    onBoardingStatus: {
      type: String,
      enum: ONBOARDING_STATUS,
      default: DEFAULT_ONBOARDING_STATUS,
    },
    profileViews: [
      {
        date: { type: Date, required: [true, "profile view date is required"] },
        viewerId: { type: Schema.Types.ObjectId },
        role: {
          type: String,
          required: function (this: any) {
            return !!this.viewerId;
          },
          enum: PROFILE_ROLES,
        },
      },
    ],

    settingsPreference: {
      type: new Schema<IProfile["settingsPreference"]>(
        {
          lessonReminderMS: {
            type: Number,
            default: 300000,
          },
          withLessonUpdates: {
            type: Boolean,
            default: true,
          },
          withLessonReminders: {
            type: Boolean,
            default: false,
          },
          withMessageUpdates: {
            type: Boolean,
            default: true,
          },
        },
        { _id: false }
      ),
    },
    reviewStats: new Schema<IProfile["reviewStats"]>(
      {
        avgRating: {
          type: Number,
          default: 0,
        },
        totalRating: {
          type: Number,
          default: 0,
        },
        totalReview: {
          type: Number,
          default: 0,
        },
        ratingCount: {
          type: Object,
          // of: Number,
          // default: {},
        },
      },
      { _id: false }
    ),
  },
  PROFILE_SCHEMA_OPTS
);

ProfileSchema.virtual("fullname").get(function (this: IProfile) {
  return `${this.firstname} ${this.lastname}`;
});

ProfileSchema.virtual("username").get(function (this: IProfile) {
  return `${this.firstname}-${this.lastname}`;
});
