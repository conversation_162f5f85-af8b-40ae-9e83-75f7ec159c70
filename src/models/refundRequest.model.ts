import mongoose, { Document, Schema } from 'mongoose';

export interface IRefundRequest extends Document {
  studentId: mongoose.Types.ObjectId;
  subscriptionId?: mongoose.Types.ObjectId;
  lessonId?: mongoose.Types.ObjectId;
  amount: number; // in cents
  requestedAmount: number; // original requested amount in dollars
  refundType: 'subscription' | 'lesson' | 'other';
  reason: string;
  description?: string;
  status: 'pending' | 'approved' | 'rejected' | 'processed' | 'cancelled';
  adminNotes?: string;
  rejectionReason?: string;
  processedBy?: mongoose.Types.ObjectId; // Admin who processed the request
  processedAt?: Date;
  stripeRefundId?: string;
  transactionId?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  canBeCancelled(): boolean;
  canBeApproved(): boolean;
  canBeRejected(): boolean;
  markAsProcessed(adminId: mongoose.Types.ObjectId, transactionId?: mongoose.Types.ObjectId): Promise<IRefundRequest>;
}

const refundRequestSchema = new Schema<IRefundRequest>({
  studentId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Student', 
    required: true 
  },
  subscriptionId: {
    type: Schema.Types.ObjectId,
    ref: 'Subscription'
  },
  lessonId: {
    type: Schema.Types.ObjectId,
    ref: 'Lesson'
  },
  amount: { 
    type: Number, 
    required: true,
    min: 0
  },
  requestedAmount: {
    type: Number,
    required: true,
    min: 0
  },
  refundType: {
    type: String,
    enum: ['subscription', 'lesson', 'other'],
    required: true
  },
  reason: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'processed', 'cancelled'],
    default: 'pending'
  },
  adminNotes: {
    type: String,
    trim: true
  },
  rejectionReason: {
    type: String,
    trim: true
  },
  processedBy: {
    type: Schema.Types.ObjectId,
    ref: 'Admin'
  },
  processedAt: {
    type: Date
  },
  stripeRefundId: {
    type: String
  },
  transactionId: {
    type: Schema.Types.ObjectId,
    ref: 'Transaction'
  }
}, {
  timestamps: true
});

// Indexes for better query performance
refundRequestSchema.index({ studentId: 1, status: 1 });
refundRequestSchema.index({ status: 1, createdAt: -1 });
refundRequestSchema.index({ processedBy: 1 });
refundRequestSchema.index({ refundType: 1 });

// Virtual for student details
refundRequestSchema.virtual('student', {
  ref: 'Student',
  localField: 'studentId',
  foreignField: '_id',
  justOne: true
});

// Virtual for admin details
refundRequestSchema.virtual('admin', {
  ref: 'Admin',
  localField: 'processedBy',
  foreignField: '_id',
  justOne: true
});

// Virtual for transaction details
refundRequestSchema.virtual('transaction', {
  ref: 'Transaction',
  localField: 'transactionId',
  foreignField: '_id',
  justOne: true
});

// Virtual for subscription details
refundRequestSchema.virtual('subscription', {
  ref: 'Subscription',
  localField: 'subscriptionId',
  foreignField: '_id',
  justOne: true
});

// Virtual for lesson details
refundRequestSchema.virtual('lesson', {
  ref: 'Lesson',
  localField: 'lessonId',
  foreignField: '_id',
  justOne: true
});

// Instance methods
refundRequestSchema.methods.canBeCancelled = function(): boolean {
  return this.status === 'pending';
};

refundRequestSchema.methods.canBeApproved = function(): boolean {
  return this.status === 'pending';
};

refundRequestSchema.methods.canBeRejected = function(): boolean {
  return this.status === 'pending';
};

refundRequestSchema.methods.markAsProcessed = function(adminId: mongoose.Types.ObjectId, transactionId?: mongoose.Types.ObjectId) {
  this.status = 'processed';
  this.processedBy = adminId;
  this.processedAt = new Date();
  if (transactionId) {
    this.transactionId = transactionId;
  }
  return this.save();
};

// Static methods
refundRequestSchema.statics.findPending = function() {
  return this.find({ status: 'pending' })
    .populate('studentId', 'firstname lastname email')
    .populate('subscriptionId')
    .populate('lessonId')
    .sort({ createdAt: -1 });
};

refundRequestSchema.statics.findByStudent = function(studentId: string) {
  return this.find({ studentId })
    .populate('processedBy', 'firstname lastname')
    .populate('subscriptionId')
    .populate('lessonId')
    .sort({ createdAt: -1 });
};

refundRequestSchema.statics.findByStatus = function(status: string) {
  return this.find({ status })
    .populate('studentId', 'firstname lastname email')
    .populate('processedBy', 'firstname lastname')
    .populate('subscriptionId')
    .populate('lessonId')
    .sort({ createdAt: -1 });
};

// Pre-save middleware
refundRequestSchema.pre('save', function(next) {
  // Convert requested amount to cents for storage
  if (this.isModified('requestedAmount')) {
    this.amount = Math.round(this.requestedAmount * 100);
  }
  
  // Set processedAt when status changes to processed, approved, or rejected
  if (this.isModified('status') && ['processed', 'approved', 'rejected'].includes(this.status)) {
    if (!this.processedAt) {
      this.processedAt = new Date();
    }
  }
  
  next();
});

// Validation middleware
refundRequestSchema.pre('save', function(next) {
  // Ensure either subscriptionId or lessonId is provided for subscription/lesson refunds
  if (this.refundType === 'subscription' && !this.subscriptionId) {
    return next(new Error('Subscription ID is required for subscription refunds'));
  }
  
  if (this.refundType === 'lesson' && !this.lessonId) {
    return next(new Error('Lesson ID is required for lesson refunds'));
  }
  
  next();
});

// Serialize virtuals
refundRequestSchema.set('toJSON', { virtuals: true });
refundRequestSchema.set('toObject', { virtuals: true });

const RefundRequest = mongoose.model<IRefundRequest>('RefundRequest', refundRequestSchema);

export default RefundRequest;
