import { Schema, model, Document, Types } from 'mongoose';

export interface IPaymentHistory {
  amount: number;
  currency: string;
  status: string;
  stripePaymentIntentId: string;
  stripeInvoiceId: string;
  paidAt: Date;
  description: string;
}

export interface ISubscription extends Document {
  _id: Types.ObjectId;
  studentId: Types.ObjectId;
  tutorId: Types.ObjectId;
  stripeSubscriptionId?: string;
  stripeCustomerId?: string;
  stripePriceId?: string;
  stripePaymentMethodId?: string;
  planType: string;
  lessonsPerWeek: 1 | 2 | 3 | 5;
  monthlyPrice: number;
  status: 'active' | 'paused' | 'cancelled' | 'pending_transfer' | 'incomplete';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  nextBillingDate: Date;
  remainingLessons: number;
  autoRenew: boolean;
  metadata?: any;
  paymentHistory?: IPaymentHistory[];
  startDate?: Date; // Added for compatibility with email services
  endDate?: Date; // Added for compatibility with email services
  cancellationReason?: string;
  cancellationDescription?: string;
  cancelledAt?: Date;
  pausedAt?: Date;
  pauseEndDate?: Date;
  pauseDuration?: number;
  createdAt?: Date;
  updatedAt?: Date;
  canScheduleLesson(): boolean;
  getRemainingDays(): number;
  isWithinBillingPeriod(date: Date): boolean;
}

const subscriptionSchema = new Schema<ISubscription>({
  studentId: { type: Schema.Types.ObjectId, ref: 'Student', required: true },
  tutorId: { type: Schema.Types.ObjectId, ref: 'Tutor', required: true },

  stripeSubscriptionId: { type: String },
  stripeCustomerId: { type: String },
  stripePriceId: { type: String },
  stripePaymentMethodId: { type: String },

  planType: { type: String, required: true },
  lessonsPerWeek: { type: Number, enum: [1, 2, 3, 5], required: true },
  monthlyPrice: { type: Number, required: true, min: 0 },

  status: {
    type: String,
    enum: ['active', 'paused', 'cancelled', 'pending_transfer', 'incomplete'],
    default: 'incomplete' // Changed default to incomplete
  },

  // Virtual properties for compatibility
  startDate: { type: Date },
  endDate: { type: Date },

  currentPeriodStart: { type: Date, required: true },
  currentPeriodEnd: { type: Date, required: true },
  nextBillingDate: { type: Date, required: true },
  remainingLessons: { type: Number, required: true, min: 0 },

  autoRenew: { type: Boolean, default: true },
  metadata: { type: Schema.Types.Mixed },

  // Cancellation fields
  cancellationReason: { type: String },
  cancellationDescription: { type: String },
  cancelledAt: { type: Date },

  // Pause fields
  pausedAt: { type: Date },
  pauseEndDate: { type: Date },
  pauseDuration: { type: Number }, // Duration in days

  paymentHistory: [{
    amount: { type: Number, required: true, min: 0 },
    currency: { type: String, required: true, default: 'usd' },
    status: { type: String, required: true, enum: ['pending', 'succeeded', 'failed', 'canceled'] },
    stripePaymentIntentId: { type: String, required: false }, // Made optional since some payments might not have payment intent
    stripeInvoiceId: { type: String, required: false }, // Made optional for flexibility
    paidAt: { type: Date, required: true },
    description: { type: String, required: true }
  }]
}, {
  timestamps: true
});

// Virtuals
subscriptionSchema.virtual('student', {
  ref: 'Student',
  localField: 'studentId',
  foreignField: '_id',
  justOne: true
});

subscriptionSchema.virtual('tutor', {
  ref: 'Tutor',
  localField: 'tutorId',
  foreignField: '_id',
  justOne: true
});

subscriptionSchema.virtual('lessons', {
  ref: 'Lesson',
  localField: '_id',
  foreignField: 'subscriptionId'
});

// Indexes
subscriptionSchema.index({ studentId: 1, status: 1 });
subscriptionSchema.index({ tutorId: 1, status: 1 });
subscriptionSchema.index({ nextBillingDate: 1 });
subscriptionSchema.index({ stripeSubscriptionId: 1 }, { unique: true, sparse: true }); // Added for webhook lookups
subscriptionSchema.index({ stripeCustomerId: 1 });
subscriptionSchema.index({ status: 1, createdAt: -1 });
subscriptionSchema.index({ currentPeriodEnd: 1 }); // For expiration checks

// Methods
subscriptionSchema.methods.canScheduleLesson = function (): boolean {
  return this.status === 'active' && this.remainingLessons > 0;
};

subscriptionSchema.methods.getRemainingDays = function (): number {
  const now = new Date();
  const end = new Date(this.currentPeriodEnd);
  const diff = end.getTime() - now.getTime();
  return Math.ceil(diff / (1000 * 60 * 60 * 24));
};

subscriptionSchema.methods.isWithinBillingPeriod = function (date: Date): boolean {
  return date >= this.currentPeriodStart && date <= this.currentPeriodEnd;
};

// Static methods
subscriptionSchema.statics.findActive = function () {
  return this.find({ status: 'active' });
};

subscriptionSchema.statics.findByStripeId = function (stripeSubscriptionId: string) {
  return this.findOne({ stripeSubscriptionId });
};

subscriptionSchema.statics.findExpiring = function (days: number = 7) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);
  return this.find({
    status: 'active',
    currentPeriodEnd: { $lte: futureDate },
    autoRenew: true
  });
};

subscriptionSchema.statics.findByStudent = function (studentId: string) {
  return this.find({ studentId }).populate('tutorId', 'basePrice teachingSubjects rating firstname lastname');
};

subscriptionSchema.statics.findByTutor = function (tutorId: string) {
  return this.find({ tutorId }).populate('studentId', 'learningReasons skillsToImprove firstname lastname');
};

// Pre-save hook
subscriptionSchema.pre('save', function (next) {
  if (this.isNew && this.status === 'active') {
    // Only set remaining lessons for active subscriptions
    if (!this.remainingLessons) {
      this.remainingLessons = this.lessonsPerWeek * 4;
    }
  }

  // Validate period dates
  if (this.currentPeriodStart && this.currentPeriodEnd) {
    if (this.currentPeriodStart >= this.currentPeriodEnd) {
      return next(new Error('Current period start must be before current period end'));
    }
  }

  // Ensure nextBillingDate is set
  if (!this.nextBillingDate && this.currentPeriodEnd) {
    this.nextBillingDate = this.currentPeriodEnd;
  }

  next();
});

// Pre-update hook
subscriptionSchema.pre('findOneAndUpdate', function (next) {
  const update = this.getUpdate() as any;
  if (update && update.currentPeriodStart && update.currentPeriodEnd) {
    if (update.currentPeriodStart >= update.currentPeriodEnd) {
      return next(new Error('Current period start must be before current period end'));
    }
  }
  next();
});

// Virtuals serialization
subscriptionSchema.set('toJSON', { virtuals: true });
subscriptionSchema.set('toObject', { virtuals: true });

const Subscription = model<ISubscription>('Subscription', subscriptionSchema);
export default Subscription;