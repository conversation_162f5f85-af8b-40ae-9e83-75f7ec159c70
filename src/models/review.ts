import { Schema, model, Types, Document } from "mongoose";
import { PROFILE_REGISTRATION_MODELS, ProfileModelType } from "./profile";
import { Lesson } from "./lesson.model";

export interface IReview extends Document {
  createdBy: Types.ObjectId;
  createdByModel: ProfileModelType;
  targetUser: Types.ObjectId;
  targetUserModel: ProfileModelType;
  lesson: Lesson | Schema.Types.ObjectId;
  rating: number;
  comment?: string;
  createdAt: Date;
  updatedAt: Date;
}

const ReviewSchema = new Schema<IReview>(
  {
    createdBy: {
      type: Schema.Types.ObjectId,
      required: true,
      refPath: "createdByModel",
    },
    createdByModel: {
      type: String,
      required: true,
      enum: PROFILE_REGISTRATION_MODELS,
    },
    targetUser: {
      type: Schema.Types.ObjectId,
      required: true,
      refPath: "targetUserModel",
    },
    targetUserModel: {
      type: String,
      required: true,
      enum: PROFILE_REGISTRATION_MODELS,
    },
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5,
    },
    comment: {
      type: String,
      maxlength: 1000,
    },
    lesson: {
      type: Schema.Types.ObjectId,
      ref: "Lesson",
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

export const Review = model<IReview>("Review", ReviewSchema);
