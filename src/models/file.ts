import { Schema } from "mongoose";
import { DEFAULT_SCHEMA_OPTS } from "./utils";

export type FileProps = {
  url: string;
  mimetype: string;
  size: string;
};

export const FileSchema = new Schema(
  {
    url: {
      type: String,
      required: "File url is required",
    },
    mimetype: {
      type: String,
      required: "File mimetype url is required",
    },
    size: {
      type: String,
      required: "File size is required",
    },
  },
  {
    ...DEFAULT_SCHEMA_OPTS,
    timestamps: false,
  }
);
