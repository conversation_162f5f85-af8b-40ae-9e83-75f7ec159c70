import { Schema, model, Document, Types } from 'mongoose';

export interface IRecurringEventPattern extends Document {
  eventId: Types.ObjectId;
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval?: number;
  daysOfWeek?: string[]; // e.g., ['Mon', 'Wed']
  dayOfMonth?: number;
  monthOfYear?: number;
  startDate: Date;
  endDate?: Date;
  occurrenceCount?: number;
  createdAt: Date;
  updatedAt: Date;
}

const recurringEventPatternSchema = new Schema<IRecurringEventPattern>({
  eventId: { type: Schema.Types.ObjectId, ref: 'Event', required: true },
  frequency: { type: String, enum: ['daily', 'weekly', 'monthly', 'yearly'], required: true },
  interval: { type: Number, default: 1 },
  daysOfWeek: [String],
  dayOfMonth: Number,
  monthOfYear: Number,
  startDate: { type: Date, required: true },
  endDate: Date,
  occurrenceCount: Number,
  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

export const RecurringEventPattern = model<IRecurringEventPattern>('RecurringEventPattern', recurringEventPatternSchema);
