import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { TransactionService } from '../services/transactionService';
import TransactionModel from '../models/transaction.model';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';

/**
 * Get user's transaction history
 */
export const getUserTransactions = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const {
      page = 1,
      limit = 20,
      type,
      category,
      status,
      startDate,
      endDate
    } = req.query;

    const options = {
      page: Number(page),
      limit: Number(limit),
      type: type as string,
      category: category as string,
      status: status as string,
      startDate: startDate ? new Date(startDate as string) : undefined,
      endDate: endDate ? new Date(endDate as string) : undefined
    };

    const result = await TransactionService.getUserTransactions(
      req.user._id as Types.ObjectId,
      options
    );

    res.json({
      success: true,
      data: result.transactions,
      summary: result.summary,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: result.total,
        pages: Math.ceil(result.total / Number(limit))
      }
    });
  } catch (error: any) {
    console.error('Error fetching user transactions:', error);
    createErrorResponse(res, 'Failed to fetch transactions', 500);
  }
};

/**
 * Get transaction by ID
 */
export const getTransactionById = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const { transactionId } = req.params;

    if (!Types.ObjectId.isValid(transactionId)) {
      createErrorResponse(res, 'Invalid transaction ID format', 400);
      return;
    }

    const transaction = await TransactionModel.findOne({
      _id: transactionId,
      $or: [
        { userId: req.user._id },
        { relatedUserId: req.user._id }
      ]
    })
      .populate('userId', 'firstname lastname email role')
      .populate('relatedUserId', 'firstname lastname email role')
      .populate('subscription', 'planType monthlyPrice status')
      .populate('lesson', 'title scheduledAt status')
      .populate('withdrawalRequest')
      .populate('refundRequest');

    if (!transaction) {
      createErrorResponse(res, 'Transaction not found', 404);
      return;
    }

    res.json({
      success: true,
      data: transaction
    });
  } catch (error: any) {
    console.error('Error fetching transaction:', error);
    createErrorResponse(res, 'Failed to fetch transaction', 500);
  }
};

/**
 * Get user's financial summary
 */
export const getUserFinancialSummary = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const { startDate, endDate } = req.query;
    const userId = req.user._id as Types.ObjectId;

    const dateFilter: any = {};
    if (startDate) dateFilter.$gte = new Date(startDate as string);
    if (endDate) dateFilter.$lte = new Date(endDate as string);

    const matchStage: any = { 
      userId,
      status: 'completed'
    };
    if (Object.keys(dateFilter).length > 0) {
      matchStage.createdAt = dateFilter;
    }

    // Get summary by category
    const categorySummary = await TransactionModel.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$category',
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 },
          avgAmount: { $avg: '$amount' }
        }
      }
    ]);

    // Get summary by type
    const typeSummary = await TransactionModel.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$type',
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Get monthly trends
    const monthlyTrends = await TransactionModel.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            category: '$category'
          },
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': -1, '_id.month': -1 } },
      { $limit: 12 }
    ]);

    // Calculate totals
    const totals = categorySummary.reduce((acc, item) => {
      acc[item._id] = {
        totalAmount: item.totalAmount / 100,
        count: item.count,
        avgAmount: item.avgAmount / 100
      };
      return acc;
    }, {});

    res.json({
      success: true,
      data: {
        totals,
        categorySummary: categorySummary.map(item => ({
          category: item._id,
          totalAmount: item.totalAmount / 100,
          count: item.count,
          avgAmount: item.avgAmount / 100
        })),
        typeSummary: typeSummary.map(item => ({
          type: item._id,
          totalAmount: item.totalAmount / 100,
          count: item.count
        })),
        monthlyTrends: monthlyTrends.map(item => ({
          year: item._id.year,
          month: item._id.month,
          category: item._id.category,
          totalAmount: item.totalAmount / 100,
          count: item.count
        }))
      }
    });
  } catch (error: any) {
    console.error('Error fetching financial summary:', error);
    createErrorResponse(res, 'Failed to fetch financial summary', 500);
  }
};

/**
 * Create manual transaction (Admin only)
 */
export const createManualTransaction = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'admin') {
      createErrorResponse(res, 'Admin access required', 403);
      return;
    }

    const {
      userId,
      relatedUserId,
      amount,
      type,
      category,
      description,
      adminNotes,
      metadata
    } = req.body;

    if (!userId || !amount || !type || !description) {
      createErrorResponse(res, 'Missing required fields: userId, amount, type, description', 400);
      return;
    }

    if (!Types.ObjectId.isValid(userId)) {
      createErrorResponse(res, 'Invalid user ID format', 400);
      return;
    }

    if (relatedUserId && !Types.ObjectId.isValid(relatedUserId)) {
      createErrorResponse(res, 'Invalid related user ID format', 400);
      return;
    }

    const transaction = await TransactionService.createTransaction({
      userId: new Types.ObjectId(userId),
      relatedUserId: relatedUserId ? new Types.ObjectId(relatedUserId) : undefined,
      amount: Math.round(amount * 100), // Convert to cents
      type,
      category,
      status: 'completed',
      paymentMethod: 'manual',
      description,
      adminNotes,
      metadata: {
        ...metadata,
        createdByAdmin: req.user._id,
        createdAt: new Date()
      }
    });

    res.status(201).json({
      success: true,
      message: 'Manual transaction created successfully',
      data: transaction
    });
  } catch (error: any) {
    console.error('Error creating manual transaction:', error);
    createErrorResponse(res, 'Failed to create manual transaction', 500);
  }
};

/**
 * Get transaction statistics (Admin only)
 */
export const getTransactionStatistics = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'admin') {
      createErrorResponse(res, 'Admin access required', 403);
      return;
    }

    const { startDate, endDate } = req.query;

    const analytics = await TransactionService.getFinancialAnalytics(
      startDate ? new Date(startDate as string) : undefined,
      endDate ? new Date(endDate as string) : undefined
    );

    // Convert amounts from cents to dollars
    const formattedAnalytics = {
      revenue: {
        totalRevenue: analytics.revenue.totalRevenue / 100,
        totalTransactions: analytics.revenue.totalTransactions,
        avgTransactionAmount: analytics.revenue.avgTransactionAmount / 100,
        totalPlatformFees: analytics.revenue.totalPlatformFees / 100
      },
      categoryBreakdown: analytics.categoryBreakdown.map(item => ({
        category: item._id,
        totalAmount: item.totalAmount / 100,
        count: item.count
      })),
      dailyTrends: analytics.dailyTrends.map(item => ({
        date: item._id.date,
        category: item._id.category,
        totalAmount: item.totalAmount / 100,
        count: item.count
      }))
    };

    res.json({
      success: true,
      data: formattedAnalytics
    });
  } catch (error: any) {
    console.error('Error fetching transaction statistics:', error);
    createErrorResponse(res, 'Failed to fetch transaction statistics', 500);
  }
};
