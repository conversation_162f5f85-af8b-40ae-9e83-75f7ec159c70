import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { AuthRequest } from '../types/AuthRequest';
import Tutor from '../models/tutor';
import {
  canStudentBookSession,
  getStudentSubscriptionSummary,
  canStudentBookWithAnyTutor
} from '../services/subscriptionCheckService';

/**
 * Check if student can book with a specific tutor
 */
export const checkBookingEligibility = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.user.role !== 'student') {
      return res.status(403).json({ message: 'Only students can check booking eligibility' });
    }

    const { tutorId } = req.params;

    if (!tutorId || !Types.ObjectId.isValid(tutorId)) {
      return res.status(400).json({ message: 'Valid tutor ID is required' });
    }

    const eligibilityCheck = await canStudentBookSession(
      req.user._id as any,
      new Types.ObjectId(tutorId)
    );

    res.json({
      success: true,
      data: {
        canBook: eligibilityCheck.canBook,
        reason: eligibilityCheck.reason,
        subscriptionStatus: eligibilityCheck.subscriptionStatus,
        remainingLessons: eligibilityCheck.remainingLessons,
        isFreeTrial: eligibilityCheck.isFreeTrial,
        freeTrialUsed: eligibilityCheck.freeTrialUsed,
        freeTrialLessonsRemaining: eligibilityCheck.freeTrialLessonsRemaining
      }
    });
  } catch (error: any) {
    console.error('Check booking eligibility error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check booking eligibility',
      error: error.message
    });
  }
};

/**
 * Get student's complete subscription summary
 */
export const getSubscriptionSummary = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.user.role !== 'student') {
      return res.status(403).json({ message: 'Only students can view subscription summary' });
    }

    const summary = await getStudentSubscriptionSummary(req.user._id as Types.ObjectId);

    res.json({
      success: true,
      data: summary
    });
  } catch (error: any) {
    console.error('Get subscription summary error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get subscription summary',
      error: error.message
    });
  }
};

/**
 * Check general booking eligibility (can book with any tutor)
 */
export const checkGeneralBookingEligibility = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.user.role !== 'student') {
      return res.status(403).json({ message: 'Only students can check booking eligibility' });
    }

    const canBook = await canStudentBookWithAnyTutor(req.user._id as Types.ObjectId);

    res.json({
      success: true,
      data: {
        canBookWithAnyTutor: canBook,
        message: canBook 
          ? 'Student can book sessions' 
          : 'Student needs an active subscription or free trial to book sessions'
      }
    });
  } catch (error: any) {
    console.error('Check general booking eligibility error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check general booking eligibility',
      error: error.message
    });
  }
};

/**
 * Get booking requirements and pricing info
 */
export const getBookingRequirements = async (req: Request, res: Response): Promise<any> => {
  try {
    const { tutorId } = req.query;

    let subscriptionPlans;
    let tutor = null;

    if (tutorId) {
      // If tutorId is provided, calculate pricing based on tutor's basePrice
      tutor = await Tutor.findById(tutorId);
      if (!tutor) {
        return res.status(404).json({
          success: false,
          error: 'Tutor not found'
        });
      }

      if (!tutor.basePrice || tutor.basePrice <= 0) {
        return res.status(400).json({
          success: false,
          error: 'Tutor has not set a valid base price per lesson'
        });
      }

      const availableLessonsPerWeek = [1, 2, 3, 5];
      subscriptionPlans = availableLessonsPerWeek.map(lessonsPerWeek => {
        // Calculate with 10% processing fee
        const basePricePerLessonCents = Math.round(tutor!.basePrice * 100);
        const baseMonthlyPriceCents = basePricePerLessonCents * lessonsPerWeek * 4;
        const processingFee = Math.round(baseMonthlyPriceCents * 0.10); // 10% processing fee
        const monthlyPriceCents = baseMonthlyPriceCents + processingFee;

        return {
          planType: `${lessonsPerWeek}-lesson`,
          lessonsPerWeek,
          monthlyPrice: monthlyPriceCents / 100, // Convert back to dollars
          pricePerLesson: tutor!.basePrice,
          baseMonthlyPrice: baseMonthlyPriceCents / 100, // Base price without processing fee
          processingFee: processingFee / 100, // Processing fee amount
          description: `${lessonsPerWeek} lesson${lessonsPerWeek > 1 ? 's' : ''} per week`
        };
      });
    } else {
      // Generic pricing information without specific tutor
      subscriptionPlans = [
        {
          planType: '1-lesson',
          lessonsPerWeek: 1,
          description: '1 lesson per week',
          note: 'Pricing varies by tutor'
        },
        {
          planType: '2-lesson',
          lessonsPerWeek: 2,
          description: '2 lessons per week',
          note: 'Pricing varies by tutor'
        },
        {
          planType: '3-lesson',
          lessonsPerWeek: 3,
          description: '3 lessons per week',
          note: 'Pricing varies by tutor'
        },
        {
          planType: '5-lesson',
          lessonsPerWeek: 5,
          description: '5 lessons per week',
          note: 'Pricing varies by tutor'
        }
      ];
    }

    const requirements = {
      subscriptionPlans,
      freeTrial: {
        available: true,
        lessonsIncluded: 2,
        description: 'Try 2 free lessons before subscribing',
        restrictions: [
          'Available only once per student',
          'Must be used with the same tutor',
          'Cannot be transferred between tutors'
        ]
      },
      bookingRules: [
        'Students must have an active subscription or available free trial lessons',
        'Free trial lessons are limited to 2 per student',
        'Subscription lessons are specific to the tutor you subscribe with',
        'Lessons cannot be booked in the past',
        'Time slots must be available (no conflicts)'
      ],
      pricingNote: tutor ?
        `Pricing shown is specific to this tutor (${tutor.firstname} ${tutor.lastname}). Includes 10% processing fee.` :
        'Pricing varies by tutor. Provide tutorId parameter to see specific pricing. All prices include 10% processing fee.'
    };

    res.json({
      success: true,
      data: requirements
    });
  } catch (error: any) {
    console.error('Get booking requirements error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get booking requirements',
      error: error.message
    });
  }
};
