import { Request, Response } from "express";
import Student from "../models/student";
import { createErrorResponse } from "../middlewares/errorHandler";
import { createOkResponse } from "../utils/misc";
import { updateProfileHook } from "../hooks/profile";

export const updateStudentProfile = async (req: Request, res: Response) => {
  try {
    const { profile, errorDetails } = await updateProfileHook(
      Student,
      req.body,
      req.user,
      true
    );

    createOkResponse(res, {
      details: errorDetails,
      success: !errorDetails,
      message: `Profile updated successfully.`,
      data: profile,
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};
