import { Request, Response } from 'express';
import Student from '../models/student';
import <PERSON><PERSON> from '../models/tutor';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';
import { createOkResponse } from '../utils/misc';
import { uploadAndDeleteFile } from '../utils/file';

// Helper function to validate base64 image
const validateBase64Image = (base64String: string): boolean => {
  // Check if it's a valid base64 image format
  const base64Regex = /^data:image\/(jpeg|jpg|png|webp);base64,/;
  return base64Regex.test(base64String);
};

export class ProfileImageController {
  // Upload profile image
  static async uploadProfileImage(req: AuthRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;
      const { imageBase64 } = req.body;

      if (!userId) {
        createErrorResponse(res, 'User not authenticated', 401);
        return;
      }

      if (!imageBase64) {
        createErrorResponse(res, 'No image data provided', 400);
        return;
      }

      // Validate base64 image format
      if (!validateBase64Image(imageBase64)) {
        createErrorResponse(res, 'Invalid image format. Only JPEG, PNG, and WebP images are allowed.', 400);
        return;
      }

      let user;
      if (userRole === 'student') {
        user = await Student.findById(userId);
      } else if (userRole === 'tutor') {
        user = await Tutor.findById(userId);
      } else {
        createErrorResponse(res, 'Invalid user role', 400);
        return;
      }

      if (!user) {
        createErrorResponse(res, 'User not found', 404);
        return;
      }

      // Upload to Cloudinary and delete old image if exists
      const uploadResult = await uploadAndDeleteFile(imageBase64, user.image);

      // Update user with new image URL
      user.image = uploadResult.url;
      await user.save();

      createOkResponse(res, {
        success: true,
        message: 'Profile image uploaded successfully',
        data: {
          imageUrl: uploadResult.url,
        },
      });
    } catch (error: any) {
      console.error('Error uploading profile image:', error);
      createErrorResponse(res, error.message || 'Failed to upload profile image', 500);
    }
  }

  // Delete profile image
  static async deleteProfileImage(req: AuthRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId) {
        createErrorResponse(res, 'User not authenticated', 401);
        return;
      }

      let user;
      if (userRole === 'student') {
        user = await Student.findById(userId);
      } else if (userRole === 'tutor') {
        user = await Tutor.findById(userId);
      } else {
        createErrorResponse(res, 'Invalid user role', 400);
        return;
      }

      if (!user) {
        createErrorResponse(res, 'User not found', 404);
        return;
      }

      if (!user.image) {
        createErrorResponse(res, 'No profile image to delete', 400);
        return;
      }

      // Delete image from Cloudinary using the existing utility
      const { deleteFile } = await import('../utils/file');
      try {
        await deleteFile(user.image);
      } catch (error) {
        console.warn('Failed to delete profile image from Cloudinary:', error);
      }

      // Remove image reference from user
      user.image = undefined;
      await user.save();

      createOkResponse(res, {
        success: true,
        message: 'Profile image deleted successfully',
      });
    } catch (error: any) {
      console.error('Error deleting profile image:', error);
      createErrorResponse(res, 'Failed to delete profile image', 500);
    }
  }

  // Get profile image URL
  static async getProfileImage(req: AuthRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId) {
        createErrorResponse(res, 'User not authenticated', 401);
        return;
      }

      let user;
      if (userRole === 'student') {
        user = await Student.findById(userId).select('image');
      } else if (userRole === 'tutor') {
        user = await Tutor.findById(userId).select('image');
      } else {
        createErrorResponse(res, 'Invalid user role', 400);
        return;
      }

      if (!user) {
        createErrorResponse(res, 'User not found', 404);
        return;
      }

      createOkResponse(res, {
        success: true,
        data: {
          imageUrl: user.image || null,
        },
      });
    } catch (error: any) {
      console.error('Error fetching profile image:', error);
      createErrorResponse(res, 'Failed to fetch profile image', 500);
    }
  }
}
