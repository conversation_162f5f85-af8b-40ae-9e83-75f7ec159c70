import { Request, Response } from "express";
import { comparePassword, generateToken, hashPassword } from "../utils/hashing";
import { createOkResponse } from "../utils/misc";
import {
  createErrorResponse,
  getErrorResponse,
} from "../middlewares/errorHandler";
import { sendMail, sendResetCode } from "../utils/mailer";
import crypto from "crypto";
import { passwordMatchCondition } from "../utils/validation";
import {
  AUTH_PROVIDERS,
  DEFAULT_AUTH_PROVIDER,
  ERROR_FORBIDDEN_ACCESS,
  ERROR_INVALID_ROLE,
  ERROR_MISSING_PROVIDER,
} from "../config/constants";
import { PROFILE_REGISTRATION_ROLES } from "../models/profile";
import {
  getProfile,
  getProfileModelByRole,
  updateProfileMedias,
  USER_PROFILE_TYPE,
} from "../utils/profile";
import {
  studentWelcomeTemplate,
  tutorWelcomeTemplate,
} from "../utils/emails-templates/email-templates";
import { logError } from "../utils/logger";
import {
  createTutorCalendars,
  createStudentCalendars,
} from "../services/calendarService";

const registerHook = async (req: Request): Promise<USER_PROFILE_TYPE> => {
  try {
    const body = req.body;

    const provider = body.provider || DEFAULT_AUTH_PROVIDER;

    body.provider = provider;

    if (!AUTH_PROVIDERS.includes(provider)) {
      throw getErrorResponse(
        {
          message: "Invalid authentication provider",
          details: {
            allowedAuthProviders: AUTH_PROVIDERS,
          },
        },
        400
      );
    }

    const withPassword = provider === "local";

    if (
      withPassword &&
      (!body.password || !passwordMatchCondition[0].test(body.password))
    ) {
      throw getErrorResponse(passwordMatchCondition[1], 400);
    }

    const profile = await getProfile({
      email: req.body.email,
    });

    if (profile) throw getErrorResponse("Profile already exists", 400);

    const Profile = getProfileModelByRole(body.role);

    if (withPassword) body.password = await hashPassword(body.password);
    else delete body.password;

    await updateProfileMedias(body, null, true);

    const newProfile = (await Profile.create(body)) as any;

    try {
      const isTutor = newProfile.role === "tutor";

      await sendMail(
        newProfile.email,
        isTutor
          ? tutorWelcomeTemplate(newProfile)
          : studentWelcomeTemplate(newProfile)
      );
    } catch (err: any) {
      logError(`Failed to send welcome email`, "mail", {
        email: newProfile.email,
        role: newProfile.role,
      });
    }

    // Create default calendars for the new user
    try {
      if (newProfile.role === "tutor") {
        // Extract teaching subjects if available
        const subjects =
          newProfile.teachingSubjects?.map((subject: any) => subject.title) ||
          [];
        await createTutorCalendars(newProfile._id, subjects);
      } else if (newProfile.role === "student") {
        await createStudentCalendars(newProfile._id);
      }
    } catch (err: any) {
      logError(`Failed to create default calendars`, "calendar", {
        userId: newProfile._id,
        role: newProfile.role,
        error: err.message,
      });
      // Don't throw error here as calendar creation failure shouldn't prevent registration
    }

    return newProfile;
  } catch (err: any) {
    throw err;
  }
};

export const register = async (req: Request, res: Response) => {
  try {
    if (!PROFILE_REGISTRATION_ROLES.includes(req.body.role)) {
      createErrorResponse(res, ERROR_INVALID_ROLE);
      return;
    }

    const user = await registerHook(req);

    createOkResponse(res, {
      data: user,
      message: "Profile created successfully.",
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const login = async (req: Request, res: Response) => {
  try {
    const body = req.body;

    let user = await getProfile({
      email: body.email,
      id: body.id,
    });

    if (!user) {
      createErrorResponse(res, "Profile not found.", 400);
      return;
    }

    if (!user.provider) {
      createErrorResponse(res, ERROR_MISSING_PROVIDER);
      return;
    }

    if (body.provider && body.provider !== DEFAULT_AUTH_PROVIDER) {
      // if (!user) user = await registerHook(req);
    } else if (
      !user.password ||
      !(await comparePassword(body.password, user.password))
    ) {
      createErrorResponse(res, "Email or Password is incorrect.", 400);
      return;
    }

    user!.isLoggedIn = true;

    await user!.save({ validateModifiedOnly: true });

    createOkResponse(res, {
      message: "Login successful.",
      data: {
        user,
        accessToken: generateToken(
          { id: user!.id, role: user!.role },
          { rememberMe: body.rememberMe }
        ),
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const forgotPassword = async (req: Request, res: Response) => {
  try {
    const { email } = req.body;

    const user = await getProfile({ email });

    if (!user) {
      createErrorResponse(res, "Profile not found", 404);
      return;
    }
    const code = crypto.randomInt(100000, 999999).toString();

    user.resetCode = code;

    const date = new Date(Date.now() + 10 * 60 * 1000); // 10 min

    user.resetCodeExpires = date;

    await user.save({ validateModifiedOnly: true });

    await sendResetCode({ email, id: user.id }, code);

    createOkResponse(res, {
      message: "Reset code sent to your email",
      data: { email, id: user.id },
      details: {
        exirationMinutes: 10,
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};

export const verifyOTP = async (req: Request, res: Response) => {
  try {
    const { userId, code } = req.body;

    const user = await getProfile({ id: userId });

    if (!user) {
      createErrorResponse(res, "Profile not found", 404);
      return;
    }

    if (
      !user.resetCode ||
      !user.resetCodeExpires ||
      user.resetCode !== code ||
      user.resetCodeExpires < new Date()
    ) {
      createErrorResponse(res, "Invalid or expired reset code", 400);
      return;
    }

    user.resetCode = undefined;

    await user.save({ validateModifiedOnly: true });

    createOkResponse(res, {
      message: "OTP verified successfully.",
      data: {
        accessToken: generateToken(
          { id: user.id, role: user.role, jti: "password" },
          { expiresIn: "10m" }
        ),
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const resetPassword = async (req: Request, res: Response) => {
  try {
    const { userId, newPassword } = req.body;

    if (
      !req.user.resetCodeExpires ||
      req.user?.id !== userId ||
      req.user.tokenId !== "password"
    ) {
      createErrorResponse(res, ERROR_FORBIDDEN_ACCESS);
      return;
    }

    const user = await getProfile({ id: userId });

    if (!user) {
      createErrorResponse(res, "Profile not found", 404);
      return;
    }

    user.password = await hashPassword(newPassword);

    user.resetCode = undefined;
    user.resetCodeExpires = undefined;

    await user.save({ validateModifiedOnly: true });

    createOkResponse(res, "Password reset successful");

    return;
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const logout = async (req: Request, res: Response) => {
  try {
    const user: USER_PROFILE_TYPE = req.user;

    user.isLoggedIn = false;

    if (req.body.settingsPreference)
      user.settingsPreference = {
        ...user,
        ...req.body.settingsPreference,
      };

    await user.save({ validateModifiedOnly: true });

    createOkResponse(res, "Logout successfully");
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};
