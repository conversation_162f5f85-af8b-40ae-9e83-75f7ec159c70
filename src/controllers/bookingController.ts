import { Request, Response } from 'express';
import { Booking } from '../models/Booking';
import { Event } from '../models/Event';
import { getUserCalendars } from '../services/calendarService';
import { canStudentBookSession, consumeLesson } from '../services/subscriptionCheckService';
import { Types } from 'mongoose';
import { getProfile } from '../utils/profile';
import LessonModel from '../models/lesson.model';
import Subscription from '../models/subscription.model';
import { createClassroomHook } from '../hooks/classroom';
import Tutor from '../models/tutor';
import Student from '../models/student';
import Classroom from '../models/classroom';
import { EmailNotificationService } from '../services/emailNotificationService';
import { AuthRequest } from '../types/AuthRequest';


export const createBooking = async (req: Request, res: Response): Promise<any> => {
  try {
    if (req.user.role !== 'learner') {
      return res.status(403).json({ message: 'Only learners can create bookings' });
    }

    const { eventId } = req.body;

    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: 'Event not found' });

    // Check if booking already exists for this learner and event
    const existingBooking = await Booking.findOne({ eventId, learnerId: req.user.id });
    if (existingBooking) {
      return res.status(409).json({ message: 'You already booked this session' });
    }

    const booking = new Booking({
      eventId,
      learnerId: req.user.id,
      status: 'pending',
    });

    await booking.save();
    res.status(201).json(booking);
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Tutor gets bookings for their events
export const getBookingsForTutor = async (req: Request, res: Response): Promise<any> => {
  try {
    if (req.user.role !== 'tutor') {
      return res.status(403).json({ message: 'Only tutors can view bookings' });
    }

    // Get all tutor's events
    const events = await Event.find({ createdBy: req.user.id });
    const eventIds = events.map(e => e._id);

    // Find bookings for those events
    const bookings = await Booking.find({ eventId: { $in: eventIds } }).populate('learnerId', 'name email');
    res.json(bookings);
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Update booking status (only tutor)
export const updateBookingStatus = async (req: Request, res: Response): Promise<any> => {
  try {
    if (req.user.role !== 'tutor') {
      return res.status(403).json({ message: 'Only tutors can update booking status' });
    }

    const bookingId = req.params.id;
    const { status } = req.body; // should be one of 'pending' | 'confirmed' | 'cancelled'

    const booking = await Booking.findById(bookingId);
    if (!booking) return res.status(404).json({ message: 'Booking not found' });

    // Verify tutor owns the event linked to booking
    const event = await Event.findById(booking.eventId);
    if (!event) return res.status(404).json({ message: 'Event not found' });
    // if (event.createdBy.toString() !== req.user.id) {
    //   return res.status(403).json({ message: 'Not authorized to update this booking' });
    // }

    if (!['pending', 'confirmed', 'cancelled'].includes(status)) {
      return res.status(400).json({ message: 'Invalid status' });
    }

    booking.status = status;
    booking.updatedAt = new Date();

    await booking.save();
    res.json(booking);
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Enhanced booking function that creates sessions in both tutor and student calendars
 * Supports the new JSON structure with optional studentId
 */
export const createEnhancedBooking = async (req: Request, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const {
      tutorId,
      studentId, // Optional - if not provided, use authenticated user
      title,
      description,
      startDateTime,
      endDateTime,
      subject
    } = req.body;

    // Validate required fields
    if (!tutorId || !title || !startDateTime || !endDateTime) {
      return res.status(400).json({
        message: 'tutorId, title, startDateTime, and endDateTime are required.'
      });
    }

    // Determine the actual student ID
    let actualStudentId: Types.ObjectId;
    if (studentId) {
      // If studentId is provided, validate it's a valid ObjectId
      if (!Types.ObjectId.isValid(studentId)) {
        return res.status(400).json({ message: 'Invalid studentId format' });
      }
      actualStudentId = new Types.ObjectId(studentId);
    } else {
      // Use authenticated user as student
      if (req.user.role !== 'student') {
        return res.status(403).json({
          message: 'Only students can book sessions, or provide a valid studentId'
        });
      }
      actualStudentId = req.user._id;
    }

    // Validate tutor ID
    if (!Types.ObjectId.isValid(tutorId)) {
      return res.status(400).json({ message: 'Invalid tutorId format' });
    }

    // Check if student can book sessions (subscription or free trial)
    const subscriptionCheck = await canStudentBookSession(
      actualStudentId,
      new Types.ObjectId(tutorId)
    );

    if (!subscriptionCheck.canBook) {
      return res.status(403).json({
        success: false,
        message: 'Cannot book session',
        reason: subscriptionCheck.reason,
        subscriptionStatus: subscriptionCheck.subscriptionStatus,
        freeTrialUsed: subscriptionCheck.freeTrialUsed,
        freeTrialLessonsRemaining: subscriptionCheck.freeTrialLessonsRemaining
      });
    }

    // Validate dates
    const startDate = new Date(startDateTime);
    const endDate = new Date(endDateTime);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return res.status(400).json({ message: 'Invalid date format' });
    }

    if (startDate >= endDate) {
      return res.status(400).json({ message: 'startDateTime must be before endDateTime' });
    }

    if (startDate <= new Date()) {
      return res.status(400).json({ message: 'Cannot book sessions in the past' });
    }

    // Get tutor's calendars
    const tutorCalendars = await getUserCalendars(new Types.ObjectId(tutorId), 'Tutor');
    if (tutorCalendars.length === 0) {
      return res.status(404).json({ message: 'No calendars found for tutor' });
    }

    // Use the first available tutor calendar (usually the main teaching calendar)
    const tutorCalendar = tutorCalendars[0];

    // Check for conflicts in tutor's calendar
    const conflictingEvent = await Event.findOne({
      calendarId: tutorCalendar._id,
      $or: [
        {
          startDateTime: { $lt: endDate },
          endDateTime: { $gt: startDate }
        }
      ]
    });

    if (conflictingEvent) {
      return res.status(409).json({ message: 'Time slot is not available in tutor\'s calendar' });
    }

    // Get student's calendars
    const studentCalendars = await getUserCalendars(actualStudentId, 'Student');
    if (studentCalendars.length === 0) {
      return res.status(404).json({ message: 'No calendars found for student' });
    }

    // Use the first available student calendar (usually the main learning calendar)
    const studentCalendar = studentCalendars[0];

    // Check for conflicts in student's calendar
    const studentConflictingEvent = await Event.findOne({
      calendarId: studentCalendar._id,
      $or: [
        {
          startDateTime: { $lt: endDate },
          endDateTime: { $gt: startDate }
        }
      ]
    });

    if (studentConflictingEvent) {
      return res.status(409).json({ message: 'Time slot is not available in student\'s calendar' });
    }

    // Consume lesson from subscription or mark as free trial
    const isFreeTrial = subscriptionCheck.isFreeTrial || false;
    const consumeResult = await consumeLesson(
      actualStudentId,
      new Types.ObjectId(tutorId),
      isFreeTrial
    );

    if (!consumeResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Failed to process lesson booking',
        error: consumeResult.message
      });
    }

    // Get student details for event description
    const studentProfile = await getProfile({ id: actualStudentId.toString() });
    const tutorProfile = await getProfile({ id: tutorId });

    if (!studentProfile || !tutorProfile) {
      return res.status(404).json({ message: 'Student or tutor profile not found' });
    }

    const studentName = `${studentProfile.firstname} ${studentProfile.lastname}`;
    const tutorName = `${tutorProfile.firstname} ${tutorProfile.lastname}`;

    // Create event in tutor's calendar
    const tutorEvent = new Event({
      calendarId: tutorCalendar._id,
      title: `${title} - ${studentName}`,
      description: `${description || ''}\nStudent: ${studentProfile.email}\nSubject: ${subject || 'General'}\nType: ${isFreeTrial ? 'Free Trial' : 'Paid Lesson'}`,
      startDateTime: startDate,
      endDateTime: endDate,
      allDay: false,
      status: 'confirmed',
      visibility: 'private',
      priority: 2
    });

    await tutorEvent.save();

    // Create event in student's calendar
    const studentEvent = new Event({
      calendarId: studentCalendar._id,
      title: `${title} - ${tutorName}`,
      description: `${description || ''}\nTutor: ${tutorProfile.email}\nSubject: ${subject || 'General'}\nType: ${isFreeTrial ? 'Free Trial' : 'Paid Lesson'}`,
      startDateTime: startDate,
      endDateTime: endDate,
      allDay: false,
      status: 'confirmed',
      visibility: 'private',
      priority: 2
    });

    await studentEvent.save();

    // Create lesson record for tracking
    let subscriptionId = null;
    if (!isFreeTrial) {
      const subscription = await Subscription.findOne({
        studentId: actualStudentId,
        tutorId: new Types.ObjectId(tutorId),
        status: 'active'
      });
      subscriptionId = subscription?._id || null;
    }

    const lesson = new LessonModel({
      tutorId: new Types.ObjectId(tutorId),
      studentId: actualStudentId,
      subscriptionId,
      scheduledTime: startDate,
      duration: Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60)), // Convert to minutes
      status: 'scheduled',
      isFreeTrial,
      notes: `Booked via enhanced booking: ${title}`
    });

    await lesson.save();

    // Send email notifications
    try {
      // Get full tutor and student profiles for email notifications
      const [tutorProfile, studentProfile] = await Promise.all([
        Tutor.findById(tutorId),
        Student.findById(actualStudentId)
      ]);

      if (tutorProfile && studentProfile) {
        // Send lesson confirmation to student
        await EmailNotificationService.sendLessonConfirmation(
          {
            id: (studentProfile._id as Types.ObjectId).toString(),
            firstname: studentProfile.firstname,
            lastname: studentProfile.lastname,
            email: studentProfile.email
          },
          {
            id: (tutorProfile._id as Types.ObjectId).toString(),
            firstname: tutorProfile.firstname,
            lastname: tutorProfile.lastname,
            email: tutorProfile.email
          },
          {
            id: (lesson._id as Types.ObjectId).toString(),
            title: title || 'Tutoring Session',
            scheduledTime: startDate,
            duration: lesson.duration,
            joinLink: `${process.env.FRONTEND_URL}/classroom/${lesson._id}`,
            status: lesson.status
          }
        );

        // Send lesson booking notification to tutor
        await EmailNotificationService.sendLessonBookingNotification(
          {
            id: (tutorProfile._id as Types.ObjectId).toString(),
            firstname: tutorProfile.firstname,
            lastname: tutorProfile.lastname,
            email: tutorProfile.email
          },
          {
            id: (studentProfile._id as Types.ObjectId).toString(),
            firstname: studentProfile.firstname,
            lastname: studentProfile.lastname,
            email: studentProfile.email
          },
          {
            id: (lesson._id as Types.ObjectId).toString(),
            title: title || 'Tutoring Session',
            scheduledTime: startDate,
            duration: lesson.duration,
            joinLink: `${process.env.FRONTEND_URL}/classroom/${lesson._id}`,
            status: lesson.status
          }
        );

        console.log(`Email notifications sent for lesson booking ${lesson._id}`);
      }
    } catch (emailError: any) {
      console.error('Error sending email notifications:', emailError);
      // Don't fail the booking if email sending fails
    }

    // Create classroom for the session
    let classroom = null;
    try {
      // Get full tutor and student profiles for classroom creation (reuse if already fetched)
      const [tutorProfile, studentProfile] = await Promise.all([
        Tutor.findById(tutorId),
        Student.findById(actualStudentId)
      ]);

      if (tutorProfile && studentProfile) {
        // Create classroom payload
        const classroomPayload = {
          chatGroup: {
            name: `${title} - ${tutorName} & ${studentName}`,
            desc: `Virtual classroom for ${title} session on ${startDate.toLocaleDateString()}`,
            maxUsers: 2
          },
          visibility: 'students-only' as const,
          expireAt: new Date(endDate.getTime() + 24 * 60 * 60 * 1000) // Expire 24 hours after session ends
        };

        // Create classroom using the hook
        classroom = await createClassroomHook(
          classroomPayload,
          tutorProfile,
          studentProfile
        );
        
        console.log(`Classroom created for session ${lesson._id}: ${classroom._id}`);
      }
    } catch (classroomError: any) {
      console.error('Error creating classroom:', classroomError);
      // Don't fail the booking if classroom creation fails
    }

    res.status(201).json({
      success: true,
      message: 'Session booked successfully in both calendars',
      data: {
        tutorEvent,
        studentEvent,
        lesson,
        classroom,
        isFreeTrial,
        subscriptionInfo: {
          remainingLessons: subscriptionCheck.remainingLessons,
          freeTrialLessonsRemaining: subscriptionCheck.freeTrialLessonsRemaining
        },
        consumeMessage: consumeResult.message
      }
    });
  } catch (error: any) {
    console.error('Enhanced booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to book session',
      error: error.message
    });
  }
};

/**
 * Get all bookings for a student with detailed time and date information
 */
export const getStudentBookings = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Allow students to get their own bookings, or admins to get any student's bookings
    let studentId: Types.ObjectId;

    if (req.params.studentId && req.user.role === 'admin') {
      // Admin requesting specific student's bookings
      if (!Types.ObjectId.isValid(req.params.studentId)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid student ID format'
        });
      }
      studentId = new Types.ObjectId(req.params.studentId);
    } else if (req.user.role === 'student') {
      // Student requesting their own bookings
      studentId = req.user._id as Types.ObjectId;
    } else {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Students can only view their own bookings.'
      });
    }

    // Query parameters for filtering
    const {
      status,
      startDate,
      endDate,
      page = 1,
      limit = 10,
      sortBy = 'scheduledTime',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query: any = { studentId };

    if (status && ['scheduled', 'completed', 'cancelled', 'no-show', 'confirmed'].includes(status as string)) {
      query.status = status;
    }

    if (startDate || endDate) {
      query.scheduledTime = {};
      if (startDate) {
        query.scheduledTime.$gte = new Date(startDate as string);
      }
      if (endDate) {
        query.scheduledTime.$lte = new Date(endDate as string);
      }
    }

    // Pagination
    const skip = (Number(page) - 1) * Number(limit);
    const sortDirection = sortOrder === 'asc' ? 1 : -1;

    // Get lessons with populated tutor and subscription information
    const [lessons, total] = await Promise.all([
      LessonModel.find(query)
        .populate('tutorId', 'firstname lastname email avatar teachingSubjects basePrice')
        .populate('subscriptionId', 'planType lessonsPerWeek monthlyPrice status')
        .sort({ [sortBy as string]: sortDirection })
        .skip(skip)
        .limit(Number(limit)),
      LessonModel.countDocuments(query)
    ]);

    // Format the response with detailed information including classroom data
    const formattedBookings = await Promise.all(lessons.map(async lesson => {
      // Find or create classroom for this lesson
      const classroom = await findOrCreateClassroomForLesson(
        lesson.tutorId as Types.ObjectId,
        lesson.studentId as Types.ObjectId,
        lesson.title || 'Tutoring Session',
        lesson.scheduledTime
      );

      return {
        id: lesson._id,
        title: lesson.title || 'Tutoring Session',
        scheduledTime: lesson.scheduledTime,
        duration: lesson.duration,
        status: lesson.status,
        isFreeTrial: lesson.isFreeTrial,
        notes: lesson.notes,
        confirmedAt: lesson.confirmedAt,
        cancelledAt: lesson.cancelledAt,
        cancellationReason: lesson.cancellationReason,
        createdAt: lesson.createdAt,
        tutor: lesson.tutorId ? {
          id: (lesson.tutorId as any)._id,
          name: `${(lesson.tutorId as any).firstname} ${(lesson.tutorId as any).lastname}`,
          email: (lesson.tutorId as any).email,
          avatar: (lesson.tutorId as any).avatar,
          subjects: (lesson.tutorId as any).teachingSubjects,
          basePrice: (lesson.tutorId as any).basePrice
        } : null,
        subscription: lesson.subscriptionId ? {
          id: (lesson.subscriptionId as any)._id,
          planType: (lesson.subscriptionId as any).planType,
          lessonsPerWeek: (lesson.subscriptionId as any).lessonsPerWeek,
          monthlyPrice: (lesson.subscriptionId as any).monthlyPrice,
          status: (lesson.subscriptionId as any).status
        } : null,
        classroom: classroom, // Include classroom information
        // Calculated fields
        endTime: new Date(lesson.scheduledTime.getTime() + lesson.duration * 60000),
        dayOfWeek: lesson.scheduledTime.toLocaleDateString('en-US', { weekday: 'long' }),
        formattedDate: lesson.scheduledTime.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        formattedTime: lesson.scheduledTime.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        })
      };
    }));

    res.json({
      success: true,
      data: {
        bookings: formattedBookings,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        },
        summary: {
          totalBookings: total,
          upcomingBookings: formattedBookings.filter(b =>
            new Date(b.scheduledTime) > new Date() &&
            ['scheduled', 'confirmed'].includes(b.status)
          ).length,
          completedBookings: formattedBookings.filter(b => b.status === 'completed').length,
          cancelledBookings: formattedBookings.filter(b => b.status === 'cancelled').length,
          freeTrialBookings: formattedBookings.filter(b => b.isFreeTrial).length
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching student bookings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch bookings',
      error: error.message
    });
  }
};

/**
 * Get all bookings for a tutor with detailed time and date information
 */
export const getTutorBookings = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Allow tutors to get their own bookings, or admins to get any tutor's bookings
    let tutorId: Types.ObjectId;

    if (req.params.tutorId && req.user.role === 'admin') {
      // Admin requesting specific tutor's bookings
      if (!Types.ObjectId.isValid(req.params.tutorId)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid tutor ID format'
        });
      }
      tutorId = new Types.ObjectId(req.params.tutorId);
    } else if (req.user.role === 'tutor') {
      // Tutor requesting their own bookings
      tutorId = req.user._id as Types.ObjectId;
    } else {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Tutors can only view their own bookings.'
      });
    }

    // Query parameters for filtering
    const {
      status,
      startDate,
      endDate,
      page = 1,
      limit = 10,
      sortBy = 'scheduledTime',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query: any = { tutorId };

    if (status && ['scheduled', 'completed', 'cancelled', 'no-show', 'confirmed'].includes(status as string)) {
      query.status = status;
    }

    if (startDate || endDate) {
      query.scheduledTime = {};
      if (startDate) {
        query.scheduledTime.$gte = new Date(startDate as string);
      }
      if (endDate) {
        query.scheduledTime.$lte = new Date(endDate as string);
      }
    }

    // Pagination
    const skip = (Number(page) - 1) * Number(limit);
    const sortDirection = sortOrder === 'asc' ? 1 : -1;

    // Get lessons with populated student and subscription information
    const [lessons, total] = await Promise.all([
      LessonModel.find(query)
        .populate('studentId', 'firstname lastname email avatar learningReasons skillsToImprove')
        .populate('subscriptionId', 'planType lessonsPerWeek monthlyPrice status')
        .sort({ [sortBy as string]: sortDirection })
        .skip(skip)
        .limit(Number(limit)),
      LessonModel.countDocuments(query)
    ]);

    // Format the response with detailed information including classroom data
    const formattedBookings = await Promise.all(lessons.map(async lesson => {
      // Find or create classroom for this lesson
      const classroom = await findOrCreateClassroomForLesson(
        lesson.tutorId as Types.ObjectId,
        lesson.studentId as Types.ObjectId,
        lesson.title || 'Tutoring Session',
        lesson.scheduledTime
      );

      return {
        id: lesson._id,
        title: lesson.title || 'Tutoring Session',
        scheduledTime: lesson.scheduledTime,
        duration: lesson.duration,
        status: lesson.status,
        isFreeTrial: lesson.isFreeTrial,
        notes: lesson.notes,
        confirmedAt: lesson.confirmedAt,
        cancelledAt: lesson.cancelledAt,
        cancellationReason: lesson.cancellationReason,
        createdAt: lesson.createdAt,
        student: lesson.studentId ? {
          id: (lesson.studentId as any)._id,
          name: `${(lesson.studentId as any).firstname} ${(lesson.studentId as any).lastname}`,
          email: (lesson.studentId as any).email,
          avatar: (lesson.studentId as any).avatar,
          learningReasons: (lesson.studentId as any).learningReasons,
          skillsToImprove: (lesson.studentId as any).skillsToImprove
        } : null,
        subscription: lesson.subscriptionId ? {
          id: (lesson.subscriptionId as any)._id,
          planType: (lesson.subscriptionId as any).planType,
          lessonsPerWeek: (lesson.subscriptionId as any).lessonsPerWeek,
          monthlyPrice: (lesson.subscriptionId as any).monthlyPrice,
          status: (lesson.subscriptionId as any).status
        } : null,
        classroom: classroom, // Include classroom information
        // Calculated fields
        endTime: new Date(lesson.scheduledTime.getTime() + lesson.duration * 60000),
        dayOfWeek: lesson.scheduledTime.toLocaleDateString('en-US', { weekday: 'long' }),
        formattedDate: lesson.scheduledTime.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        formattedTime: lesson.scheduledTime.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        })
      };
    }));

    res.json({
      success: true,
      data: {
        bookings: formattedBookings,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        },
        summary: {
          totalBookings: total,
          upcomingBookings: formattedBookings.filter(b =>
            new Date(b.scheduledTime) > new Date() &&
            ['scheduled', 'confirmed'].includes(b.status)
          ).length,
          completedBookings: formattedBookings.filter(b => b.status === 'completed').length,
          cancelledBookings: formattedBookings.filter(b => b.status === 'cancelled').length,
          freeTrialBookings: formattedBookings.filter(b => b.isFreeTrial).length
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching tutor bookings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch bookings',
      error: error.message
    });
  }
};

/**
 * Get upcoming bookings for the authenticated user (student or tutor)
 */
export const getUpcomingBookings = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const { limit = 5 } = req.query;
    const now = new Date();

    let query: any;
    let populateField: string;
    let populateSelect: string;

    if (req.user.role === 'student') {
      query = {
        studentId: req.user._id,
        scheduledTime: { $gt: now },
        status: { $in: ['scheduled', 'confirmed'] }
      };
      populateField = 'tutorId';
      populateSelect = 'firstname lastname email avatar teachingSubjects basePrice';
    } else if (req.user.role === 'tutor') {
      query = {
        tutorId: req.user._id,
        scheduledTime: { $gt: now },
        status: { $in: ['scheduled', 'confirmed'] }
      };
      populateField = 'studentId';
      populateSelect = 'firstname lastname email avatar learningReasons';
    } else {
      return res.status(403).json({
        success: false,
        message: 'Only students and tutors can view upcoming bookings'
      });
    }

    // Get upcoming lessons
    const lessons = await LessonModel.find(query)
      .populate(populateField, populateSelect)
      .populate('subscriptionId', 'planType lessonsPerWeek monthlyPrice status')
      .sort({ scheduledTime: 1 })
      .limit(Number(limit));

    // Format the response with classroom information
    const formattedBookings = await Promise.all(lessons.map(async lesson => {
      // Find or create classroom for this lesson
      const classroom = await findOrCreateClassroomForLesson(
        lesson.tutorId as Types.ObjectId,
        lesson.studentId as Types.ObjectId,
        lesson.title || 'Tutoring Session',
        lesson.scheduledTime
      );

      const baseBooking = {
        id: lesson._id,
        title: lesson.title || 'Tutoring Session',
        scheduledTime: lesson.scheduledTime,
        duration: lesson.duration,
        status: lesson.status,
        isFreeTrial: lesson.isFreeTrial,
        notes: lesson.notes,
        classroom: classroom, // Include classroom information
        endTime: new Date(lesson.scheduledTime.getTime() + lesson.duration * 60000),
        dayOfWeek: lesson.scheduledTime.toLocaleDateString('en-US', { weekday: 'long' }),
        formattedDate: lesson.scheduledTime.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        formattedTime: lesson.scheduledTime.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        }),
        timeUntilSession: getTimeUntilSession(lesson.scheduledTime)
      };

      if (req.user!.role === 'student') {
        return {
          ...baseBooking,
          tutor: lesson.tutorId ? {
            id: (lesson.tutorId as any)._id,
            name: `${(lesson.tutorId as any).firstname} ${(lesson.tutorId as any).lastname}`,
            email: (lesson.tutorId as any).email,
            avatar: (lesson.tutorId as any).avatar,
            subjects: (lesson.tutorId as any).teachingSubjects,
            basePrice: (lesson.tutorId as any).basePrice
          } : null
        };
      } else {
        return {
          ...baseBooking,
          student: lesson.studentId ? {
            id: (lesson.studentId as any)._id,
            name: `${(lesson.studentId as any).firstname} ${(lesson.studentId as any).lastname}`,
            email: (lesson.studentId as any).email,
            avatar: (lesson.studentId as any).avatar,
            learningReasons: (lesson.studentId as any).learningReasons
          } : null
        };
      }
    }));

    res.json({
      success: true,
      data: {
        upcomingBookings: formattedBookings,
        count: formattedBookings.length,
        userRole: req.user.role
      }
    });
  } catch (error: any) {
    console.error('Error fetching upcoming bookings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch upcoming bookings',
      error: error.message
    });
  }
};

/**
 * Helper function to find or create classroom for a lesson using createClassroomHook
 */
async function findOrCreateClassroomForLesson(
  tutorId: Types.ObjectId,
  studentId: Types.ObjectId,
  lessonTitle?: string,
  lessonDate?: Date
): Promise<any> {
  try {
    // First, try to find existing classroom
    const existingClassroom = await Classroom.findOne({
      tutor: tutorId,
      students: studentId.toString()
    }).populate('tutor', 'firstname lastname email');

    if (existingClassroom) {
      return {
        id: existingClassroom._id,
        chatGroup: existingClassroom.chatGroup,
        visibility: existingClassroom.visibility,
        expireAt: existingClassroom.expireAt,
        joinLink: `${process.env.FRONTEND_URL}/classroom/${existingClassroom._id}`
      };
    }

    // If no classroom exists, create one using createClassroomHook
    const [tutorProfile, studentProfile] = await Promise.all([
      Tutor.findById(tutorId),
      Student.findById(studentId)
    ]);

    if (tutorProfile && studentProfile) {
      const tutorName = `${tutorProfile.firstname} ${tutorProfile.lastname}`;
      const studentName = `${studentProfile.firstname} ${studentProfile.lastname}`;
      const sessionDate = lessonDate ? lessonDate.toLocaleDateString() : 'upcoming session';

      // Create classroom payload
      const classroomPayload = {
        chatGroup: {
          name: `${lessonTitle || 'Tutoring Session'} - ${tutorName} & ${studentName}`,
          desc: `Virtual classroom for ${lessonTitle || 'tutoring session'} on ${sessionDate}`,
          maxUsers: 2
        },
        visibility: 'students-only' as const,
        expireAt: lessonDate ? new Date(lessonDate.getTime() + 24 * 60 * 60 * 1000) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Expire 24 hours after lesson or 7 days from now
      };

      // Create classroom using the hook
      const newClassroom = await createClassroomHook(
        classroomPayload,
        tutorProfile,
        studentProfile
      );

      console.log(`New classroom created for lesson: ${newClassroom._id}`);

      return {
        id: newClassroom._id,
        chatGroup: newClassroom.chatGroup,
        visibility: newClassroom.visibility,
        expireAt: newClassroom.expireAt,
        joinLink: `${process.env.FRONTEND_URL}/classroom/${newClassroom._id}`
      };
    }

    return null;
  } catch (error) {
    console.error('Error finding or creating classroom for lesson:', error);
    return null;
  }
}

/**
 * Get tutor availability for students to view
 * Shows available time slots based on tutor's calendar
 */
export const getTutorAvailability = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Only students should be able to view tutor availability
    if (req.user.role !== 'student') {
      return res.status(403).json({
        success: false,
        message: 'Only students can view tutor availability'
      });
    }

    const { tutorId } = req.params;
    const {
      startDate,
      endDate,
      duration = 60, // Default session duration in minutes
      timezone = 'UTC'
    } = req.query;

    // Validate tutor ID
    if (!Types.ObjectId.isValid(tutorId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid tutor ID format'
      });
    }

    // Validate dates
    const start = startDate ? new Date(startDate as string) : new Date();
    const end = endDate ? new Date(endDate as string) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // Default to 7 days from now

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid date format'
      });
    }

    if (start >= end) {
      return res.status(400).json({
        success: false,
        message: 'Start date must be before end date'
      });
    }

    // Ensure we don't show past availability
    const now = new Date();
    const searchStart = start > now ? start : now;

    // Get tutor's shared calendars
    const tutorCalendars = await getUserCalendars(new Types.ObjectId(tutorId), 'Tutor');
    const sharedCalendars = tutorCalendars.filter(calendar => calendar.isShared);

    if (sharedCalendars.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No shared calendars found for this tutor'
      });
    }

    // Get all events from tutor's shared calendars within the date range
    const calendarIds = sharedCalendars.map(calendar => calendar._id);
    const bookedEvents = await Event.find({
      calendarId: { $in: calendarIds },
      startDateTime: { $lt: end },
      endDateTime: { $gt: searchStart },
      status: { $in: ['confirmed', 'tentative'] }
    }).sort({ startDateTime: 1 });

    // Get tutor profile for additional info
    const tutorProfile = await getProfile({ id: tutorId, role: 'tutor' });
    if (!tutorProfile) {
      return res.status(404).json({
        success: false,
        message: 'Tutor not found'
      });
    }

    // Cast to ITutor to access tutor-specific properties
    const tutor = tutorProfile as any; // We know it's a tutor from the role parameter

    // Generate available time slots
    const availableSlots = generateAvailableTimeSlots(
      searchStart,
      end,
      bookedEvents,
      Number(duration),
      timezone as string
    );

    // Group availability by date for easier frontend consumption
    const availabilityByDate = groupAvailabilityByDate(availableSlots);

    res.json({
      success: true,
      data: {
        tutor: {
          id: tutorId,
          name: `${tutorProfile.firstname} ${tutorProfile.lastname}`,
          email: tutorProfile.email,
          image: tutorProfile.image,
          subjects: tutor.teachingSubjects || [],
          basePrice: tutor.basePrice
        },
        searchPeriod: {
          startDate: searchStart,
          endDate: end,
          duration: Number(duration),
          timezone
        },
        calendars: sharedCalendars.map(calendar => ({
          id: calendar._id,
          name: calendar.name,
          description: calendar.description,
          color: calendar.color
        })),
        bookedSlots: bookedEvents.map(event => ({
          id: event._id,
          title: event.title,
          startDateTime: event.startDateTime,
          endDateTime: event.endDateTime,
          status: event.status
        })),
        availableSlots,
        availabilityByDate,
        summary: {
          totalAvailableSlots: availableSlots.length,
          daysWithAvailability: Object.keys(availabilityByDate).length,
          nextAvailableSlot: availableSlots.length > 0 ? availableSlots[0] : null
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching tutor availability:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch tutor availability',
      error: error.message
    });
  }
};

/**
 * Helper function to generate available time slots
 */
function generateAvailableTimeSlots(
  startDate: Date,
  endDate: Date,
  bookedEvents: any[],
  durationMinutes: number,
  timezone: string
): any[] {
  const availableSlots: any[] = [];
  const slotDuration = durationMinutes * 60 * 1000; // Convert to milliseconds

  // Define working hours (9 AM to 6 PM by default)
  const workingHours = {
    start: 9, // 9 AM
    end: 18   // 6 PM
  };

  // Iterate through each day in the date range
  const currentDate = new Date(startDate);
  while (currentDate < endDate) {
    // Skip weekends (optional - can be made configurable)
    const dayOfWeek = currentDate.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) { // Sunday = 0, Saturday = 6
      currentDate.setDate(currentDate.getDate() + 1);
      continue;
    }

    // Set working hours for the current day
    const dayStart = new Date(currentDate);
    dayStart.setHours(workingHours.start, 0, 0, 0);

    const dayEnd = new Date(currentDate);
    dayEnd.setHours(workingHours.end, 0, 0, 0);

    // Generate time slots for the day
    const currentSlot = new Date(dayStart);
    while (currentSlot.getTime() + slotDuration <= dayEnd.getTime()) {
      const slotEnd = new Date(currentSlot.getTime() + slotDuration);

      // Check if this slot conflicts with any booked events
      const hasConflict = bookedEvents.some(event => {
        const eventStart = new Date(event.startDateTime);
        const eventEnd = new Date(event.endDateTime);

        return (
          (currentSlot >= eventStart && currentSlot < eventEnd) ||
          (slotEnd > eventStart && slotEnd <= eventEnd) ||
          (currentSlot <= eventStart && slotEnd >= eventEnd)
        );
      });

      // Only add slot if there's no conflict and it's in the future
      if (!hasConflict && currentSlot > new Date()) {
        availableSlots.push({
          startDateTime: new Date(currentSlot),
          endDateTime: new Date(slotEnd),
          duration: durationMinutes,
          dayOfWeek: currentSlot.toLocaleDateString('en-US', { weekday: 'long' }),
          formattedDate: currentSlot.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }),
          formattedTime: currentSlot.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          }),
          timezone
        });
      }

      // Move to next slot (30-minute intervals)
      currentSlot.setMinutes(currentSlot.getMinutes() + 30);
    }

    // Move to next day
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return availableSlots;
}

/**
 * Helper function to group availability by date
 */
function groupAvailabilityByDate(availableSlots: any[]): Record<string, any[]> {
  const grouped: Record<string, any[]> = {};

  availableSlots.forEach(slot => {
    const dateKey = slot.startDateTime.toISOString().split('T')[0]; // YYYY-MM-DD format
    if (!grouped[dateKey]) {
      grouped[dateKey] = [];
    }
    grouped[dateKey].push(slot);
  });

  return grouped;
}

/**
 * Reschedule a booking/lesson
 */
export const rescheduleBooking = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const { lessonId } = req.params;
    const { newStartDateTime, newEndDateTime, reason } = req.body;

    // Validate required fields
    if (!newStartDateTime || !newEndDateTime) {
      return res.status(400).json({
        success: false,
        message: 'newStartDateTime and newEndDateTime are required'
      });
    }

    // Validate lesson ID
    if (!Types.ObjectId.isValid(lessonId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid lesson ID format'
      });
    }

    // Find the lesson
    const lesson = await LessonModel.findById(lessonId)
      .populate('tutorId', 'firstname lastname email')
      .populate('studentId', 'firstname lastname email');

    if (!lesson) {
      return res.status(404).json({
        success: false,
        message: 'Lesson not found'
      });
    }

    // Check if user has permission to reschedule
    const isStudent = req.user.role === 'student' && (lesson.studentId as any)._id.toString() === (req.user as any)._id.toString();
    const isTutor = req.user.role === 'tutor' && (lesson.tutorId as any)._id.toString() === (req.user as any)._id.toString();
    const isAdmin = req.user.role === 'admin';

    if (!isStudent && !isTutor && !isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to reschedule this lesson'
      });
    }

    // Check if lesson can be rescheduled (only scheduled or confirmed lessons)
    if (!['scheduled', 'confirmed'].includes(lesson.status)) {
      return res.status(400).json({
        success: false,
        message: `Cannot reschedule lesson with status: ${lesson.status}`
      });
    }

    // Validate new dates
    const newStartDate = new Date(newStartDateTime);
    const newEndDate = new Date(newEndDateTime);

    if (isNaN(newStartDate.getTime()) || isNaN(newEndDate.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid date format'
      });
    }

    if (newStartDate >= newEndDate) {
      return res.status(400).json({
        success: false,
        message: 'newStartDateTime must be before newEndDateTime'
      });
    }

    if (newStartDate <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'Cannot reschedule to a time in the past'
      });
    }

    // Check for conflicts in both tutor and student calendars
    const tutorCalendars = await getUserCalendars(lesson.tutorId._id as Types.ObjectId, 'Tutor');
    const studentCalendars = await getUserCalendars(lesson.studentId._id as Types.ObjectId, 'Student');

    if (tutorCalendars.length === 0 || studentCalendars.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Calendars not found for tutor or student'
      });
    }

    // Check for conflicts in tutor's calendar (excluding current lesson events)
    const tutorConflict = await Event.findOne({
      calendarId: { $in: tutorCalendars.map(c => c._id) },
      $or: [
        {
          startDateTime: { $lt: newEndDate },
          endDateTime: { $gt: newStartDate }
        }
      ],
      // Exclude events that might be related to this lesson
      title: { $not: new RegExp((lesson.studentId as any).firstname, 'i') }
    });

    if (tutorConflict) {
      return res.status(409).json({
        success: false,
        message: 'New time slot conflicts with tutor\'s existing schedule'
      });
    }

    // Check for conflicts in student's calendar (excluding current lesson events)
    const studentConflict = await Event.findOne({
      calendarId: { $in: studentCalendars.map(c => c._id) },
      $or: [
        {
          startDateTime: { $lt: newEndDate },
          endDateTime: { $gt: newStartDate }
        }
      ],
      // Exclude events that might be related to this lesson
      title: { $not: new RegExp((lesson.tutorId as any).firstname, 'i') }
    });

    if (studentConflict) {
      return res.status(409).json({
        success: false,
        message: 'New time slot conflicts with student\'s existing schedule'
      });
    }

    // Store old scheduled time for notifications
    const oldScheduledTime = lesson.scheduledTime;

    // Update lesson with new time
    lesson.scheduledTime = newStartDate;
    lesson.duration = Math.round((newEndDate.getTime() - newStartDate.getTime()) / (1000 * 60));
    lesson.notes = `${lesson.notes || ''}\nRescheduled from ${oldScheduledTime.toISOString()} by ${req.user.role}${reason ? `. Reason: ${reason}` : ''}`;

    await lesson.save();

    // Update corresponding events in calendars
    const studentName = `${(lesson.studentId as any).firstname} ${(lesson.studentId as any).lastname}`;
    const tutorName = `${(lesson.tutorId as any).firstname} ${(lesson.tutorId as any).lastname}`;

    // Update tutor's calendar event
    await Event.updateMany(
      {
        calendarId: { $in: tutorCalendars.map(c => c._id) },
        startDateTime: oldScheduledTime,
        title: { $regex: (lesson.studentId as any).firstname, $options: 'i' }
      },
      {
        $set: {
          startDateTime: newStartDate,
          endDateTime: newEndDate,
          updatedAt: new Date()
        }
      }
    );

    // Update student's calendar event
    await Event.updateMany(
      {
        calendarId: { $in: studentCalendars.map(c => c._id) },
        startDateTime: oldScheduledTime,
        title: { $regex: (lesson.tutorId as any).firstname, $options: 'i' }
      },
      {
        $set: {
          startDateTime: newStartDate,
          endDateTime: newEndDate,
          updatedAt: new Date()
        }
      }
    );

    // Send email notifications
    try {
      // Send notification to student
      await EmailNotificationService.sendStudentLessonStatus(
        lesson.studentId as any,
        lesson.tutorId as any,
        'rescheduled',
        newStartDate
      );

      // Send notification to tutor
      await EmailNotificationService.sendLessonCancelledByStudent(
        lesson.tutorId as any,
        lesson.studentId as any,
        'rescheduled',
        newStartDate
      );

      console.log(`Reschedule notifications sent for lesson ${lesson._id}`);
    } catch (emailError: any) {
      console.error('Error sending reschedule notifications:', emailError);
      // Don't fail the reschedule if email sending fails
    }

    res.json({
      success: true,
      message: 'Lesson rescheduled successfully',
      data: {
        lesson: {
          id: lesson._id,
          oldScheduledTime,
          newScheduledTime: newStartDate,
          duration: lesson.duration,
          status: lesson.status,
          tutor: {
            id: (lesson.tutorId as any)._id,
            name: tutorName,
            email: (lesson.tutorId as any).email
          },
          student: {
            id: (lesson.studentId as any)._id,
            name: studentName,
            email: (lesson.studentId as any).email
          }
        }
      }
    });
  } catch (error: any) {
    console.error('Reschedule booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reschedule lesson',
      error: error.message
    });
  }
};

/**
 * Cancel a booking/lesson
 */
export const cancelBooking = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const { lessonId } = req.params;
    const { reason } = req.body;

    // Validate required fields
    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Cancellation reason is required'
      });
    }

    // Validate lesson ID
    if (!Types.ObjectId.isValid(lessonId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid lesson ID format'
      });
    }

    // Find the lesson
    const lesson = await LessonModel.findById(lessonId)
      .populate('tutorId', 'firstname lastname email')
      .populate('studentId', 'firstname lastname email')
      .populate('subscriptionId');

    if (!lesson) {
      return res.status(404).json({
        success: false,
        message: 'Lesson not found'
      });
    }

    // Check if user has permission to cancel
    const isStudent = req.user.role === 'student' && (lesson.studentId as any)._id.toString() === (req.user as any)._id.toString();
    const isTutor = req.user.role === 'tutor' && (lesson.tutorId as any)._id.toString() === (req.user as any)._id.toString();
    const isAdmin = req.user.role === 'admin';

    if (!isStudent && !isTutor && !isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to cancel this lesson'
      });
    }

    // Check if lesson can be cancelled (only scheduled or confirmed lessons)
    if (!['scheduled', 'confirmed'].includes(lesson.status)) {
      return res.status(400).json({
        success: false,
        message: `Cannot cancel lesson with status: ${lesson.status}`
      });
    }

    // Check if cancellation is within allowed timeframe (e.g., at least 2 hours before)
    const now = new Date();
    const timeDiff = lesson.scheduledTime.getTime() - now.getTime();
    const hoursUntilLesson = timeDiff / (1000 * 60 * 60);

    if (hoursUntilLesson < 2 && req.user.role !== 'admin') {
      return res.status(400).json({
        success: false,
        message: 'Lessons can only be cancelled at least 2 hours in advance'
      });
    }

    // Update lesson status
    lesson.status = 'cancelled';
    lesson.cancelledAt = new Date();
    lesson.cancellationReason = reason;
    lesson.notes = `${lesson.notes || ''}\nCancelled by ${req.user.role} on ${new Date().toISOString()}. Reason: ${reason}`;

    await lesson.save();

    // Update corresponding events in calendars to cancelled status
    const tutorCalendars = await getUserCalendars(lesson.tutorId._id as Types.ObjectId, 'Tutor');
    const studentCalendars = await getUserCalendars(lesson.studentId._id as Types.ObjectId, 'Student');

    // Cancel tutor's calendar event
    if (tutorCalendars.length > 0) {
      await Event.updateMany(
        {
          calendarId: { $in: tutorCalendars.map(c => c._id) },
          startDateTime: lesson.scheduledTime,
          title: { $regex: (lesson.studentId as any).firstname, $options: 'i' }
        },
        {
          $set: {
            status: 'cancelled',
            updatedAt: new Date()
          }
        }
      );
    }

    // Cancel student's calendar event
    if (studentCalendars.length > 0) {
      await Event.updateMany(
        {
          calendarId: { $in: studentCalendars.map(c => c._id) },
          startDateTime: lesson.scheduledTime,
          title: { $regex: (lesson.tutorId as any).firstname, $options: 'i' }
        },
        {
          $set: {
            status: 'cancelled',
            updatedAt: new Date()
          }
        }
      );
    }

    // Handle lesson refund/credit back to subscription if applicable
    let refundInfo = null;
    if (!lesson.isFreeTrial && lesson.subscriptionId) {
      try {
        // Credit the lesson back to the subscription
        const subscription = lesson.subscriptionId as any;
        if (subscription && subscription.status === 'active') {
          subscription.remainingLessons += 1;
          await subscription.save();

          refundInfo = {
            creditedBack: true,
            newRemainingLessons: subscription.remainingLessons
          };
        }
      } catch (refundError) {
        console.error('Error crediting lesson back to subscription:', refundError);
        // Don't fail the cancellation if refund fails
      }
    }

    // Send email notifications
    try {
      // Send notification to student
      await EmailNotificationService.sendStudentLessonStatus(
        lesson.studentId as any,
        lesson.tutorId as any,
        'cancelled'
      );

      // Send notification to tutor
      await EmailNotificationService.sendLessonCancelledByStudent(
        lesson.tutorId as any,
        lesson.studentId as any,
        'cancelled'
      );

      console.log(`Cancellation notifications sent for lesson ${lesson._id}`);
    } catch (emailError: any) {
      console.error('Error sending cancellation notifications:', emailError);
      // Don't fail the cancellation if email sending fails
    }

    const studentName = `${(lesson.studentId as any).firstname} ${(lesson.studentId as any).lastname}`;
    const tutorName = `${(lesson.tutorId as any).firstname} ${(lesson.tutorId as any).lastname}`;

    res.json({
      success: true,
      message: 'Lesson cancelled successfully',
      data: {
        lesson: {
          id: lesson._id,
          scheduledTime: lesson.scheduledTime,
          status: lesson.status,
          cancelledAt: lesson.cancelledAt,
          cancellationReason: lesson.cancellationReason,
          tutor: {
            id: (lesson.tutorId as any)._id,
            name: tutorName,
            email: (lesson.tutorId as any).email
          },
          student: {
            id: (lesson.studentId as any)._id,
            name: studentName,
            email: (lesson.studentId as any).email
          }
        },
        refund: refundInfo
      }
    });
  } catch (error: any) {
    console.error('Cancel booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel lesson',
      error: error.message
    });
  }
};

/**
 * Get tutor's booked time slots for students to view (date and time only)
 * This helps students see when tutors are unavailable
 */
export const getTutorBookedSlots = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Only students should be able to view tutor booked slots
    if (req.user.role !== 'student') {
      return res.status(403).json({
        success: false,
        message: 'Only students can view tutor booked slots'
      });
    }

    const { tutorId } = req.params;
    const {
      startDate,
      endDate,
      timezone = 'UTC'
    } = req.query;

    // Validate tutor ID
    if (!Types.ObjectId.isValid(tutorId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid tutor ID format'
      });
    }

    // Validate dates
    const start = startDate ? new Date(startDate as string) : new Date();
    const end = endDate ? new Date(endDate as string) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // Default to 30 days from now

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid date format'
      });
    }

    if (start >= end) {
      return res.status(400).json({
        success: false,
        message: 'Start date must be before end date'
      });
    }

    // Ensure we don't show past bookings
    const now = new Date();
    const searchStart = start > now ? start : now;

    // Get tutor profile for validation
    const tutorProfile = await getProfile({ id: tutorId, role: 'tutor' });
    if (!tutorProfile) {
      return res.status(404).json({
        success: false,
        message: 'Tutor not found'
      });
    }

    // Get tutor's booked lessons within the date range
    const bookedLessons = await LessonModel.find({
      tutorId: new Types.ObjectId(tutorId),
      scheduledTime: {
        $gte: searchStart,
        $lte: end
      },
      status: { $in: ['scheduled', 'confirmed', 'completed'] } // Include all non-cancelled lessons
    }).select('scheduledTime duration status').sort({ scheduledTime: 1 });

    // Get tutor's calendar events (for additional context)
    const tutorCalendars = await getUserCalendars(new Types.ObjectId(tutorId), 'Tutor');
    const sharedCalendars = tutorCalendars.filter(calendar => calendar.isShared);

    let calendarEvents: any[] = [];
    if (sharedCalendars.length > 0) {
      const calendarIds = sharedCalendars.map(calendar => calendar._id);
      calendarEvents = await Event.find({
        calendarId: { $in: calendarIds },
        startDateTime: { $gte: searchStart, $lte: end },
        status: { $in: ['confirmed', 'tentative'] }
      }).select('startDateTime endDateTime title status').sort({ startDateTime: 1 });
    }

    // Format booked slots with only date and time information
    const bookedSlots = bookedLessons.map(lesson => {
      const endTime = new Date(lesson.scheduledTime.getTime() + lesson.duration * 60000);

      return {
        startDateTime: lesson.scheduledTime,
        endDateTime: endTime,
        duration: lesson.duration,
        status: lesson.status,
        type: 'lesson',
        dayOfWeek: lesson.scheduledTime.toLocaleDateString('en-US', { weekday: 'long' }),
        formattedDate: lesson.scheduledTime.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        formattedStartTime: lesson.scheduledTime.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        }),
        formattedEndTime: endTime.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        }),
        timezone
      };
    });

    // Format calendar events (if any shared calendars exist)
    const calendarSlots = calendarEvents.map(event => ({
      startDateTime: event.startDateTime,
      endDateTime: event.endDateTime,
      duration: Math.round((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60)),
      status: event.status,
      type: 'calendar_event',
      title: event.title,
      dayOfWeek: event.startDateTime.toLocaleDateString('en-US', { weekday: 'long' }),
      formattedDate: event.startDateTime.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      formattedStartTime: event.startDateTime.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }),
      formattedEndTime: event.endDateTime.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }),
      timezone
    }));

    // Combine and sort all booked slots
    const allBookedSlots = [...bookedSlots, ...calendarSlots].sort((a, b) =>
      a.startDateTime.getTime() - b.startDateTime.getTime()
    );

    // Group by date for easier frontend consumption
    const slotsByDate = allBookedSlots.reduce((acc: Record<string, any[]>, slot) => {
      const dateKey = slot.startDateTime.toISOString().split('T')[0]; // YYYY-MM-DD format
      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push(slot);
      return acc;
    }, {});

    res.json({
      success: true,
      data: {
        tutor: {
          id: tutorId,
          name: `${tutorProfile.firstname} ${tutorProfile.lastname}`,
          email: tutorProfile.email
        },
        searchPeriod: {
          startDate: searchStart,
          endDate: end,
          timezone
        },
        bookedSlots: allBookedSlots,
        slotsByDate,
        summary: {
          totalBookedSlots: allBookedSlots.length,
          lessonSlots: bookedSlots.length,
          calendarSlots: calendarSlots.length,
          daysWithBookings: Object.keys(slotsByDate).length,
          nextBookedSlot: allBookedSlots.length > 0 ? allBookedSlots[0] : null
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching tutor booked slots:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch tutor booked slots',
      error: error.message
    });
  }
};

/**
 * Helper function to calculate time until session
 */
function getTimeUntilSession(scheduledTime: Date): string {
  const now = new Date();
  const timeDiff = scheduledTime.getTime() - now.getTime();

  if (timeDiff <= 0) return 'Session has started';

  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) {
    return `${days} day${days > 1 ? 's' : ''}, ${hours} hour${hours !== 1 ? 's' : ''}`;
  } else if (hours > 0) {
    return `${hours} hour${hours > 1 ? 's' : ''}, ${minutes} minute${minutes !== 1 ? 's' : ''}`;
  } else {
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
}
