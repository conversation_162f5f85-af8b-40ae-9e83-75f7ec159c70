import { Request, Response } from 'express';
import { Calendar } from '../models/calender';
import { createDefaultCalendar, getUserCalendars, getSharedCalendars } from '../services/calendarService';

// Create a calendar
export const createCalendar = async (req: Request, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { name, description, color, isShared } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Calendar name is required' });
    }

    // Determine owner type based on user role
    const ownerType = req.user.role === 'tutor' ? 'Tutor' : 'Student';

    const calendar = await createDefaultCalendar({
      ownerUserId: req.user._id,
      ownerType,
      name,
      description,
      color,
      isShared: isShared ?? (req.user.role === 'tutor'), // <PERSON><PERSON> share by default
    });

    res.status(201).json({
      success: true,
      message: 'Calendar created successfully',
      data: calendar
    });
  } catch (error: any) {
    console.error('Create calendar error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create calendar',
      error: error.message
    });
  }
};

// Get all calendars of a tutor (for learner or tutor)
export const getTutorCalendars = async (req: Request, res: Response): Promise<any> => {
  try {
    const tutorId = req.params.tutorId;

    if (!tutorId) {
      return res.status(400).json({ message: 'Tutor ID is required' });
    }

    // Get shared calendars for this tutor
    const calendars = await getSharedCalendars(tutorId as any);

    res.json({
      success: true,
      data: calendars
    });
  } catch (error: any) {
    console.error('Get tutor calendars error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch tutor calendars',
      error: error.message
    });
  }
};

// Update calendar (only owner can update)
export const updateCalendar = async (req: Request, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const calendarId = req.params.id;
    const userId = req.user._id;

    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      return res.status(404).json({ message: 'Calendar not found' });
    }

    if (calendar.ownerUserId.toString() !== userId.toString()) {
      return res.status(403).json({ message: 'Not authorized to update this calendar' });
    }

    const { name, description, color, isShared } = req.body;

    calendar.name = name ?? calendar.name;
    calendar.description = description ?? calendar.description;
    calendar.color = color ?? calendar.color;
    if (typeof isShared === 'boolean') calendar.isShared = isShared;
    calendar.updatedAt = new Date();

    await calendar.save();

    res.json({
      success: true,
      message: 'Calendar updated successfully',
      data: calendar
    });
  } catch (error: any) {
    console.error('Update calendar error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update calendar',
      error: error.message
    });
  }
};

// Delete calendar (only owner can delete)
export const deleteCalendar = async (req: Request, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const calendarId = req.params.id;
    const userId = req.user._id;

    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      return res.status(404).json({ message: 'Calendar not found' });
    }

    if (calendar.ownerUserId.toString() !== userId.toString()) {
      return res.status(403).json({ message: 'Not authorized to delete this calendar' });
    }

    await calendar.deleteOne();

    res.json({
      success: true,
      message: 'Calendar deleted successfully'
    });
  } catch (error: any) {
    console.error('Delete calendar error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete calendar',
      error: error.message
    });
  }
};

// Get user's own calendars
export const getUserOwnCalendars = async (req: Request, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const userType = req.user.role === 'tutor' ? 'Tutor' : 'Student';
    const calendars = await getUserCalendars(req.user._id, userType);

    res.json({
      success: true,
      data: calendars
    });
  } catch (error: any) {
    console.error('Get user calendars error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user calendars',
      error: error.message
    });
  }
};
