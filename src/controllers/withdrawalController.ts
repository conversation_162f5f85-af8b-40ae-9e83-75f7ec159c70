import { Request, Response } from 'express';
import { Types } from 'mongoose';
import WithdrawalRequest from '../models/withdrawalRequest.model';
import RefundRequest from '../models/refundRequest.model';
import Tutor from '../models/tutor';
import Student from '../models/student';
import Subscription from '../models/subscription.model';
import LessonModel from '../models/lesson.model';
import TransactionModel from '../models/transaction.model';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';
import { logError } from '../utils/logger';

/**
 * Create a withdrawal request (Tutor only)
 */
export const createWithdrawalRequest = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.user.role !== 'tutor') {
      return res.status(403).json({ message: 'Only tutors can request withdrawals' });
    }

    const { amount, email } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({ message: 'Valid withdrawal amount is required' });
    }

    const tutor = await Tutor.findById(req.user._id);
    if (!tutor) {
      return res.status(404).json({ message: 'Tutor not found' });
    }

    const requestedAmountInCents = Math.round(amount * 100);

    if (!tutor.canWithdraw(requestedAmountInCents)) {
      return res.status(400).json({
        message: 'Insufficient balance for withdrawal',
        availableBalance: tutor.availableBalance / 100,
        requestedAmount: amount
      });
    }

    const pendingRequest = await WithdrawalRequest.findOne({
      tutorId: req.user._id,
      status: 'pending'
    });

    if (pendingRequest) {
      return res.status(409).json({
        message: 'You already have a pending withdrawal request. Please wait for it to be processed.'
      });
    }

    const withdrawalRequest = new WithdrawalRequest({
      tutorId: req.user._id,
      requestedAmount: amount,
      amount: requestedAmountInCents,
      status: 'pending'
    });

    await withdrawalRequest.save();
    await withdrawalRequest.populate('tutorId', 'email');

    res.status(201).json({
      success: true,
      message: 'Withdrawal request submitted successfully',
      data: {
        email: (withdrawalRequest.tutorId as any).email,
        amount: withdrawalRequest.requestedAmount
      }
    });
  } catch (error: any) {
    console.error('Create withdrawal request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create withdrawal request',
      error: error.message
    });
  }
};


/**
 * Get tutor's withdrawal requests
 */
export const getTutorWithdrawalRequests = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.user.role !== 'tutor') {
      return res.status(403).json({ message: 'Only tutors can view their withdrawal requests' });
    }

    const { page = 1, limit = 10, status } = req.query;

    const query: any = { tutorId: req.user._id };
    if (status) {
      query.status = status;
    }

    const withdrawalRequests = await WithdrawalRequest.find(query)
      .populate('processedBy', 'firstname lastname')
      .populate('transactionId')
      .sort({ createdAt: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    const total = await WithdrawalRequest.countDocuments(query);

    res.json({
      success: true,
      data: withdrawalRequests,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error: any) {
    console.error('Get tutor withdrawal requests error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch withdrawal requests',
      error: error.message
    });
  }
};

/**
 * Cancel a withdrawal request (Tutor only)
 */
export const cancelWithdrawalRequest = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.user.role !== 'tutor') {
      return res.status(403).json({ message: 'Only tutors can cancel their withdrawal requests' });
    }

    const { requestId } = req.params;

    if (!Types.ObjectId.isValid(requestId)) {
      return res.status(400).json({ message: 'Invalid request ID format' });
    }

    const withdrawalRequest = await WithdrawalRequest.findOne({
      _id: requestId,
      tutorId: req.user._id
    });

    if (!withdrawalRequest) {
      return res.status(404).json({ message: 'Withdrawal request not found' });
    }

    if (!withdrawalRequest.canBeCancelled()) {
      return res.status(400).json({ 
        message: 'Cannot cancel withdrawal request in current status',
        currentStatus: withdrawalRequest.status
      });
    }

    withdrawalRequest.status = 'cancelled';
    await withdrawalRequest.save();

    res.json({
      success: true,
      message: 'Withdrawal request cancelled successfully',
      data: withdrawalRequest
    });
  } catch (error: any) {
    console.error('Cancel withdrawal request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel withdrawal request',
      error: error.message
    });
  }
};

/**
 * Get withdrawal request by ID (Tutor only - their own requests)
 */
export const getWithdrawalRequestById = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.user.role !== 'tutor') {
      return res.status(403).json({ message: 'Only tutors can view their withdrawal requests' });
    }

    const { requestId } = req.params;

    if (!Types.ObjectId.isValid(requestId)) {
      return res.status(400).json({ message: 'Invalid request ID format' });
    }

    const withdrawalRequest = await WithdrawalRequest.findOne({
      _id: requestId,
      tutorId: req.user._id
    })
      .populate('tutorId', 'firstname lastname email availableBalance')
      .populate('processedBy', 'firstname lastname')
      .populate('transactionId');

    if (!withdrawalRequest) {
      return res.status(404).json({ message: 'Withdrawal request not found' });
    }

    res.json({
      success: true,
      data: withdrawalRequest
    });
  } catch (error: any) {
    console.error('Get withdrawal request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch withdrawal request',
      error: error.message
    });
  }
};

/**
 * Get tutor's available balance
 */
export const getTutorBalance = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.user.role !== 'tutor') {
      return res.status(403).json({ message: 'Only tutors can view their balance' });
    }

    const tutor = await Tutor.findById(req.user._id).select('availableBalance');
    if (!tutor) {
      return res.status(404).json({ message: 'Tutor not found' });
    }

    // Get pending withdrawal amount
    const pendingWithdrawals = await WithdrawalRequest.find({
      tutorId: req.user._id,
      status: { $in: ['pending', 'approved'] }
    });

    const pendingAmount = pendingWithdrawals.reduce((sum, request) => sum + request.amount, 0);

    res.json({
      success: true,
      data: {
        totalBalance: tutor.availableBalance / 100, // Convert to dollars
        pendingWithdrawals: pendingAmount / 100, // Convert to dollars
        availableForWithdrawal: (tutor.availableBalance - pendingAmount) / 100, // Convert to dollars
        pendingRequestsCount: pendingWithdrawals.length
      }
    });
  } catch (error: any) {
    console.error('Get tutor balance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch tutor balance',
      error: error.message
    });
  }
};

// ============================================================================
// STUDENT REFUND REQUEST FUNCTIONS
// ============================================================================

/**
 * Create a refund request (Student only)
 */
export const createRefundRequest = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.user.role !== 'student') {
      return res.status(403).json({ message: 'Only students can request refunds' });
    }

    const {
      requestedAmount,
      refundType,
      reason,
      description,
      subscriptionId,
      lessonId
    } = req.body;

    // Validate required fields
    if (!requestedAmount || requestedAmount <= 0) {
      return res.status(400).json({ message: 'Valid refund amount is required' });
    }

    if (!refundType || !['subscription', 'lesson', 'other'].includes(refundType)) {
      return res.status(400).json({
        message: 'Valid refund type is required (subscription, lesson, or other)'
      });
    }

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Refund reason is required' });
    }

    // Validate refund type specific requirements
    if (refundType === 'subscription') {
      if (!subscriptionId || !Types.ObjectId.isValid(subscriptionId)) {
        return res.status(400).json({ message: 'Valid subscription ID is required for subscription refunds' });
      }

      // Verify subscription belongs to student
      const subscription = await Subscription.findOne({
        _id: subscriptionId,
        studentId: req.user._id
      });

      if (!subscription) {
        return res.status(404).json({ message: 'Subscription not found or does not belong to you' });
      }

      // Check if subscription is eligible for refund
      if (!['active', 'paused'].includes(subscription.status)) {
        return res.status(400).json({
          message: 'Only active or paused subscriptions are eligible for refunds',
          subscriptionStatus: subscription.status
        });
      }
    }

    if (refundType === 'lesson') {
      if (!lessonId || !Types.ObjectId.isValid(lessonId)) {
        return res.status(400).json({ message: 'Valid lesson ID is required for lesson refunds' });
      }

      // Verify lesson belongs to student
      const lesson = await LessonModel.findOne({
        _id: lessonId,
        studentId: req.user._id
      });

      if (!lesson) {
        return res.status(404).json({ message: 'Lesson not found or does not belong to you' });
      }

      // Check if lesson is eligible for refund (e.g., not completed, within refund window)
      if (lesson.status === 'completed') {
        return res.status(400).json({
          message: 'Completed lessons are not eligible for refunds'
        });
      }
    }

    // Check for existing pending refund requests
    const existingRequest = await RefundRequest.findOne({
      studentId: req.user._id,
      status: 'pending',
      ...(subscriptionId && { subscriptionId }),
      ...(lessonId && { lessonId })
    });

    if (existingRequest) {
      return res.status(409).json({
        message: 'You already have a pending refund request for this item. Please wait for it to be processed.'
      });
    }

    // Create refund request
    const refundRequest = new RefundRequest({
      studentId: req.user._id,
      requestedAmount,
      amount: Math.round(requestedAmount * 100), // Convert to cents
      refundType,
      reason: reason.trim(),
      description: description?.trim(),
      subscriptionId: subscriptionId || undefined,
      lessonId: lessonId || undefined,
      status: 'pending'
    });

    await refundRequest.save();

    // Populate related data for response
    await refundRequest.populate([
      { path: 'studentId', select: 'firstname lastname email' },
      { path: 'subscriptionId', select: 'planType monthlyPrice status' },
      { path: 'lessonId', select: 'title scheduledAt status' }
    ]);

    res.status(201).json({
      success: true,
      message: 'Refund request submitted successfully',
      data: refundRequest
    });
  } catch (error: any) {
    console.error('Create refund request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create refund request',
      error: error.message
    });
  }
};

/**
 * Get student's refund requests
 */
export const getStudentRefundRequests = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.user.role !== 'student') {
      return res.status(403).json({ message: 'Only students can view their refund requests' });
    }

    const { page = 1, limit = 10, status, refundType } = req.query;

    const query: any = { studentId: req.user._id };
    if (status) {
      query.status = status;
    }
    if (refundType) {
      query.refundType = refundType;
    }

    const refundRequests = await RefundRequest.find(query)
      .populate('processedBy', 'firstname lastname')
      .populate('transactionId')
      .populate('subscriptionId', 'planType monthlyPrice status')
      .populate('lessonId', 'title scheduledAt status')
      .sort({ createdAt: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    const total = await RefundRequest.countDocuments(query);

    res.json({
      success: true,
      data: refundRequests,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error: any) {
    console.error('Get student refund requests error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch refund requests',
      error: error.message
    });
  }
};

/**
 * Cancel a refund request (Student only)
 */
export const cancelRefundRequest = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.user.role !== 'student') {
      return res.status(403).json({ message: 'Only students can cancel their refund requests' });
    }

    const { requestId } = req.params;

    if (!Types.ObjectId.isValid(requestId)) {
      return res.status(400).json({ message: 'Invalid request ID format' });
    }

    const refundRequest = await RefundRequest.findOne({
      _id: requestId,
      studentId: req.user._id
    });

    if (!refundRequest) {
      return res.status(404).json({ message: 'Refund request not found' });
    }

    if (!refundRequest.canBeCancelled()) {
      return res.status(400).json({
        message: 'Cannot cancel refund request in current status',
        currentStatus: refundRequest.status
      });
    }

    refundRequest.status = 'cancelled';
    await refundRequest.save();

    res.json({
      success: true,
      message: 'Refund request cancelled successfully',
      data: refundRequest
    });
  } catch (error: any) {
    console.error('Cancel refund request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel refund request',
      error: error.message
    });
  }
};

/**
 * Get refund request by ID (Student only - their own requests)
 */
export const getRefundRequestById = async (req: AuthRequest, res: Response): Promise<any> => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (req.user.role !== 'student') {
      return res.status(403).json({ message: 'Only students can view their refund requests' });
    }

    const { requestId } = req.params;

    if (!Types.ObjectId.isValid(requestId)) {
      return res.status(400).json({ message: 'Invalid request ID format' });
    }

    const refundRequest = await RefundRequest.findOne({
      _id: requestId,
      studentId: req.user._id
    })
      .populate('studentId', 'firstname lastname email')
      .populate('processedBy', 'firstname lastname')
      .populate('transactionId')
      .populate('subscriptionId', 'planType monthlyPrice status currentPeriodStart currentPeriodEnd')
      .populate('lessonId', 'title scheduledAt status tutorId');

    if (!refundRequest) {
      return res.status(404).json({ message: 'Refund request not found' });
    }

    res.json({
      success: true,
      data: refundRequest
    });
  } catch (error: any) {
    console.error('Get refund request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch refund request',
      error: error.message
    });
  }
};
