const fs = require('fs');
const path = require('path');

const baseDir = path.join(__dirname, 'admin');

const folders = [
  'auth',
  'tutor-approval',
  'review-management',
  'withdrawal-management',
  'user-management',

  '../support/tickets',
  '../support/session-management',
  '../support/refunds',
  '../support/affiliate-support',

  '../marketing/reporting',
  '../marketing/affiliate-management',

  '../curriculum/content-management',
  '../curriculum/feedback',

  '../business-logic/trial-no-shows',
  '../business-logic/tutor-ranking',
  '../business-logic/commission-model',
  '../business-logic/email-automation',
  '../business-logic/content-privacy-guard',
];

folders.forEach((folder) => {
  const dirPath = path.resolve(baseDir, folder);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`Created: ${dirPath}`);
  } else {
    console.log(`Skipped (exists): ${dirPath}`);
  }
});
