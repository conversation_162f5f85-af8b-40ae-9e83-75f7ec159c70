import { lazy } from "react";

// Lazy load components for better code splitting
// Core pages (loaded immediately)
import LoginPage from "../pages/auth/login";
import { HomePage } from "../pages/home/<USER>";
import { createBrowserRouter } from "react-router-dom";
import App from "@/app";
import ErrorPage from "@/components/errors/ErrorPage";
import NotFoundPage from "@/components/errors/NotFoundPage";

// Auth pages
const CreatePassword = lazy(() =>
	import("../pages/auth/passwordManager/CreatePassword")
);
const ForgotPassword = lazy(() =>
	import("../pages/auth/passwordManager/ForgotPassword")
);
const VerifyPassword = lazy(() =>
	import("../pages/auth/passwordManager/VerifyPassword")
);
const SignupPage = lazy(() => import("../pages/auth/signup"));

// Public pages
const FindTutors = lazy(() => import("../pages/findTutors/findTutors"));
const TutorProfile = lazy(() =>
	import("@/pages/tutor/tutorProfile/tutorProfile")
);
const LessonTrial = lazy(() => import("@/pages/lessonTrial/LessonTrial"));

// Onboarding
const TutorOnboarding = lazy(() =>
	import("../pages/tutor/tutorOnboarding/tutorOnboardingLayout")
);
const StudentOnboarding = lazy(() =>
	import("@/pages/student/studentOnboarding/studentOnboardingLayout")
);

// Layout wrappers
const StudentLayoutWrapper = lazy(() =>
	import("@/layout/studentLayoutWrapper/StudentLayoutWrapper")
);
const TutorLayoutWrapper = lazy(() =>
	import("@/layout/tutorLayoutWrapper/TutorLayoutWrapper")
);
const AdminLayoutWrapper = lazy(() =>
	import("@/layout/adminLayoutWrapper/AdminLayoutWrapper")
);

// Student pages
const StudentDashboard = lazy(() =>
	import("@/pages/student/studentDashboard/studentDashboard")
);
const MyLessons = lazy(() =>
	import("@/pages/student/studentDashboard/mylessons/MyLessons")
);
const Tutors = lazy(() =>
	import("@/pages/student/studentDashboard/mylessons/tutors/Tutors")
);
const Calender = lazy(() =>
	import("@/pages/student/studentDashboard/mylessons/calendar/Calender")
);
const StudentReviews = lazy(() =>
	import("@/pages/student/studentDashboard/reviews/StudentReviews")
);
const StudentSettings = lazy(() =>
	import("@/pages/student/studentDashboard/settings/StudentSettings")
);
const TutorDetails = lazy(() =>
	import("@/pages/student/studentDashboard/mylessons/tutors/TutorDetails")
);
const SubscriptionPage = lazy(() =>
	import("@/pages/student/studentDashboard/mylessons/tutors/SubscriptionPage")
);
const Subscription = lazy(() =>
	import("@/pages/student/studentDashboard/subscription/Subscription")
);

// Tutor pages
const TutorDashboard = lazy(() =>
	import("@/pages/tutor/tutorDashboard/tutorDashboard")
);
const TutorLessons = lazy(() =>
	import("@/pages/tutor/tutorDashboard/mylessons/TutorLessons")
);
const TutorReviews = lazy(() =>
	import("@/pages/tutor/tutorDashboard/reviews/TutorReviews")
);
const TutorSettings = lazy(() =>
	import("@/pages/tutor/tutorDashboard/settings/TutorSettings")
);

// Admin pages
const AdminDashboard = lazy(() =>
	import("@/pages/admin/adminDashboard/AdminDashboard")
);
const AdminStudentsPage = lazy(() =>
	import("@/pages/admin/adminDashboard/AdminStudentsPage")
);
const AdminStudentProfile = lazy(() =>
	import("@/pages/admin/adminDashboard/AdminStudentProfile")
);
const AdminTutorProfile = lazy(() =>
	import("@/pages/admin/adminDashboard/AdminTutorProfile")
);
const AdminTutorsPage = lazy(() =>
	import("@/pages/admin/adminDashboard/AdminTutorsPage")
);
const AdminHistory = lazy(() =>
	import("@/pages/admin/adminDashboard/AdminHistory")
);
const AdminBookings = lazy(() =>
	import("@/pages/admin/adminDashboard/AdminBookings")
);
const AdminPayouts = lazy(() =>
	import("@/pages/admin/adminDashboard/AdminPayouts")
);
const AdminSubAccounts = lazy(() =>
	import("@/pages/admin/adminDashboard/AdminSubAccounts")
);
const AdminRefundApproval = lazy(() =>
	import("@/pages/admin/adminDashboard/AdminRefundApproval")
);
const SystemSsettings = lazy(() =>
	import("@/pages/admin/adminDashboard/SystemSsettings")
);
const AdminReviews = lazy(() =>
	import("@/pages/admin/adminDashboard/AdminReviews")
);
const AdminReport = lazy(() =>
	import("@/pages/admin/adminDashboard/AdminReport")
);
const AdminApplications = lazy(() =>
	import("@/pages/admin/adminDashboard/AdminApplications")
);
const AdminTutorsReviews = lazy(() =>
	import("@/pages/admin/components/tutors/AdminTutorsReviews")
);

// Classroom and messaging (heavy components)
const ClassroomLayout = lazy(() => import("@/pages/classroom/classroomLayout"));
const Chats = lazy(() => import("@/pages/messaging/Chats"));

// Payment pages
const Payments = lazy(() => import("@/pages/payments/Payments"));
const PostPayment = lazy(() => import("@/pages/payments/PostPayment"));

export const appRoutes = [
	{
		path: "/",
		element: <HomePage />,
	},
	{
		path: "*",
		element: <NotFoundPage />,
	},
	{
		path: "/signin",
		element: <LoginPage />,
	},
	{
		path: "/reset-password",
		element: <ForgotPassword />,
	},
	{
		path: "create-password",
		element: <CreatePassword />,
	},
	{
		path: "/verify-password/",
		element: <VerifyPassword />,
	},
	{
		path: "/signup/:role",
		element: <SignupPage />,
	},
	{
		path: "/find-tutors",
		element: <FindTutors />,
	},
	{
		path: "/tutor-onboarding",
		element: <TutorOnboarding />,
	},
	{
		path: "/student-onboarding",
		element: <StudentOnboarding />,
	},
	{
		path: "/tutor-profile/:id",
		element: <TutorProfile />,
	},

	{
		path: "/classroom/:classId",
		element: <ClassroomLayout />,
	},
	{
		path: "/admin",
		element: <AdminLayoutWrapper />,
		children: [
			{
				path: "dashboard",
				element: <AdminDashboard />,
			},
			{
				path: "students",
				element: <AdminStudentsPage />,
			},
			{
				path: "students/:id",
				element: <AdminStudentProfile />,
			},
			{
				path: "tutors",
				element: <AdminTutorsPage />,
			},
			{
				path: "tutors/:id",
				element: <AdminTutorProfile />,
			},
			{
				path: "tutors/:id/reviews",
				element: <AdminTutorsReviews />,
			},
			{
				path: "history",
				element: <AdminHistory />,
			},
			{
				path: "bookings",
				element: <AdminBookings />,
			},
			{
				path: "payouts",
				element: <AdminPayouts />,
			},
			{
				path: "refund-approval",
				element: <AdminRefundApproval />,
			},
			{
				path: "subaccounts",
				element: <AdminSubAccounts />,
			},

			{
				path: "reports",
				element: <AdminReport />,
			},
			{
				path: "applications",
				element: <AdminApplications />,
			},
		],
	},
	{
		path: "/tutor",
		element: <TutorLayoutWrapper />,
		children: [
			{
				path: "dashboard",
				element: <TutorDashboard />,
			},
			{
				path: "messages/:userId?",
				element: <Chats />,
			},
			{
				path: "my-lessons",
				element: <TutorLessons />,
			},
			{
				path: "reviews",
				element: <TutorReviews />,
			},
			{
				path: "settings",
				element: <TutorSettings />,
			},
		],
	},

	{
		path: "/student",
		element: <StudentLayoutWrapper />,
		children: [
			{
				path: "dashboard",
				element: <StudentDashboard />,
			},
			{
				path: "messages/:userId?",
				element: <Chats />,
			},
			{
				path: "my-lessons",
				element: <MyLessons />,
			},
			{
				path: "tutors",
				element: <Tutors />,
			},
			{
				path: "my-lessons/tutors/:id",
				element: <TutorDetails />,
			},
			{
				path: "my-lessons/tutors/:id/subscribe",
				element: <SubscriptionPage />,
			},
			{
				path: "calendar",
				element: <Calender />,
			},
			{
				path: "reviews",
				element: <StudentReviews />,
			},
			{
				path: "subscription",
				element: <Subscription />,
			},
			{
				path: "settings",
				element: <StudentSettings />,
			},
		],
	},
	{
		path: "/payment",
		element: <Payments />,
	},
	{
		path: "/post-payment",
		element: <PostPayment />,
	},
	{
		path: "/trial-lesson",
		element: <LessonTrial />,
	},
];

export const router = createBrowserRouter([
	{
		path: "/",
		element: <App />,
		children: appRoutes,
		errorElement: <ErrorPage />,
	},
]);
