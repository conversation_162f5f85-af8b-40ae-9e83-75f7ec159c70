#!/usr/bin/env ts-node

/**
 * Test script to demonstrate the 20% platform fee implementation
 * Shows calculations and examples for different tutor pricing scenarios
 */

/**
 * Calculate platform fee and tutor earnings
 */
function calculatePlatformFee(lessonPrice: number): {
  grossAmount: number;
  platformFeeRate: number;
  platformFeeAmount: number;
  netEarnings: number;
  platformFeePercentage: string;
} {
  const platformFeeRate = 0.20; // 20%
  const platformFeeAmount = lessonPrice * platformFeeRate;
  const netEarnings = lessonPrice - platformFeeAmount;

  return {
    grossAmount: lessonPrice,
    platformFeeRate,
    platformFeeAmount,
    netEarnings,
    platformFeePercentage: '20%'
  };
}

/**
 * Calculate monthly earnings for a tutor
 */
function calculateMonthlyEarnings(
  lessonPrice: number, 
  lessonsPerWeek: number, 
  weeksPerMonth: number = 4
): {
  totalLessons: number;
  grossMonthlyEarnings: number;
  totalPlatformFees: number;
  netMonthlyEarnings: number;
  averagePerLesson: {
    gross: number;
    platformFee: number;
    net: number;
  };
} {
  const totalLessons = lessonsPerWeek * weeksPerMonth;
  const grossMonthlyEarnings = lessonPrice * totalLessons;
  const totalPlatformFees = grossMonthlyEarnings * 0.20;
  const netMonthlyEarnings = grossMonthlyEarnings - totalPlatformFees;

  return {
    totalLessons,
    grossMonthlyEarnings,
    totalPlatformFees,
    netMonthlyEarnings,
    averagePerLesson: {
      gross: lessonPrice,
      platformFee: lessonPrice * 0.20,
      net: lessonPrice * 0.80
    }
  };
}

/**
 * Demonstrate platform fee calculations
 */
function demonstratePlatformFee() {
  console.log('='.repeat(80));
  console.log('TUTOR PLATFORM FEE IMPLEMENTATION (20%)');
  console.log('='.repeat(80));

  console.log('\n💰 PLATFORM FEE STRUCTURE:');
  console.log('-'.repeat(50));
  console.log('• Platform Fee Rate: 20%');
  console.log('• Tutor Earnings: 80% of lesson price');
  console.log('• Platform Revenue: 20% of lesson price');
  console.log('• Fee Applied: On each successful lesson completion');

  console.log('\n📊 CALCULATION EXAMPLES:');
  console.log('='.repeat(80));

  // Test different tutor pricing scenarios
  const tutorScenarios = [
    { name: 'Budget Tutor', price: 15 },
    { name: 'Standard Tutor', price: 25 },
    { name: 'Premium Tutor', price: 40 },
    { name: 'Expert Tutor', price: 60 },
    { name: 'Specialist Tutor', price: 100 }
  ];

  tutorScenarios.forEach(scenario => {
    console.log(`\n📚 ${scenario.name} ($${scenario.price}/lesson):`);
    console.log('-'.repeat(50));
    
    const calculation = calculatePlatformFee(scenario.price);
    
    console.log(`Gross Amount:      $${calculation.grossAmount.toFixed(2)}`);
    console.log(`Platform Fee (20%): $${calculation.platformFeeAmount.toFixed(2)}`);
    console.log(`Tutor Earnings:    $${calculation.netEarnings.toFixed(2)}`);
    console.log(`Tutor Keeps:       ${((calculation.netEarnings / calculation.grossAmount) * 100).toFixed(1)}%`);
  });

  console.log('\n📅 MONTHLY EARNINGS BREAKDOWN:');
  console.log('='.repeat(80));

  // Monthly earnings for different lesson frequencies
  const monthlyScenarios = [
    { tutor: 'Standard Tutor ($25/lesson)', price: 25, lessonsPerWeek: 10 },
    { tutor: 'Premium Tutor ($40/lesson)', price: 40, lessonsPerWeek: 15 },
    { tutor: 'Expert Tutor ($60/lesson)', price: 60, lessonsPerWeek: 20 }
  ];

  monthlyScenarios.forEach(scenario => {
    console.log(`\n🗓️ ${scenario.tutor} - ${scenario.lessonsPerWeek} lessons/week:`);
    console.log('-'.repeat(60));
    
    const monthly = calculateMonthlyEarnings(scenario.price, scenario.lessonsPerWeek);
    
    console.log(`Total Lessons/Month:     ${monthly.totalLessons}`);
    console.log(`Gross Monthly Earnings:  $${monthly.grossMonthlyEarnings.toFixed(2)}`);
    console.log(`Total Platform Fees:    $${monthly.totalPlatformFees.toFixed(2)}`);
    console.log(`Net Monthly Earnings:    $${monthly.netMonthlyEarnings.toFixed(2)}`);
    console.log(`Per Lesson (Net):        $${monthly.averagePerLesson.net.toFixed(2)}`);
  });

  console.log('\n🔧 TECHNICAL IMPLEMENTATION:');
  console.log('='.repeat(80));

  console.log('\n📝 Code Example - Lesson Completion:');
  console.log(`
const lessonPriceInCents = Math.round(tutor.basePrice * 100);
const platformFeeRate = 0.20; // 20% platform fee
const platformFeeAmount = Math.round(lessonPriceInCents * platformFeeRate);
const tutorEarnings = lessonPriceInCents - platformFeeAmount;

// Update tutor balance with net earnings (80%)
tutor.availableBalance += tutorEarnings;
  `);

  console.log('\n📊 Transaction Records Created:');
  console.log('1. Lesson Payment (Full Amount)');
  console.log('2. Platform Fee (20% Deduction)');
  console.log('3. Tutor Payout (80% Net Earnings)');

  console.log('\n🔍 COMPARISON: BEFORE vs AFTER PLATFORM FEE');
  console.log('='.repeat(80));

  const examplePrice = 50;
  console.log(`\nExample: $${examplePrice} lesson`);
  console.log('-'.repeat(30));
  console.log('BEFORE (No Platform Fee):');
  console.log(`  Tutor Receives: $${examplePrice.toFixed(2)} (100%)`);
  console.log(`  Platform Revenue: $0.00 (0%)`);
  
  const afterCalculation = calculatePlatformFee(examplePrice);
  console.log('\nAFTER (20% Platform Fee):');
  console.log(`  Tutor Receives: $${afterCalculation.netEarnings.toFixed(2)} (80%)`);
  console.log(`  Platform Revenue: $${afterCalculation.platformFeeAmount.toFixed(2)} (20%)`);

  console.log('\n📈 BUSINESS IMPACT:');
  console.log('='.repeat(80));

  // Calculate platform revenue for different scenarios
  const platformRevenue = [
    { scenario: '100 lessons/month at $25 avg', lessons: 100, avgPrice: 25 },
    { scenario: '500 lessons/month at $35 avg', lessons: 500, avgPrice: 35 },
    { scenario: '1000 lessons/month at $40 avg', lessons: 1000, avgPrice: 40 }
  ];

  platformRevenue.forEach(scenario => {
    const totalRevenue = scenario.lessons * scenario.avgPrice;
    const platformFee = totalRevenue * 0.20;
    const tutorEarnings = totalRevenue * 0.80;
    
    console.log(`\n💼 ${scenario.scenario}:`);
    console.log(`  Total Lesson Revenue: $${totalRevenue.toLocaleString()}`);
    console.log(`  Platform Revenue (20%): $${platformFee.toLocaleString()}`);
    console.log(`  Tutor Earnings (80%): $${tutorEarnings.toLocaleString()}`);
  });

  console.log('\n🧪 API TESTING EXAMPLES:');
  console.log('='.repeat(80));

  console.log('\n📤 Mark Lesson as Completed:');
  console.log('PATCH /api/tutor/lessons/:lessonId/status');
  console.log('Authorization: Bearer tutor-jwt-token');
  console.log('Content-Type: application/json');
  console.log(`
{
  "status": "completed",
  "notes": "Great lesson, student understood the concepts well!"
}
  `);

  console.log('\n📥 Expected Response:');
  console.log(`
{
  "success": true,
  "message": "Lesson status updated successfully",
  "data": {
    "id": "lesson_id",
    "status": "completed",
    "confirmedAt": "2024-01-15T15:00:00.000Z"
  }
}
  `);

  console.log('\n📊 Get Detailed Earnings:');
  console.log('GET /api/tutor/earnings-detailed');
  console.log('Authorization: Bearer tutor-jwt-token');

  console.log('\n📥 Expected Earnings Response:');
  console.log(`
{
  "success": true,
  "data": {
    "earnings": {
      "summary": {
        "totalCompletedLessons": 10,
        "totalGrossEarnings": 500.00,
        "totalPlatformFees": 100.00,
        "totalNetEarnings": 400.00,
        "platformFeeRate": 20
      },
      "lessons": [
        {
          "earnings": {
            "grossAmount": 50.00,
            "platformFeeRate": 20,
            "platformFeeAmount": 10.00,
            "netEarnings": 40.00
          }
        }
      ]
    },
    "platformFeeInfo": {
      "rate": 20,
      "description": "Platform fee charged on each successful lesson",
      "calculation": "Net Earnings = Gross Amount - (Gross Amount × 20%)"
    }
  }
}
  `);

  console.log('\n✅ IMPLEMENTATION SUMMARY:');
  console.log('='.repeat(80));
  console.log('✅ 20% platform fee automatically applied on lesson completion');
  console.log('✅ Tutors receive 80% of lesson price as net earnings');
  console.log('✅ All fees tracked in transaction system for audit trail');
  console.log('✅ Transparent earnings breakdown available to tutors');
  console.log('✅ Consistent fee application across all lesson types');
  console.log('✅ Error handling ensures lesson completion doesn\'t fail');
  console.log('✅ Integration with existing payment and withdrawal systems');

  console.log('\n🎯 KEY BENEFITS:');
  console.log('-'.repeat(50));
  console.log('• Sustainable platform revenue model');
  console.log('• Transparent fee structure for tutors');
  console.log('• Automatic fee collection with no manual intervention');
  console.log('• Complete audit trail for accounting purposes');
  console.log('• Scalable implementation for growing lesson volume');

  console.log('\n🚀 Platform fee implementation is complete and ready for production!');
}

// Run the demonstration
if (require.main === module) {
  demonstratePlatformFee();
}

export { 
  calculatePlatformFee, 
  calculateMonthlyEarnings, 
  demonstratePlatformFee 
};
