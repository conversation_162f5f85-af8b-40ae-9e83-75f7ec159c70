import { Server } from "socket.io";
import { CORS_CONFIG } from "../misc";
import http from "http";
import { Express } from "express";
import { createError } from "../../middlewares/errorHandler";
import { decodeJWT } from "../../middlewares/auth";
import { socketIOLimiter } from "../../middlewares/rateLimiter";
import createClassroomSocket from "./classroom-socket";
import createChatSocket from "./chat-socket";
import { handleExitSignals } from "../../utils/error";
import { unBindEvents } from "../../utils/socket-cache";
import { createUserSocket } from "./user-socket";

export const SOCKET_ERROR_KEY = "error-response";

const createServerSocket = (app: Express) => {
  const server = http.createServer(app);

  const io = new Server(server, {
    cors: CORS_CONFIG,
  });

  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;

      const forwarded = socket.handshake.headers["x-forwarded-for"];
      const rawIp = socket.handshake.address;

      const ip = forwarded
        ? (Array.isArray(forwarded)
            ? forwarded
            : forwarded.split(","))[0]?.trim() || rawIp
        : rawIp;

      if (!token) {
        next(createError("Unauthorized: Token not provided", 401));
        return;
      }

      try {
        await socketIOLimiter.consume(ip);
      } catch (err) {
        next(
          createError(
            "Too many connection attempts. Please try again later.",
            429
          )
        );
        return;
      }

      const decoded = decodeJWT(token);

      if (!decoded) {
        next(createError("Invalid token or token expired", 401));
        return;
      }

      socket.data.user = decoded;

      next();
    } catch (error: any) {
      next(error);
    }
  });

  io.on("connection", (socket) => {
    const classroomSocket = createClassroomSocket(socket);

    const chatSocket = createChatSocket(socket, io);

    const userSocket = createUserSocket(socket, io);

    // handleExitSignals(() => {
    //   unBindEvents(socket, classroomSocket.handlers);
    //   unBindEvents(socket, chatSocket.handlers);
    // });

    socket.on("disconnect", () => {
      classroomSocket.onLeave();
      chatSocket.onLeave();
    });
  });

  return server;
};

export default createServerSocket;
