import { Server, Socket } from "socket.io";
import { safelyBind } from "../../utils/socket-cache";
import { KeyValuePair } from "../../types/misc";
import { isObject } from "../../utils/validation";
import { SOCKET_ERROR_KEY } from "./server-socket";
import { getErrorResponse } from "../../middlewares/errorHandler";

export const createUserSocket = (socket: Socket, io: Server) => {
  safelyBind(io, "user", (data: KeyValuePair) => {
    if (!isObject(data)) {
      socket.emit(
        SOCKET_ERROR_KEY,
        getErrorResponse(
          {
            message: "invalid data format.",
            details: {
              reason: "Expect a plain object {} data type",
            },
          },
          400
        )
      );
      return;
    }

    io.emit("user", data);
  });
};
