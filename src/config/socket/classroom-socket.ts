import {
  handleRegisterRoomSocketData,
  handleReJoinRoom,
  handleUnRegisterSocket,
  safelyBind,
  TSocketData,
  unBindEvents,
} from "../../utils/socket-cache";

const createClassroomSocket = (socket: any) => {
  const userId = socket.data.user.id;

  const roomKey = "class-room-" + socket.id;

  const userRoomKey = "class-room-" + userId;

  const subHandlers = {
    broadcast: {
      event: "class-room-broadcast",
      handler: (channelId: string, data: any) => {
        socket.to(channelId).emit("class-room-broadcast", data);
      },
    },
  };

  const onJoinRoom = async (channelId: string, joinData: TSocketData) => {
    safelyBind(
      socket,
      subHandlers.broadcast.event,
      subHandlers.broadcast.handler
    );

    const { hasJoined, hasErrors } = handleRegisterRoomSocketData(
      socket,
      roomKey,
      userRoomKey,
      channelId,
      joinData
    );

    if (hasErrors) return;

    if (!hasJoined) socket.to(channelId).emit("joined-class-room", joinData);
  };

  const onLeave = () => {
    handleUnRegisterSocket(socket, roomKey, userRoomKey, "left-class-room");
    unBindEvents(socket, handlers);
  };

  const handlers = {
    ...subHandlers,
    join: {
      event: "join-class-room",
      handler: onJoinRoom,
    },
    leave: {
      event: "leave-class-room",
      handler: onLeave,
    },
  };

  handleReJoinRoom(userRoomKey, onJoinRoom);

  safelyBind(socket, handlers.join.event, handlers.join.handler);

  safelyBind(socket, handlers.leave.event, handlers.leave.handler);

  return { onLeave, handlers };
};

export default createClassroomSocket;
