/**
 * Webhook Configuration
 * Defines the connection between Stripe webhooks and subscription controller
 */

export interface WebhookEventConfig {
  eventType: string;
  description: string;
  handlerFunction: string;
  notifyController: boolean;
  requiresAuth: boolean;
}

export const WEBHOOK_EVENTS: WebhookEventConfig[] = [
  {
    eventType: 'invoice.payment_succeeded',
    description: 'Payment for an invoice was successful',
    handlerFunction: 'handleInvoicePaymentSucceeded',
    notifyController: true,
    requiresAuth: false
  },
  {
    eventType: 'invoice.payment_failed',
    description: 'Payment for an invoice failed',
    handlerFunction: 'handleInvoicePaymentFailed',
    notifyController: true,
    requiresAuth: false
  },
  {
    eventType: 'invoice.finalized',
    description: 'Invoice has been finalized and is ready for payment',
    handlerFunction: 'handleInvoiceFinalized',
    notifyController: false,
    requiresAuth: false
  },
  {
    eventType: 'customer.subscription.updated',
    description: 'Subscription status or details were updated',
    handlerFunction: 'handleSubscriptionUpdated',
    notifyController: true,
    requiresAuth: false
  },
  {
    eventType: 'customer.subscription.deleted',
    description: 'Subscription was cancelled/deleted',
    handlerFunction: 'handleSubscriptionDeleted',
    notifyController: true,
    requiresAuth: false
  },
  {
    eventType: 'setup_intent.succeeded',
    description: 'Setup intent for future payments succeeded',
    handlerFunction: 'handleSetupIntentSucceeded',
    notifyController: false,
    requiresAuth: false
  },
  {
    eventType: 'payment_intent.succeeded',
    description: 'Payment intent was successful',
    handlerFunction: 'handlePaymentIntentSucceeded',
    notifyController: true,
    requiresAuth: false
  }
];

export const WEBHOOK_CONFIG = {
  // Webhook endpoint configuration
  endpoint: '/webhook/stripe',
  
  // Subscription controller notification endpoint
  notificationEndpoint: '/api/subscription/webhook-notify',
  
  // Webhook signature verification
  verifySignature: true,
  
  // Retry configuration for failed webhook processing
  retryConfig: {
    maxRetries: 3,
    retryDelay: 1000, // milliseconds
    exponentialBackoff: true
  },
  
  // Logging configuration
  logging: {
    logAllEvents: true,
    logSuccessfulEvents: true,
    logFailedEvents: true,
    logNotifications: true
  },
  
  // Security configuration
  security: {
    requireSignature: true,
    allowedOrigins: ['stripe.com'],
    rateLimitPerMinute: 100
  }
};

/**
 * Get webhook event configuration by event type
 */
export function getWebhookEventConfig(eventType: string): WebhookEventConfig | undefined {
  return WEBHOOK_EVENTS.find(event => event.eventType === eventType);
}

/**
 * Check if event should notify controller
 */
export function shouldNotifyController(eventType: string): boolean {
  const config = getWebhookEventConfig(eventType);
  return config?.notifyController || false;
}

/**
 * Get all supported webhook event types
 */
export function getSupportedEventTypes(): string[] {
  return WEBHOOK_EVENTS.map(event => event.eventType);
}

/**
 * Validate webhook event type
 */
export function isValidEventType(eventType: string): boolean {
  return getSupportedEventTypes().includes(eventType);
}

export default WEBHOOK_CONFIG;
