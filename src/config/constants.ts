import dotenv from "dotenv";
import { PROFILE_REGISTRATION_ROLES } from "../models/profile";
import path from "path";

dotenv.config();

export const IS_PROD = process.env.NODE_ENV === "production";

export const LOGS_DIR = path.join(
  process.cwd(),
  "src/logs",
  IS_PROD ? "/prod" : "/dev"
);

export const SERVER_ORIGIN = "https://convolly-backend.onrender.com";

export const CLIENT_ORIGIN = "";

export const DEFAULT_AUTH_PROVIDER = "local";

export const DEFAULT_PROFILE_ROLE = "student";

export const ERROR_INVALID_ROLE = {
  message: "Invalid role specified",
  details: {
    allowedRoles: PROFILE_REGISTRATION_ROLES,
  },
  status: 403,
};

export const STATUS_CODES = {
  TUTOR_NOT_APPROVED: "TUTOR_NOT_APPROVED",
};

export const ERROR_FORBIDDEN_ACCESS = {
  message: "Forbidden Access",
  status: 403,
};

export const ERROR_INTERNAL_SERVER = {
  message: "Internal Server Error",
  status: 500,
};

export const AUTH_PROVIDERS = [
  DEFAULT_AUTH_PROVIDER,
  "apple",
  "google",
  "facebook",
  "linkedIn",
];

export const ERROR_MISSING_PROVIDER = {
  message: "Forbideen access",
  name: "AUTH_PROVIDER_MISSING",
  details: {
    reason: "Missing provider",
  },
  status: 403,
};
