import { useEffect } from "react";
import posthog from "posthog-js";

export const usePostHog = () => {
  useEffect(() => {
    posthog.opt_in_capturing();
  }, []);

  const capture = (event, properties = {}) => {
    posthog.capture(event, properties);
  };

  const identify = (userId, properties = {}) => {
    posthog.identify(userId, properties);
  };

  const reset = () => {
    // clears user identity
    posthog.reset();
  };

  return {
    capture,
    identify,
    reset
  };
};
