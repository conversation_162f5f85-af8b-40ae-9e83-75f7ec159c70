import { useRef, useLayoutEffect } from "react";

/**
 * useScrollAnchor
 * Keeps the scroll position stable when new messages are prepended.
 *
 * @param {React.RefObject} scrollRef - The scrollable container.
 * @param {Array} itemsCount - The current size of a list or array.
 * @param {Number} selector - string passed to element.querySelector(selctor).
 */

export const useScrollAnchor = (scrollRef, itemsCount, selector) => {
  const anchorRef = useRef(null);
  const prevItemsCount = useRef(itemsCount);

  useLayoutEffect(() => {
    const container = scrollRef?.current;

    if (!container) return;

    if (itemsCount > prevItemsCount.current) {
      const firstVisibleChild = container.querySelector(selector);

      if (firstVisibleChild) {
        const prevTop = firstVisibleChild.getBoundingClientRect().top;
        anchorRef.current = { el: firstVisibleChild, prevTop };
      }
    }

    requestAnimationFrame(() => {
      if (anchorRef.current) {
        anchorRef.current.el.scrollIntoView({ behavior: "smooth" });
        // const { el, prevTop } = anchorRef.current;
        // const newTop = el.getBoundingClientRect().top;
        // container.scrollTop += newTop - prevTop;
        // anchorRef.current = null;
      }
      prevItemsCount.current = itemsCount;
    });
  }, [itemsCount, scrollRef, selector]);
};
