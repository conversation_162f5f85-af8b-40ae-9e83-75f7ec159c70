import { useEffect, useState } from "react";
import { useToast } from "../context/toastContext/toastContext";

// General GET handler hook for Query fetching
const useGet = (query, options = {}, condition = true) => {
  const { showError } = useToast();

  // if a condition is passed, verify if it is valid then call the endpoint or not
  const queryResult = !condition
    ? {
        data: undefined,
        error: null,
        isLoading: false,
        isError: false,
        isSuccess: false,
        refetch: () => {}
      }
    : query(options);

  const { data, error, isLoading, isError, isSuccess, refetch } = queryResult;

  // store the error
  const [internalError, setInternalError] = useState(null);

  useEffect(() => {
    if (isError && error) {
      const message =
        error?.data?.message || `An error occurred while fetching data`;

      // Add more detailed error logging for debugging
      console.error("API GET Error Details:", {
        message,
        status: error?.status,
        data: error?.data,
        originalError: error
      });

      showError(message);
      setInternalError(message);
    }
  }, [isError, error, showError]);

  return {
    data: data?.data,
    isLoading,
    isError,
    isSuccess,
    error: internalError,
    refetch
  };
};

export default useGet;
