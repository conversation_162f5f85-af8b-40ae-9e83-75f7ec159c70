import { useCallback, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useSocket } from './useSocket';
import {
  fetchConversations,
  fetchMessages,
  loadMoreMessages,
  searchChatData,
  markAsRead,
  fetchUnreadCount,
  setActiveConversation,
  addMessage,
  updateMessage,
  setUserTyping,
  removeUserTyping,
  setUserOnline,
  updateUnreadCount,
  addPendingMessage,
  removePendingMessage,
} from '../redux/slices/chatSlice';
import { encryptMessage, encryptFile, decryptText } from '../pages/messaging/utils/crypto';

/**
 * Main chat hook that provides all chat functionality
 */
export const useChat = () => {
  const dispatch = useDispatch();
  const chatState = useSelector(state => state.chat);
  const currentUser = useSelector(state => state.app?.userInfo?.user);
  const accessToken = useSelector(state => state.app?.userInfo?.accessToken);

  // Socket connection
  const socketOpts = {
    auth: { token: accessToken }
  };
  
  const { socket, connected } = useSocket(
    "https://convolly-backend.onrender.com",
    currentUser ? socketOpts : undefined
  );

  // Refs for managing state
  const typingTimeoutRef = useRef({});
  const isTypingRef = useRef({});

  /**
   * Load user conversations
   */
  const loadConversations = useCallback(async (options = {}) => {
    if (!currentUser?.id || !accessToken) return;
    
    return dispatch(fetchConversations({
      userId: currentUser.id,
      accessToken,
      options,
    }));
  }, [dispatch, currentUser?.id, accessToken]);

  /**
   * Load messages for a conversation
   */
  const loadMessages = useCallback(async (conversationId, options = {}) => {
    if (!conversationId || !accessToken) return;
    
    return dispatch(fetchMessages({
      conversationId,
      accessToken,
      options,
    }));
  }, [dispatch, accessToken]);

  /**
   * Load more messages (pagination)
   */
  const loadMoreMessagesForConversation = useCallback(async (conversationId, cursor) => {
    if (!conversationId || !accessToken || !cursor) return;
    
    return dispatch(loadMoreMessages({
      conversationId,
      accessToken,
      cursor,
    }));
  }, [dispatch, accessToken]);

  /**
   * Search chats
   */
  const searchChats = useCallback(async (query, options = {}) => {
    if (!query.trim() || !accessToken) return;
    
    return dispatch(searchChatData({
      query,
      accessToken,
      options,
    }));
  }, [dispatch, accessToken]);

  /**
   * Send a message
   */
  const sendMessage = useCallback(async (conversationId, messageText, files = [], otherUser) => {
    if (!currentUser || !otherUser || (!messageText.trim() && files.length === 0)) {
      return;
    }

    const tempId = `temp_${Date.now()}_${Math.random()}`;

    try {
      // Encrypt files if any
      const encryptedFiles = [];
      if (files.length > 0) {
        for (const file of files) {
          const recipients = {};
          
          // Encrypt for recipient
          recipients[otherUser.id] = await encryptFile(
            file.file,
            otherUser.encryptedData.publicKey
          );
          
          // Encrypt for sender
          recipients[currentUser.id] = await encryptFile(
            file.file,
            currentUser.encryptedData.publicKey
          );

          encryptedFiles.push({
            recipients,
            name: file.name,
            extension: file.name.split('.').pop(),
            mimetype: file.file.type,
            size: file.size,
            type: file.type,
          });
        }
      }

      // Encrypt message text
      const receiverEncryptedMessage = messageText
        ? await encryptMessage(otherUser.encryptedData.publicKey, messageText)
        : null;

      const senderEncryptedMessage = messageText
        ? await encryptMessage(currentUser.encryptedData.publicKey, messageText)
        : null;

      const message = {
        conversationId: conversationId || '',
        tempId,
        sender: {
          role: currentUser.role,
          id: currentUser.id,
          firstname: currentUser.firstname,
          lastname: currentUser.lastname,
          image: currentUser.image,
        },
        receiver: {
          role: otherUser.role,
          id: otherUser.id,
        },
        files: encryptedFiles,
        recipients: {
          [currentUser.id]: senderEncryptedMessage,
          [otherUser.id]: receiverEncryptedMessage,
        },
        text: messageText || (encryptedFiles.length > 0 ? '[File attached]' : ''),
        createdAt: new Date().toISOString(),
      };

      // Add to pending messages for optimistic update
      dispatch(addPendingMessage({
        conversationId: conversationId || 'new',
        message: {
          ...message,
          files: encryptedFiles.map((f, index) => ({
            ...f,
            preview: files[index]?.preview,
          })),
        },
      }));

      // Send via socket
      if (socket && connected) {
        socket.emit('send-chat-message', message);
      }

      return tempId;
    } catch (error) {
      console.error('Error sending message:', error);
      // Remove from pending messages on error
      dispatch(removePendingMessage({
        conversationId: conversationId || 'new',
        tempId,
      }));
      throw error;
    }
  }, [currentUser, socket, connected, dispatch]);

  /**
   * Mark messages as read
   */
  const markMessagesAsRead = useCallback(async (conversationId, messageIds) => {
    if (!conversationId || !messageIds.length || !accessToken) return;
    
    return dispatch(markAsRead({
      conversationId,
      messageIds,
      accessToken,
    }));
  }, [dispatch, accessToken]);

  /**
   * Set active conversation
   */
  const setActiveConversationId = useCallback((conversationId) => {
    dispatch(setActiveConversation(conversationId));
  }, [dispatch]);

  /**
   * Handle typing indicators
   */
  const handleTyping = useCallback((conversationId, otherUserId) => {
    if (!socket || !connected || !currentUser || !otherUserId) return;

    const key = `${conversationId}_${otherUserId}`;

    // Start typing
    if (!isTypingRef.current[key]) {
      isTypingRef.current[key] = true;
      socket.emit('chat-user-typing', otherUserId, currentUser);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current[key]) {
      clearTimeout(typingTimeoutRef.current[key]);
    }

    // Set timeout to stop typing
    typingTimeoutRef.current[key] = setTimeout(() => {
      if (isTypingRef.current[key]) {
        socket.emit('chat-user-stopped-typing', otherUserId, currentUser);
        isTypingRef.current[key] = false;
      }
    }, 3000);
  }, [socket, connected, currentUser]);

  /**
   * Stop typing
   */
  const stopTyping = useCallback((conversationId, otherUserId) => {
    if (!socket || !connected || !currentUser || !otherUserId) return;

    const key = `${conversationId}_${otherUserId}`;

    if (isTypingRef.current[key]) {
      socket.emit('chat-user-stopped-typing', otherUserId, currentUser);
      isTypingRef.current[key] = false;
    }

    if (typingTimeoutRef.current[key]) {
      clearTimeout(typingTimeoutRef.current[key]);
      delete typingTimeoutRef.current[key];
    }
  }, [socket, connected, currentUser]);

  /**
   * Get unread count
   */
  const getUnreadCount = useCallback(async () => {
    if (!currentUser?.id || !accessToken) return;
    
    return dispatch(fetchUnreadCount({
      userId: currentUser.id,
      accessToken,
    }));
  }, [dispatch, currentUser?.id, accessToken]);

  // Socket event handlers
  useEffect(() => {
    if (!socket || !currentUser) return;

    // Join chat room
    socket.emit('join-chat-room');

    // Handle new messages
    const handleNewMessage = async (message) => {
      try {
        const decryptedText = await decryptText(currentUser, message);
        const messageWithText = { ...message, text: decryptedText };
        
        dispatch(addMessage({
          conversationId: message.conversation.id,
          message: messageWithText,
        }));

        // Update unread count if not from current user
        if (message.sender.id !== currentUser.id) {
          dispatch(updateUnreadCount({
            conversationId: message.conversation.id,
            count: 1, // This should be calculated properly
          }));
        }
      } catch (error) {
        console.error('Error handling new message:', error);
      }
    };

    // Handle message saved (optimistic update confirmation)
    const handleMessageSaved = ({ tempId, message }) => {
      dispatch(removePendingMessage({
        conversationId: message.conversation.id,
        tempId,
      }));
      
      dispatch(addMessage({
        conversationId: message.conversation.id,
        message,
      }));
    };

    // Handle message read
    const handleMessageRead = (message) => {
      dispatch(updateMessage({
        conversationId: message.conversation.id,
        messageId: message.id,
        updates: { readAt: message.readAt },
      }));
    };

    // Handle message delivered
    const handleMessageDelivered = (message) => {
      dispatch(updateMessage({
        conversationId: message.conversation.id,
        messageId: message.id,
        updates: { deliveredAt: message.deliveredAt },
      }));
    };

    // Handle typing indicators
    const handleUserTyping = (user) => {
      dispatch(setUserTyping({
        conversationId: chatState.activeConversationId,
        user,
      }));
    };

    const handleUserStoppedTyping = (user) => {
      dispatch(removeUserTyping({
        conversationId: chatState.activeConversationId,
        userId: user.id,
      }));
    };

    // Bind socket events
    socket.on('new-chat-message', handleNewMessage);
    socket.on('chat-message-saved', handleMessageSaved);
    socket.on('chat-message-read', handleMessageRead);
    socket.on('chat-message-delivered', handleMessageDelivered);
    socket.on('chat-user-typing', handleUserTyping);
    socket.on('chat-user-stopped-typing', handleUserStoppedTyping);

    // Cleanup
    return () => {
      socket.off('new-chat-message', handleNewMessage);
      socket.off('chat-message-saved', handleMessageSaved);
      socket.off('chat-message-read', handleMessageRead);
      socket.off('chat-message-delivered', handleMessageDelivered);
      socket.off('chat-user-typing', handleUserTyping);
      socket.off('chat-user-stopped-typing', handleUserStoppedTyping);
    };
  }, [socket, currentUser, dispatch, chatState.activeConversationId]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      Object.values(typingTimeoutRef.current).forEach(clearTimeout);
    };
  }, []);

  return {
    // State
    ...chatState,
    connected,
    
    // Actions
    loadConversations,
    loadMessages,
    loadMoreMessagesForConversation,
    searchChats,
    sendMessage,
    markMessagesAsRead,
    setActiveConversationId,
    handleTyping,
    stopTyping,
    getUnreadCount,
  };
};
