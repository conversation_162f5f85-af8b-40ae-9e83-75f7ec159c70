import { getErrorResponse } from "../middlewares/errorHandler";
import { createChatGroup, getAndCreateChatUser } from "../services/agora";
import Classroom, {
  CLASSROOM_VISIBILITY,
  IClassroom,
} from "../models/classroom";
import { ITutor } from "../models/tutor";
import Student, { IStudent } from "../models/student";
import mongoose from "mongoose";
import { isValidObjectId } from "../utils/validation";
import { STATUS_CODES } from "../config/constants";

export const CLASSROOM_STATUS_CODES = {
  403: "CLASSROOM_ACCESS_DENIED",
};

type TClassroom = {
  chatGroup: {
    name: string;
    desc: string;
    maxUsers: number;
  };
  visibility: IClassroom["visibility"];
  expireAt?: Date | null;
  //   students: string[];
};

export const createClassroomHook = async (
  classroomPayload: TClassroom,
  tutorProfile: ITutor,
  student?: IStudent | mongoose.Types.ObjectId | string
): Promise<IClassroom> => {
  const { chatGroup: bodyChatGroup, visibility, expireAt } = classroomPayload;

  if (tutorProfile.approvalStatus !== "approved") {
    throw getErrorResponse(
      {
        message:
          "Classroom can't be created because the tutor's account hasn't been approved.",
        code: CLASSROOM_STATUS_CODES["403"],
        details: {
          code: STATUS_CODES["TUTOR_NOT_APPROVED"],
        },
      },
      403
    );
  }

  if (visibility !== undefined && !CLASSROOM_VISIBILITY.includes(visibility))
    throw getErrorResponse(
      {
        message: "Invalid classroom visibility",
        details: {
          allowedVisibility: CLASSROOM_VISIBILITY,
        },
      },
      400
    );

  const username = tutorProfile.username;

  let studentProfile;

  if (student) {
    if (typeof student !== "string" && "email" in student)
      studentProfile = student;
    else {
      if (!isValidObjectId(student))
        throw getErrorResponse("Invalid student id", 400);

      studentProfile = await Student.findById(student);

      if (!studentProfile) throw getErrorResponse("Student not found", 404);
    }

    await getAndCreateChatUser({
      username: studentProfile.username,
    });
  }

  await getAndCreateChatUser({
    username: tutorProfile.username,
  });

  // const channelId = await generateChannelId();

  const members = [tutorProfile.username];

  if (studentProfile) members.push(studentProfile.username);

  const chatGroup = await createChatGroup({
    groupname: bodyChatGroup.name,
    desc: bodyChatGroup.desc,
    members,
    maxusers: bodyChatGroup.maxUsers || 2,
    public: visibility !== "private",
    owner: username,
  });

  const students = [];

  if (studentProfile) students.push(studentProfile.id.toString());

  const updates: Record<string, any> = {
    tutor: tutorProfile.id,
    visibility,
    students,
    // channelId,
    chatGroup,
    // chatUser,
  };

  if (expireAt && !isNaN(new Date(expireAt).getTime())) {
    updates.expireAt = new Date(expireAt);
  } else {
    updates.$unset = { expireAt: 1 };
  }

  let classroom = new Classroom(updates);

  classroom = await classroom.save();

  classroom = await classroom.populate("tutor");

  return classroom;
};
