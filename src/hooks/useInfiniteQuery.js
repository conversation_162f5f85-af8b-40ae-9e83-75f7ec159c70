import { useCallback, useRef, useState } from "react";
import { useQuery } from "@tanstack/react-query";

const useInfiniteQuery = (query) => {
  const [data, setData] = useState(undefined);

  const [error, setError] = useState(false);

  const [refetching, setRefetching] = useState(false);

  const stateRef = useRef({
    hasfetchedInit: false,
    pagination: undefined,
    hasCacheData: false,
  });

  const queryFn = useCallback(async () => {
    try {
      if (!query.enabled) return;

      setRefetching(!!stateRef.current.pagination);

      const res = await query.queryFn(
        stateRef.current.pagination,
        query.searchTerm
      );

      stateRef.current.pagination = res.details.pagination;

      const withEmpty = !query.searchTerm || !stateRef.current.hasEmptyData;

      if (withEmpty) {
        stateRef.current.hasEmptyData = false;
      }

      stateRef.current.hasCacheData = !!stateRef.current.withSearchTerm;

      setData((data = []) =>
        query.backward
          ? [...res.data, ...(withEmpty ? [] : data)]
          : [...(withEmpty ? [] : data), ...res.data]
      );
      return res.details.pagination.hasMore;
    } catch (err) {
      setError(true);

      setData([]);

      if (!stateRef.current.pagination) throw err;
      return false;
    } finally {
      setRefetching(false);
    }
  }, [query]);

  const api = useQuery({
    queryKey: query.queryKey,
    queryFn,
    enabled: query.enabled,
  });

  const isFetching = stateRef.current.pagination ? false : api.isFetching;

  const isRefetching = api.isRefetching || refetching;

  if (!stateRef.current.hasfetchedInit && !isFetching)
    stateRef.current.hasfetchedInit = true;

  const isPending = isFetching || isRefetching;

  if (query.searchTerm) stateRef.current.withSearchTerm = true;
  else if (!isPending) stateRef.current.withSearchTerm = false;

  const isSearching = !!(stateRef.current.withSearchTerm && isPending);

  return {
    ...api,
    ...stateRef.current,
    data,
    isFetching,
    isSearching,
    hasMore: stateRef.current.pagination?.hasMore || false,
    isError: api.isError || error,
    isRefetching,
    isPending,

    fetchNextPage: useCallback(async () => {
      try {
        return await queryFn();
      } catch (err) {
        if (err) return false;
      }
    }, [queryFn]),
  };
};

export default useInfiniteQuery;
