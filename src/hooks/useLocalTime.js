import { getLocalTime } from "@/utils/date";
import { useState, useEffect } from "react";

/**
 * Hook to localize a given datetime in a specific timezone.
 * Updates every minute.
 *
 * @param {string} datetime - ISO string or any parseable date
 * @param {string} timezone - IANA timezone like "Asia/Tokyo"
 */

const useLocalTime = (timezone, datetime) => {
  const [localTime, setLocalTime] = useState(getLocalTime(timezone, datetime));

  useEffect(() => {
    const interval = setInterval(() => {
      setLocalTime(getLocalTime(timezone, datetime));
    }, 60 * 1000); // update every minute

    return () => clearInterval(interval);
  }, [datetime, timezone]);

  return localTime;
};

export default useLocalTime;
