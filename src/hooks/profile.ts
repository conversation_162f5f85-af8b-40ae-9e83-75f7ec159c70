import { Request, Response } from "express";
import { createOkResponse, mapToIdRecords } from "../utils/misc";
import { Model } from "mongoose";
import { IStudent } from "../models/student";
import { ITutor } from "../models/tutor";
import {
  createErrorResponse,
  getErrorResponse,
} from "../middlewares/errorHandler";
import {
  getProfileModelByRole,
  updateProfileMedias,
  UPLOAD_ERROR_DETAILS,
  USER_PROFILE_TYPE,
} from "../utils/profile";
import Admin, { IAdmin } from "../models/admin";
import { sendMail } from "../utils/mailer";
import {
  approveTutorTemplate,
  guideToFindTutorTemplate,
} from "../utils/emails-templates/email-templates";
import { logError } from "../utils/logger";
import { KeyValuePair } from "../types/misc";
import { comparePassword, hashPassword } from "../utils/hashing";
import { sendTutorAwaitingApprovalMail } from "../services/emailNotificationService";

export type PROFILE_MODEL = Model<ITutor> | Model<IStudent> | Model<IAdmin>;

export type SINGLE_PROFILE_MODEL = Model<ITutor | IStudent | IAdmin>;

export function getProfileById(
  Profile: PROFILE_MODEL
): (req: Request, res: Response) => Promise<void> {
  return async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const profile = await (Profile as SINGLE_PROFILE_MODEL).findOne({
        _id: id,
      });

      if (!profile) {
        createErrorResponse(res, `Profile not found.`, 404);
        return;
      }

      createOkResponse(res, { data: profile });
    } catch (error: any) {
      createErrorResponse(res, error, 500);
    }
  };
}

const isComplete = (profile: USER_PROFILE_TYPE) => {
  if (
    profile.firstname &&
    profile.lastname &&
    profile.email &&
    profile.aboutMe &&
    profile.role &&
    profile.phoneno &&
    profile.countryOfBirth &&
    profile.countryOfResidence &&
    profile.nativeLanguage &&
    profile.timeAvailable.length &&
    profile.daysAvailable.length &&
    profile.image
  ) {
    if (profile.role === "tutor") {
      if (
        profile.teachingSubjects.length &&
        profile.academics.length &&
        profile.teachingExperience &&
        profile.motivatePotentialStudent &&
        profile.headline &&
        profile.introVideo &&
        profile.basePrice
      ) {
        return true;
      }
    } else if (profile.role === "student") {
      if (profile.learningReasons.length && profile.skillsToImprove.length) {
        return true;
      }
    }
  }
  return false;
};

export const updateProfileHook = async (
  Profile: PROFILE_MODEL,
  updates: KeyValuePair,
  user: USER_PROFILE_TYPE,
  withGenericData = false
): Promise<{
  profile: USER_PROFILE_TYPE;
  errorDetails: UPLOAD_ERROR_DETAILS;
}> => {
  const errorDetails = await updateProfileMedias(updates, user);

  if (isComplete({ ...user.toObject(), ...updates } as USER_PROFILE_TYPE))
    updates.onBoardingStatus = "complete";

  if (withGenericData) {
    mapToIdRecords(updates, "languages");

    mapToIdRecords(updates, "timeAvailable");
  }

  if (!withGenericData) {
    delete updates.firstname;
    delete updates.lastname;
    delete updates.email;
  }

  delete updates.role;
  delete updates.provider;
  delete updates.password;
  delete updates.createdAt;
  delete updates.updatedAt;

  const profile = await (Profile as SINGLE_PROFILE_MODEL).findByIdAndUpdate(
    user._id,
    updates,
    {
      new: true,
      runValidators: true,
    }
  );

  if (!profile) throw getErrorResponse(`Profile not found.`, 404);

  return { profile, errorDetails };
};

export const onBoardUser = async (req: Request, res: Response) => {
  try {
    const Profile = getProfileModelByRole(req.body.role);

    const userId = req.body.userId;

    const user = req.user;

    if (!userId) {
      createErrorResponse(res, "body.userId is required");
      return;
    }

    const updates = { ...req.body, onBoardingStatus: "ongoing" };

    if (user.onBoardingStatus === "complete") {
      createErrorResponse(res, "Profile onboarding complete", 401);
      return;
    }

    const { profile, errorDetails } = await updateProfileHook(
      Profile,
      updates,
      req.user
    );

    if (profile.onBoardingStatus === "complete") {
      if (profile.role === "tutor")
        await sendTutorAwaitingApprovalMail(profile);
    } else if (user.onBoardingStatus === "new") {
      if (profile.role === "student") {
        try {
          await sendMail(profile.email, guideToFindTutorTemplate(profile));
        } catch (err: any) {
          logError(`Failed to send onboarding mail`, "mail", {
            email: profile.email,
            role: profile.role,
          });
        }
      }
    }

    createOkResponse(res, {
      details: {
        errorDetails,
      },
      success: !errorDetails,
      message: `Profile updated successfully.`,
      data: profile,
    });
  } catch (error: any) {
    createErrorResponse(res, error);
  }
};

export const deleteProfileHook = async (req: Request, res: Response) => {
  try {
    await req.user.deleteOne();

    createOkResponse(res, "Profile deleted successfully");

    return;
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};

export const updatePassword = async (req: Request, res: Response) => {
  try {
    if (
      !req.body.currentPassword ||
      !(await comparePassword(req.body.currentPassword, req.user.password))
    ) {
      createErrorResponse(res, "Current password mismatch");
      return;
    }

    if (!req.body.newPassword) {
      createErrorResponse(res, "Invalid body, newPassword field is required");
      return;
    }

    const user: USER_PROFILE_TYPE = req.user;

    user.password = await hashPassword(req.body.newPassword);

    await user.save({ validateModifiedOnly: true });

    createOkResponse(res, "Password updated successfully.");

    return;
  } catch (err: any) {
    createErrorResponse(res, err);
    return;
  }
};
