// hooks/useDebounce.js
import { useEffect, useState } from "react";

export const useDebounce = (value, delay, cb) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
      cb?.(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay, cb]);

  return debouncedValue;
};
