# Admin Financial Management System

## Overview

The admin financial management system provides comprehensive tracking and control over all financial aspects of the platform, including payments, subscriptions, escrow transactions, refunds, and tutor payouts.

## Features

### 1. Financial Overview Dashboard
- **Endpoint**: `GET /api/admin/financial/overview`
- **Description**: Provides a comprehensive overview of platform finances
- **Returns**:
  - Total revenue (all-time and monthly)
  - Transaction counts and summaries
  - Pending tutor payouts
  - Escrow balance
  - Refunded amounts
  - Platform fees collected
  - Active subscription count
  - Failed payment statistics
  - Recent transactions
  - Payment method breakdown

### 2. Transaction Management

#### Get All Transactions
- **Endpoint**: `GET /api/admin/financial/transactions`
- **Query Parameters**:
  - `page` (default: 1)
  - `limit` (default: 20)
  - `type` (subscription_payment, lesson_payout, refund, fee)
  - `status` (pending, completed, failed)
  - `userId` (filter by specific user)
  - `startDate` / `endDate` (date range filter)
  - `sortBy` (default: createdAt)
  - `sortOrder` (asc/desc, default: desc)

#### Get Transaction Details
- **Endpoint**: `GET /api/admin/financial/transactions/:transactionId`
- **Description**: Get detailed information about a specific transaction

### 3. Escrow Management

#### Get All Escrow Transactions
- **Endpoint**: `GET /api/admin/financial/escrow`
- **Query Parameters**:
  - `page`, `limit` (pagination)
  - `status` (held, released, refunded, disputed)
  - `tutorId`, `studentId` (filter by user)
  - `sortBy`, `sortOrder`

#### Release Escrow Funds
- **Endpoint**: `PATCH /api/admin/financial/escrow/:escrowId/release`
- **Body**: `{ "reason": "Lesson completed successfully" }`
- **Description**: Release held escrow funds to tutor and create payout transaction

### 4. Refund Management

#### Process Refund
- **Endpoint**: `POST /api/admin/financial/refunds/:subscriptionId`
- **Body**:
  ```json
  {
    "amount": 50.00,
    "reason": "Customer dissatisfaction",
    "refundType": "partial" // or "full"
  }
  ```
- **Description**: Process refunds for subscriptions

### 5. Financial Reports

#### Generate Financial Reports
- **Endpoint**: `GET /api/admin/financial/reports`
- **Query Parameters**:
  - `reportType` (monthly, custom)
  - `startDate` / `endDate` (for custom reports)
  - `year` (for monthly breakdown, default: current year)
- **Returns**:
  - Revenue by month
  - Transaction breakdown by type
  - Subscription metrics
  - Tutor payout summaries
  - Refund analysis

### 6. Payment Analytics

#### Get Payment Method Analytics
- **Endpoint**: `GET /api/admin/financial/payment-analytics`
- **Returns**:
  - Subscription breakdown by plan type
  - Payment failure rates
  - Average subscription values
  - Churn analysis

### 7. Tutor Payout Management

#### Get Tutor Payout Summary
- **Endpoint**: `GET /api/admin/financial/tutor-payouts`
- **Query Parameters**:
  - `page`, `limit` (pagination)
  - `tutorId` (filter by specific tutor)
  - `status` (pending, completed, failed, or 'all')
  - `startDate` / `endDate` (date range)
- **Returns**:
  - Payout transactions
  - Summary by status
  - Top earning tutors
  - Pagination info

## Security & Permissions

All financial management endpoints require:
- Admin authentication (`requireAdmin()` middleware)
- Activity logging for audit trails
- Request body validation where applicable

## Data Models

### Transaction Model
```typescript
{
  userId: ObjectId,
  amount: Number, // in cents
  type: 'subscription_payment' | 'lesson_payout' | 'refund' | 'fee',
  status: 'pending' | 'completed' | 'failed',
  stripeTransactionId?: String,
  description?: String,
  createdAt: Date
}
```

### Escrow Model
```typescript
{
  lessonId: ObjectId,
  studentId: ObjectId,
  tutorId: ObjectId,
  amountHeld: Number, // in cents
  platformFee: Number, // 20% commission in cents
  tutorPayout: Number, // amount after commission
  stripePaymentIntentId?: String,
  status: 'held' | 'released' | 'refunded' | 'disputed',
  releasedAt?: Date,
  createdAt: Date
}
```

## Usage Examples

### Get Financial Overview
```javascript
const response = await fetch('/api/admin/financial/overview', {
  headers: { 'Authorization': 'Bearer <admin-token>' }
});
const data = await response.json();
```

### Process a Refund
```javascript
const response = await fetch('/api/admin/financial/refunds/subscription123', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer <admin-token>',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    amount: 25.00,
    reason: 'Service not as expected',
    refundType: 'partial'
  })
});
```

### Release Escrow Funds
```javascript
const response = await fetch('/api/admin/financial/escrow/escrow123/release', {
  method: 'PATCH',
  headers: {
    'Authorization': 'Bearer <admin-token>',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    reason: 'Lesson completed successfully'
  })
});
```

## Monitoring & Logging

All financial operations are logged with:
- Admin user information
- Action performed
- Timestamp
- Success/failure status
- Request details
- Response status

Logs are stored in `src/logs/error-log.json` and console output for immediate visibility.

## Error Handling

All endpoints include comprehensive error handling with:
- Input validation
- Database error handling
- Proper HTTP status codes
- Detailed error messages
- Security considerations (no sensitive data exposure)
