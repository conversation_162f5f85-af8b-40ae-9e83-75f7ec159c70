# Booking Reschedule, Cancel, and Tutor Availability API Documentation

This document describes the new booking management endpoints that allow students and tutors to reschedule bookings, cancel bookings, and view tutor availability.

## 🚀 **New Endpoints**

### 1. **Reschedule Booking**
- **Endpoint**: `PUT /api/bookings/reschedule/:lessonId`
- **Authentication**: Required (Student, Tutor, or Admin)
- **Description**: Reschedule an existing lesson to a new date and time

### 2. **Cancel Booking**
- **Endpoint**: `PUT /api/bookings/cancel/:lessonId`
- **Authentication**: Required (Student, Tutor, or Admin)
- **Description**: Cancel an existing lesson with a required reason

### 3. **Get Tutor Booked Slots**
- **Endpoint**: `GET /api/bookings/tutor-booked-slots/:tutorId`
- **Authentication**: Required (Student only)
- **Description**: View tutor's booked time slots (date and time only) to see when they're unavailable

---

## 📋 **API Details**

### **1. Reschedule Booking**

**Request:**
```http
PUT /api/bookings/reschedule/:lessonId
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "newStartDateTime": "2024-01-20T15:00:00.000Z",
  "newEndDateTime": "2024-01-20T16:00:00.000Z",
  "reason": "Student requested different time due to schedule conflict"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Lesson rescheduled successfully",
  "data": {
    "lesson": {
      "id": "lesson_id",
      "oldScheduledTime": "2024-01-15T14:00:00.000Z",
      "newScheduledTime": "2024-01-20T15:00:00.000Z",
      "duration": 60,
      "status": "scheduled",
      "tutor": {
        "id": "tutor_id",
        "name": "John Smith",
        "email": "<EMAIL>"
      },
      "student": {
        "id": "student_id",
        "name": "Jane Doe",
        "email": "<EMAIL>"
      }
    }
  }
}
```

### **2. Cancel Booking**

**Request:**
```http
PUT /api/bookings/cancel/:lessonId
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "reason": "Student is sick and cannot attend the lesson"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Lesson cancelled successfully",
  "data": {
    "lesson": {
      "id": "lesson_id",
      "scheduledTime": "2024-01-15T14:00:00.000Z",
      "status": "cancelled",
      "cancelledAt": "2024-01-10T10:30:00.000Z",
      "cancellationReason": "Student is sick and cannot attend the lesson",
      "tutor": {
        "id": "tutor_id",
        "name": "John Smith",
        "email": "<EMAIL>"
      },
      "student": {
        "id": "student_id",
        "name": "Jane Doe",
        "email": "<EMAIL>"
      }
    },
    "refund": {
      "creditedBack": true,
      "newRemainingLessons": 5
    }
  }
}
```

### **3. Get Tutor Booked Slots**

**Request:**
```http
GET /api/bookings/tutor-booked-slots/:tutorId?startDate=2024-01-15&endDate=2024-01-30&timezone=UTC
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:**
```json
{
  "success": true,
  "data": {
    "tutor": {
      "id": "tutor_id",
      "name": "John Smith",
      "email": "<EMAIL>"
    },
    "searchPeriod": {
      "startDate": "2024-01-15T00:00:00.000Z",
      "endDate": "2024-01-30T23:59:59.999Z",
      "timezone": "UTC"
    },
    "bookedSlots": [
      {
        "startDateTime": "2024-01-15T14:00:00.000Z",
        "endDateTime": "2024-01-15T15:00:00.000Z",
        "duration": 60,
        "status": "scheduled",
        "type": "lesson",
        "dayOfWeek": "Monday",
        "formattedDate": "January 15, 2024",
        "formattedStartTime": "02:00 PM",
        "formattedEndTime": "03:00 PM",
        "timezone": "UTC"
      }
    ],
    "slotsByDate": {
      "2024-01-15": [
        {
          "startDateTime": "2024-01-15T14:00:00.000Z",
          "endDateTime": "2024-01-15T15:00:00.000Z",
          "duration": 60,
          "status": "scheduled",
          "type": "lesson",
          "dayOfWeek": "Monday",
          "formattedDate": "January 15, 2024",
          "formattedStartTime": "02:00 PM",
          "formattedEndTime": "03:00 PM",
          "timezone": "UTC"
        }
      ]
    },
    "summary": {
      "totalBookedSlots": 1,
      "lessonSlots": 1,
      "calendarSlots": 0,
      "daysWithBookings": 1,
      "nextBookedSlot": {
        "startDateTime": "2024-01-15T14:00:00.000Z",
        "endDateTime": "2024-01-15T15:00:00.000Z"
      }
    }
  }
}
```

---

## 🔒 **Authentication & Authorization**

### **Reschedule Booking**
- **Students**: Can reschedule their own lessons
- **Tutors**: Can reschedule lessons they're teaching
- **Admins**: Can reschedule any lesson

### **Cancel Booking**
- **Students**: Can cancel their own lessons
- **Tutors**: Can cancel lessons they're teaching
- **Admins**: Can cancel any lesson

### **Get Tutor Booked Slots**
- **Students**: Can view any tutor's booked slots
- **Tutors/Admins**: Not allowed (403 Forbidden)

---

## ⚠️ **Business Rules**

### **Reschedule Rules**
1. Only lessons with status `scheduled` or `confirmed` can be rescheduled
2. Cannot reschedule to a time in the past
3. New time slot must not conflict with existing bookings
4. Both tutor and student calendars are checked for conflicts

### **Cancel Rules**
1. Only lessons with status `scheduled` or `confirmed` can be cancelled
2. Cancellation reason is required
3. Lessons can only be cancelled at least 2 hours in advance (except by admins)
4. Cancelled lessons are credited back to the subscription (if applicable)

### **Email Notifications**
- Both reschedule and cancel operations send email notifications to both student and tutor
- Notifications include the new time (for reschedule) or cancellation reason (for cancel)

---

## 🧪 **Testing with Postman**

### **Test Reschedule Booking**
1. Create a booking first using the enhanced booking endpoint
2. Use the lesson ID from the response
3. Send PUT request to `/api/bookings/reschedule/:lessonId` with new date/time
4. Verify the lesson is updated and notifications are sent

### **Test Cancel Booking**
1. Create a booking first using the enhanced booking endpoint
2. Use the lesson ID from the response
3. Send PUT request to `/api/bookings/cancel/:lessonId` with cancellation reason
4. Verify the lesson is cancelled and credited back to subscription

### **Test Get Tutor Booked Slots**
1. Ensure there are some existing bookings for a tutor
2. Send GET request to `/api/bookings/tutor-booked-slots/:tutorId`
3. Verify only date/time information is returned (no personal details)

---

## 📝 **Error Handling**

All endpoints return appropriate HTTP status codes:
- `400`: Bad Request (invalid data, validation errors)
- `401`: Unauthorized (missing or invalid token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found (lesson/tutor not found)
- `409`: Conflict (time slot conflicts)
- `500`: Internal Server Error

Error responses follow this format:
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message (in development)"
}
```
