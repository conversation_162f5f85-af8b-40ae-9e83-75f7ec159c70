{"info": {"name": "Convolly Booking API Tests", "description": "Postman collection for testing booking creation endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "auth_token", "value": "your_jwt_token_here", "type": "string"}, {"key": "tutor_id", "value": "60f7b3b3b3b3b3b3b3b3b3b3", "type": "string"}, {"key": "student_id", "value": "60f7b3b3b3b3b3b3b3b3b3b4", "type": "string"}, {"key": "event_id", "value": "60f7b3b3b3b3b3b3b3b3b3b5", "type": "string"}], "item": [{"name": "Legacy Booking", "item": [{"name": "Create Simple Booking", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"eventId\": \"{{event_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/bookings", "host": ["{{base_url}}"], "path": ["api", "bookings"]}, "description": "Creates a simple booking for an existing event. Requires:\n- User must be authenticated as 'learner' role\n- eventId: Valid MongoDB ObjectId of existing event\n\nResponse: Returns booking object with status 'pending'"}}]}, {"name": "Enhanced Booking", "item": [{"name": "Create Enhanced Booking (Student)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"tutorId\": \"{{tutor_id}}\",\n  \"title\": \"Mathematics Tutoring Session\",\n  \"description\": \"Algebra and calculus review session\",\n  \"startDateTime\": \"2025-07-15T10:00:00.000Z\",\n  \"endDateTime\": \"2025-07-15T11:00:00.000Z\",\n  \"subject\": \"Mathematics\"\n}"}, "url": {"raw": "{{base_url}}/api/bookings/enhanced", "host": ["{{base_url}}"], "path": ["api", "bookings", "enhanced"]}, "description": "Creates an enhanced booking with calendar integration. When authenticated as student:\n\nRequired fields:\n- tutorId: Valid MongoDB ObjectId\n- title: Session title\n- startDateTime: ISO 8601 datetime string (future date)\n- endDateTime: ISO 8601 datetime string (after startDateTime)\n\nOptional fields:\n- description: Session description\n- subject: Subject being taught\n\nFeatures:\n- Creates events in both tutor and student calendars\n- Checks subscription/free trial eligibility\n- Sends email notifications\n- Creates classroom for the session\n- <PERSON>les conflict detection"}}, {"name": "Create Enhanced Booking (Admin with Student ID)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"tutorId\": \"{{tutor_id}}\",\n  \"studentId\": \"{{student_id}}\",\n  \"title\": \"Physics Tutoring Session\",\n  \"description\": \"Quantum mechanics and thermodynamics\",\n  \"startDateTime\": \"2025-07-16T14:00:00.000Z\",\n  \"endDateTime\": \"2025-07-16T15:30:00.000Z\",\n  \"subject\": \"Physics\"\n}"}, "url": {"raw": "{{base_url}}/api/bookings/enhanced", "host": ["{{base_url}}"], "path": ["api", "bookings", "enhanced"]}, "description": "Creates an enhanced booking specifying a specific student (admin/tutor use case):\n\nRequired fields:\n- tutorId: Valid MongoDB ObjectId\n- studentId: Valid MongoDB ObjectId (when not using authenticated student)\n- title: Session title\n- startDateTime: ISO 8601 datetime string (future date)\n- endDateTime: ISO 8601 datetime string (after startDateTime)\n\nOptional fields:\n- description: Session description\n- subject: Subject being taught\n\nNote: When studentId is provided, the system uses that student instead of the authenticated user."}}, {"name": "Create Enhanced Booking (Free Trial)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"tutorId\": \"{{tutor_id}}\",\n  \"title\": \"Free Trial - English Conversation\",\n  \"description\": \"Introductory English conversation session\",\n  \"startDateTime\": \"2025-07-17T09:00:00.000Z\",\n  \"endDateTime\": \"2025-07-17T10:00:00.000Z\",\n  \"subject\": \"English\"\n}"}, "url": {"raw": "{{base_url}}/api/bookings/enhanced", "host": ["{{base_url}}"], "path": ["api", "bookings", "enhanced"]}, "description": "Creates a free trial booking. The system automatically detects if the student is eligible for a free trial and marks the lesson accordingly.\n\nThe response will include:\n- isFreeTrial: true/false\n- freeTrialLessonsRemaining: number of free trials left\n- subscriptionStatus: current subscription status"}}]}, {"name": "Test Data Examples", "item": [{"name": "Example: Weekend Booking (Should Fail)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"tutorId\": \"{{tutor_id}}\",\n  \"title\": \"Weekend Session\",\n  \"description\": \"This should fail as weekends are not available\",\n  \"startDateTime\": \"2025-07-13T10:00:00.000Z\",\n  \"endDateTime\": \"2025-07-13T11:00:00.000Z\",\n  \"subject\": \"Mathematics\"\n}"}, "url": {"raw": "{{base_url}}/api/bookings/enhanced", "host": ["{{base_url}}"], "path": ["api", "bookings", "enhanced"]}, "description": "Example of a booking that should fail because it's scheduled for a weekend (Saturday/Sunday are typically not available)."}}, {"name": "Example: Past Date Booking (Should Fail)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"tutorId\": \"{{tutor_id}}\",\n  \"title\": \"Past Date Session\",\n  \"description\": \"This should fail as it's in the past\",\n  \"startDateTime\": \"2024-01-01T10:00:00.000Z\",\n  \"endDateTime\": \"2024-01-01T11:00:00.000Z\",\n  \"subject\": \"Mathematics\"\n}"}, "url": {"raw": "{{base_url}}/api/bookings/enhanced", "host": ["{{base_url}}"], "path": ["api", "bookings", "enhanced"]}, "description": "Example of a booking that should fail because it's scheduled for a past date."}}]}]}