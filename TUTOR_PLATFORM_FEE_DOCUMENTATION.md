# Tutor Platform Fee Implementation (20%)

This document describes the implementation of a 20% platform fee charged to tutors for each successful lesson.

## 📊 **Overview**

The platform charges tutors a **20% fee** on each successful lesson completion. This means:
- **Tutor receives**: 80% of the lesson price
- **Platform receives**: 20% of the lesson price

## 💰 **Fee Calculation**

### **Formula:**
```
Lesson Price = Tutor's Base Price per Lesson
Platform Fee = Lesson Price × 20%
Tutor Earnings = Lesson Price - Platform Fee
```

### **Example:**
- Tutor Base Price: $50/lesson
- Platform Fee: $50 × 20% = $10
- Tutor Earnings: $50 - $10 = $40

## 🔧 **Implementation Details**

### **1. Automatic Fee Application**
The 20% platform fee is automatically applied when:
- A lesson is marked as "completed" by the tutor
- The session completion service processes completed sessions
- Manual lesson completion through admin tools

### **2. Transaction Recording**
Every lesson completion creates multiple transaction records:
- **Lesson Payment**: Records the full lesson amount
- **Platform Fee**: Records the 20% fee deduction
- **Tutor Payout**: Records the 80% earnings for the tutor

### **3. Balance Updates**
- Tutor's `availableBalance` is updated with net earnings (80%)
- Platform fee is tracked separately for accounting

## 🛠 **Technical Implementation**

### **Modified Functions:**

#### **1. `updateLessonStatus` in TutorController**
```typescript
// Calculate lesson price and platform fee (20%)
const lessonPriceInCents = Math.round(tutor.basePrice * 100);
const platformFeeRate = 0.20; // 20% platform fee
const platformFeeAmount = Math.round(lessonPriceInCents * platformFeeRate);
const tutorEarnings = lessonPriceInCents - platformFeeAmount; // 80% for tutor

// Update tutor's earnings (after platform fee)
tutor.availableBalance += tutorEarnings;
```

#### **2. `TransactionService.recordLessonPayment`**
```typescript
// Calculate platform fee (20%)
const platformFeeAmount = Math.round(amount * 0.20);
const tutorEarnings = amount - platformFeeAmount;

// Create payment transaction for student
const paymentTransaction = await this.createTransaction({
  userId: studentId,
  relatedUserId: tutorId,
  amount,
  type: 'lesson_payment',
  platformFeeAmount,
  // ...
});
```

#### **3. Session Completion Service**
```typescript
// Calculate platform fee (20%)
const platformFeeRate = 0.20;
const platformFee = Math.round(lessonPrice * platformFeeRate * 100);
const tutorEarnings = Math.round(lessonPrice * (1 - platformFeeRate) * 100);
```

## 📋 **API Endpoints**

### **1. Lesson Status Update**
**PATCH** `/api/tutor/lessons/:lessonId/status`

Updates lesson status and applies platform fee when marked as "completed".

#### **Request:**
```json
{
  "status": "completed",
  "notes": "Great lesson!"
}
```

#### **Response:**
```json
{
  "success": true,
  "message": "Lesson status updated successfully",
  "data": {
    "id": "lesson_id",
    "status": "completed",
    "confirmedAt": "2024-01-15T14:00:00.000Z"
  }
}
```

### **2. Detailed Earnings Breakdown**
**GET** `/api/tutor/earnings-detailed`

Shows detailed earnings with platform fee breakdown.

#### **Query Parameters:**
- `startDate` (optional): Filter from date
- `endDate` (optional): Filter to date
- `page` (optional): Page number (default: 1)
- `limit` (optional): Results per page (default: 20)

#### **Response:**
```json
{
  "success": true,
  "data": {
    "tutor": {
      "id": "tutor_id",
      "basePrice": 50,
      "availableBalance": 400.00,
      "totalLessons": 12
    },
    "earnings": {
      "summary": {
        "totalCompletedLessons": 10,
        "totalGrossEarnings": 500.00,
        "totalPlatformFees": 100.00,
        "totalNetEarnings": 400.00,
        "platformFeeRate": 20,
        "currency": "USD"
      },
      "lessons": [
        {
          "id": "lesson_id",
          "title": "Math Tutoring",
          "scheduledTime": "2024-01-15T14:00:00.000Z",
          "confirmedAt": "2024-01-15T15:00:00.000Z",
          "student": {
            "id": "student_id",
            "name": "John Doe",
            "email": "<EMAIL>"
          },
          "earnings": {
            "grossAmount": 50.00,
            "platformFeeRate": 20,
            "platformFeeAmount": 10.00,
            "netEarnings": 40.00,
            "currency": "USD"
          }
        }
      ]
    },
    "platformFeeInfo": {
      "rate": 20,
      "description": "Platform fee charged on each successful lesson",
      "calculation": "Net Earnings = Gross Amount - (Gross Amount × 20%)",
      "example": {
        "lessonPrice": 50,
        "platformFee": 10,
        "tutorEarnings": 40
      }
    }
  }
}
```

## 📊 **Transaction Types**

### **1. Lesson Payment**
- **Type**: `lesson_payment`
- **Category**: `payment`
- **Amount**: Full lesson price
- **Description**: Payment from student for lesson

### **2. Platform Fee**
- **Type**: `platform_fee`
- **Category**: `fee`
- **Amount**: 20% of lesson price
- **Description**: Platform fee deduction

### **3. Tutor Payout**
- **Type**: `lesson_payout`
- **Category**: `payout`
- **Amount**: 80% of lesson price (net earnings)
- **Description**: Tutor earnings after platform fee

## 🔍 **Monitoring & Analytics**

### **Platform Fee Tracking**
All platform fees are tracked in the transaction system with:
- Individual lesson fee amounts
- Aggregate fee totals
- Fee percentage verification
- Audit trail for all deductions

### **Tutor Earnings Transparency**
Tutors can view:
- Gross earnings (before fees)
- Platform fee deductions
- Net earnings (after fees)
- Historical breakdown by lesson
- Transaction history

## 💡 **Key Features**

### **✅ Automatic Fee Application**
- No manual intervention required
- Applied consistently across all lesson completions
- Integrated with existing transaction system

### **✅ Transparent Reporting**
- Clear breakdown of gross vs net earnings
- Detailed fee calculations shown to tutors
- Historical tracking of all fees

### **✅ Audit Trail**
- Every fee deduction is recorded as a transaction
- Full audit trail for accounting purposes
- Reconciliation capabilities

### **✅ Error Handling**
- Graceful handling of transaction recording failures
- Lesson completion doesn't fail if fee recording fails
- Comprehensive error logging

## 🧪 **Testing Scenarios**

### **1. Lesson Completion**
1. Tutor marks lesson as "completed"
2. Verify 20% fee is deducted
3. Verify tutor receives 80% of lesson price
4. Verify transaction records are created

### **2. Earnings Calculation**
1. Complete multiple lessons
2. Verify total earnings = (lesson_price × 0.8) × lesson_count
3. Verify platform fees = (lesson_price × 0.2) × lesson_count

### **3. Balance Updates**
1. Check tutor balance before lesson completion
2. Complete lesson
3. Verify balance increased by net earnings (80%)

### **4. Transaction Recording**
1. Complete lesson
2. Verify three transaction types are created:
   - lesson_payment
   - platform_fee  
   - lesson_payout

## 📈 **Business Impact**

### **Revenue Generation**
- Consistent 20% revenue from all successful lessons
- Automatic collection with no manual processing
- Transparent fee structure for tutors

### **Tutor Incentives**
- Clear understanding of earnings structure
- Transparent fee breakdown
- Predictable income calculation

### **Platform Sustainability**
- Sustainable revenue model
- Covers platform operational costs
- Enables continued platform development

---

## 🚀 **Usage Examples**

### **Tutor Completing a Lesson:**
```bash
curl -X PATCH "http://localhost:3000/api/tutor/lessons/lesson_id/status" \
  -H "Authorization: Bearer tutor-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"status": "completed", "notes": "Great session!"}'
```

### **Viewing Detailed Earnings:**
```bash
curl -X GET "http://localhost:3000/api/tutor/earnings-detailed?page=1&limit=10" \
  -H "Authorization: Bearer tutor-jwt-token"
```

### **Filtering Earnings by Date:**
```bash
curl -X GET "http://localhost:3000/api/tutor/earnings-detailed?startDate=2024-01-01&endDate=2024-01-31" \
  -H "Authorization: Bearer tutor-jwt-token"
```

---

**✅ The 20% platform fee is now fully implemented and integrated across all lesson completion workflows!**
