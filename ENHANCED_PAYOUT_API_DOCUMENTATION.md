# Enhanced Payout API Documentation

## Overview

The enhanced payout system provides comprehensive management of withdrawal requests with improved status handling including `pending`, `failed`, and `paid` statuses. This system allows for better tracking, bulk operations, and detailed analytics.

## Enhanced Status Management

### **Status Types**
- **pending**: Payout is being processed
- **failed**: Payout attempt failed
- **paid**: Payout completed successfully

### **Status Flow**
```
approved → pending → paid/failed
```

## Individual Payout Status Update

### **PATCH** `/api/admin/withdrawals/:requestId/status`

Update the status of a specific withdrawal request with enhanced status handling.

#### **Headers:**
```json
{
  "Authorization": "Bearer ADMIN_JWT_TOKEN",
  "Content-Type": "application/json"
}
```

#### **Request Body:**
```json
{
  "status": "pending|failed|paid",
  "failureReason": "Required for failed status",
  "stripeTransferId": "stripe_transfer_id_123",
  "adminNotes": "Additional notes about the status update"
}
```

#### **Parameters:**
- **status** (required): One of `pending`, `failed`, or `paid`
- **failureReason** (required for failed): Reason why the payout failed
- **stripeTransferId** (optional): Stripe transfer ID for tracking
- **adminNotes** (optional): Additional administrative notes

#### **Response Examples:**

**Pending Status:**
```json
{
  "success": true,
  "message": "Payout status updated to pending successfully",
  "data": {
    "withdrawalRequest": {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
      "status": "approved",
      "adminNotes": "Status updated to pending: Processing initiated"
    },
    "transaction": null,
    "tutorNewBalance": 1250.00,
    "statusUpdate": {
      "previousStatus": "approved",
      "newStatus": "pending",
      "processedBy": "64f8a1b2c3d4e5f6a7b8c9d1",
      "processedAt": "2024-01-20T14:30:00.000Z"
    }
  }
}
```

**Failed Status:**
```json
{
  "success": true,
  "message": "Payout status updated to failed successfully",
  "data": {
    "withdrawalRequest": {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
      "status": "rejected",
      "rejectionReason": "Payout failed: Bank account not found"
    },
    "transaction": {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d2",
      "type": "withdrawal",
      "status": "failed",
      "amount": 50000
    },
    "tutorNewBalance": 1250.00,
    "statusUpdate": { /* status update details */ }
  }
}
```

**Paid Status:**
```json
{
  "success": true,
  "message": "Payout status updated to paid successfully",
  "data": {
    "withdrawalRequest": {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
      "status": "processed",
      "stripeTransferId": "stripe_transfer_id_123"
    },
    "transaction": {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d3",
      "type": "withdrawal",
      "status": "completed",
      "amount": 50000
    },
    "tutorNewBalance": 750.00,
    "statusUpdate": { /* status update details */ }
  }
}
```

## Bulk Payout Status Update

### **POST** `/api/admin/withdrawals/bulk-status-update`

Update the status of multiple withdrawal requests simultaneously.

#### **Headers:**
```json
{
  "Authorization": "Bearer ADMIN_JWT_TOKEN",
  "Content-Type": "application/json"
}
```

#### **Request Body:**
```json
{
  "requestIds": [
    "64f8a1b2c3d4e5f6a7b8c9d0",
    "64f8a1b2c3d4e5f6a7b8c9d1",
    "64f8a1b2c3d4e5f6a7b8c9d2"
  ],
  "status": "paid",
  "failureReason": "Required for failed status",
  "adminNotes": "Bulk payout processing completed"
}
```

#### **Response:**
```json
{
  "success": true,
  "message": "Bulk payout status update completed. 2 successful, 1 failed.",
  "data": {
    "successful": [
      {
        "requestId": "64f8a1b2c3d4e5f6a7b8c9d0",
        "newStatus": "paid",
        "transactionId": "64f8a1b2c3d4e5f6a7b8c9d3"
      },
      {
        "requestId": "64f8a1b2c3d4e5f6a7b8c9d1",
        "newStatus": "paid",
        "transactionId": "64f8a1b2c3d4e5f6a7b8c9d4"
      }
    ],
    "failed": [
      {
        "requestId": "64f8a1b2c3d4e5f6a7b8c9d2",
        "error": "Tutor has insufficient balance for this withdrawal"
      }
    ],
    "totalProcessed": 2
  }
}
```

## Payout Analytics

### **GET** `/api/admin/withdrawals/payout-analytics`

Get comprehensive analytics and insights about payout statuses.

#### **Query Parameters:**
- **startDate** (optional): Filter from date (ISO format)
- **endDate** (optional): Filter to date (ISO format)
- **tutorId** (optional): Filter by specific tutor
- **status** (optional): Filter by status

#### **Response:**
```json
{
  "success": true,
  "data": {
    "statusBreakdown": [
      {
        "status": "pending",
        "count": 15,
        "totalAmount": 7500.00,
        "avgAmount": 500.00
      },
      {
        "status": "processed",
        "count": 45,
        "totalAmount": 22500.00,
        "avgAmount": 500.00
      },
      {
        "status": "rejected",
        "count": 3,
        "totalAmount": 1200.00,
        "avgAmount": 400.00
      }
    ],
    "dailyTrends": [
      {
        "date": "2024-01-20",
        "status": "processed",
        "count": 8,
        "totalAmount": 4000.00
      }
    ],
    "tutorBreakdown": [
      {
        "tutor": {
          "id": "64f8a1b2c3d4e5f6a7b8c9d0",
          "name": "John Doe",
          "email": "<EMAIL>"
        },
        "totalRequests": 12,
        "totalAmount": 6000.00,
        "statusCounts": {
          "pending": 2,
          "approved": 1,
          "processed": 8,
          "rejected": 1
        }
      }
    ],
    "recentActivity": [ /* Recent withdrawal requests */ ]
  }
}
```

## Postman Testing Examples

### Update Single Payout to Pending
```json
PATCH /api/admin/withdrawals/64f8a1b2c3d4e5f6a7b8c9d0/status
Headers: {
  "Authorization": "Bearer YOUR_ADMIN_TOKEN",
  "Content-Type": "application/json"
}
Body: {
  "status": "pending",
  "adminNotes": "Processing payout via Stripe"
}
```

### Update Single Payout to Failed
```json
PATCH /api/admin/withdrawals/64f8a1b2c3d4e5f6a7b8c9d0/status
Headers: {
  "Authorization": "Bearer YOUR_ADMIN_TOKEN",
  "Content-Type": "application/json"
}
Body: {
  "status": "failed",
  "failureReason": "Invalid bank account details",
  "adminNotes": "Tutor needs to update bank information"
}
```

### Update Single Payout to Paid
```json
PATCH /api/admin/withdrawals/64f8a1b2c3d4e5f6a7b8c9d0/status
Headers: {
  "Authorization": "Bearer YOUR_ADMIN_TOKEN",
  "Content-Type": "application/json"
}
Body: {
  "status": "paid",
  "stripeTransferId": "tr_1234567890",
  "adminNotes": "Successfully transferred via Stripe"
}
```

### Bulk Update Multiple Payouts
```json
POST /api/admin/withdrawals/bulk-status-update
Headers: {
  "Authorization": "Bearer YOUR_ADMIN_TOKEN",
  "Content-Type": "application/json"
}
Body: {
  "requestIds": [
    "64f8a1b2c3d4e5f6a7b8c9d0",
    "64f8a1b2c3d4e5f6a7b8c9d1",
    "64f8a1b2c3d4e5f6a7b8c9d2"
  ],
  "status": "paid",
  "adminNotes": "Weekly payout batch processed"
}
```

### Get Payout Analytics
```json
GET /api/admin/withdrawals/payout-analytics?startDate=2024-01-01&endDate=2024-01-31
Headers: {
  "Authorization": "Bearer YOUR_ADMIN_TOKEN"
}
```

## Error Handling

### **Common Errors**

**400 Bad Request - Invalid Status:**
```json
{
  "message": "Invalid status. Must be one of: pending, failed, paid"
}
```

**400 Bad Request - Missing Failure Reason:**
```json
{
  "message": "Failure reason is required for failed status"
}
```

**400 Bad Request - Invalid Request State:**
```json
{
  "message": "Cannot update status for withdrawal request in processed status"
}
```

**400 Bad Request - Insufficient Balance:**
```json
{
  "message": "Tutor has insufficient balance for this withdrawal"
}
```

## Key Features

### **Enhanced Status Tracking**
- Clear status progression from pending to final state
- Detailed failure reason tracking
- Comprehensive audit trail

### **Bulk Operations**
- Process multiple payouts simultaneously
- Detailed success/failure reporting
- Atomic operations with rollback on errors

### **Comprehensive Analytics**
- Status breakdown and trends
- Tutor-specific analytics
- Daily/weekly/monthly reporting

### **Financial Safety**
- Balance validation before processing
- Transaction record creation
- Audit trail maintenance

### **Integration Ready**
- Stripe transfer ID tracking
- External payment system integration
- Webhook-friendly status updates

## Best Practices

1. **Always provide failure reasons** for failed payouts
2. **Use bulk operations** for processing multiple payouts
3. **Monitor analytics regularly** for trends and issues
4. **Validate balances** before marking as paid
5. **Keep detailed admin notes** for audit purposes
6. **Use Stripe transfer IDs** for external tracking

The enhanced payout system provides complete control over withdrawal processing with improved tracking, bulk operations, and comprehensive analytics for better financial management.
