> 💡 **Platform** **Overview:** **Convolly**

**Convolly** is a **career-focused** **language** **learning**
**platform** that connects learners with tutors who specialize in
specific professional industries. This ensures users learn a language
that's directly relevant to their field (e.g., medical English, legal
Spanish, etc.). It serves three user groups: **Students**
**(Learners)**, **Tutors**, and **Admin** **Roles** (with sub-roles like Support and Curriculum specialists).

## SERVER SETUP

To install dependencies

```sh
npm install
```

To run the dev server for your app, use:

```sh
npm run dev
```

add .env in your root folder with the following key value pairs

```sh
PORT=port-number
MONGO_DEV_URI=mongo-dev-uri
MONGO_PROD_URI=mongo-prod-uri
NODE_ENV=development
JWT_SECRET=your-secret
SUPPORT_MAIL_PASSWORD="your-mail-password"
```

## NOTE

Neccessary details is to be added to the `src/config/constants.ts` file.

## CONTRIBUTIONS

The `dev-branch` is the default branch for everything pre-production.

The `main` branch is the master branch for production and deployment.

## Creating PR.

Create a `PR` within `your branch` do not push directly to `main` or `dev-branch` without approval.

## APP Features

> 󰳐 **Learner** **(Student)** **Features** **–** **Expanded**
>
> **1.** **Multilingual** **Content**
>
> ● App UI/UX, forms, messages, and system prompts should support i18n
> (internationalization) so learners can navigate in their preferred
> language.
>
> **2.** **Flexible** **Authentication** **Options**
>
> ● Login/Signup with Google, Facebook, LinkedIn, Apple, or
> email/password.
>
> ● OAuth integration ensures seamless entry points for busy
> professionals.
>
> **3.** **Smart** **Onboarding** **Flow**
>
> ● Choose type of English (e.g., Legal English, Business English)
>
> ● Select preferred schedule (days, time, recurrence)
>
> ● Could include a calendar sync (e.g., Google Calendar) for reminder
> setups.
>
> **4.** **Personal** **Dashboard**
>
> ● View booked lessons, upcoming sessions, tutor feedback.
>
> ● Summary view of all classes (past and future), including status.
>
> **5.** **Advanced** **Tutor** **Search**

Search filters:

> ● **Country**
>
> ● **Industry** **expertise** (e.g., medical, finance)
>
> ● **Availability**
>
> ● **Type** **of** **English** taught
>
> ● **Price** **range**
>
> ● **Tutor** **qualities**: Tags like “Tech-savvy,” “Great
> conversation,” “Goes the extra mile,” etc.
>
> **6.** **Lesson** **Confirmation**
>
> ● After a lesson ends, learner confirms it for the tutor to receive
> payment.
>
> **7.** **Tutor** **Reporting**
>
> ● Form/reporting tool for no-shows.
>
> **8.** **Ratings** **&** **Reviews**
>
> ● 5-star scale, written feedback. Potential moderation system for
> abuse.
>
> **9.** **Trial** **Lesson** **System**
>
> ● 2–3 paid trial lessons (refundable if none is chosen). Controlled
> via platform logic.
>
> **10.** **Classroom** **Tools**
>
> ● In-lesson notepad for taking notes.
>
> ● Personal dictionary to save vocabulary.
>
> ● Material sharing (upload PDFs, links, etc.)
>
> ● Real-time chat with tutor.
>
> **11.** **Referral** **System**
>
> ● Referral tracking.
>
> ● 20% commission for every paid lesson (excluding trials).
>
> 󰞹 **Tutor** **Features** **–** **Expanded**
>
> **1.** **Signup** **&** **Onboarding**
>
> ● OAuth or manual signup.
>
> ● Onboarding includes:
>
> ○ Personal bio, education, work experience
>
> ○ Internet connection test
>
> ○ Upload intro video/picture
>
> ○ Set availability via calendar
>
> ○ Submit for approval
>
> **2.** **Calendar** **&** **Notifications**
>
> ● Integrated calendar to manage bookings.
>
> ● Email notifications for:
>
> ○ New bookings
>
> ○ Reschedules
>
> ○ Messages
>
> **3.** **Lesson** **Interface**
>
> ● Video/audio classroom
>
> ● Shared library for materials (with tutor-student sync control)
>
> ● Page-locking logic (student must complete before answers are shown)
>
> **4.** **Tutor** **Profile** **Management**
>
> ● Edit profile, pricing, video, and images.
>
> **5.** **Chat** **Templates**
>
> ● Quick-reply templates (e.g., “Thanks for booking!” or “Please leave
> a review”)
>
> **6.** **Tutor** **Analytics**
>
> ● Profile views
>
> ● Rankings (based on review score, missed trials, availability)
>
> ● Student count
>
> ● Lessons taught
>
> ● Total earnings
>
> **7.** **Tutor** **Protections**
>
> ● Report abusive students
>
> ● Platform may disable student accounts upon report review.
>
> 🛠 **Admin** **Roles** **and** **Permissions** **–** **Expanded**
>
> **1.** **Authentication**
>
> ● Company-only email domains allowed.
>
> **2.** **Role-Based** **Access**
>
> ● Admins can assign permissions by department (e.g., support,
> marketing).
>
> **3.** **Management** **Functions**
>
> ● Approve/reject tutor applications
>
> ● Cancel reviews/ratings if necessary
>
> ● Approve tutor withdrawal requests
>
> ● Review and approve profile changes
>
> 🎧 **Support** **Team** **(Customer** **Care)**
>
> **Features:**
>
> ● Ticket management (chat or email)
>
> ● Reschedule or cancel student/tutor sessions
>
> ● Manage withdrawal and refund settlements
>
> ● Affiliate management support
>
> 📈 **Business/Marketing** **Team**
>
> ● View-only access to tutor and student databases
>
> ● Manage affiliate programs and commission payouts
>
> 📚 **ESL** **Curriculum** **Specialist**
>
> ● Create/update/delete library content (videos, images, writeups)
>
> ● View post-class feedback from both tutors and students
>
> 🧠 **Business** **Technical** **Logic** **(Automations** **&** > **Rankings)**
>
> **1.** **Trial** **Lesson** **No-Shows**
>
> ● 5 allowed missed trial lessons → account automatically deactivated.
>
> **2.** **Tutor** **Search** **Ranking** **Algorithm**

Rank =

> ✅ Total Reviews (Higher is better) ➖ Trial No-shows (Fewer is
> better)
>
> ✅ Calendar Availability (More available slots = higher ranking)
>
> **3.** **Dynamic** **Tutor** **Commission** **Model**

Based on **country** **of** **birth** and **teaching** **hours**:

> **Countr** **S** **After** **After** **After** **y** **Tier** **t** > **20** **100** **300**
>
> **a** **hrs** **hrs** **hrs** **r**
>
> **ti** **n** **g**
>
> 1st 2 20% 12% 12% World 5
>
> %
>
> 2nd 3 28% 15% 15% World 5
>
> %
>
> 3rd 4 35% 30% 30% World 5
>
> %
>
> **4.** **Email** **Automation** **Triggers**
>
> ● Profile click → tutor notified
>
> ● Lesson confirmed → tutor gets payment notification
>
> ● Subscribed/unsubscribed → both tutor & student notified
>
> ● Inactive response (24h) → hide tutor from search
>
> ● First message → alerts tutor, 24h timer starts
>
> ● Unfinished onboarding → email nudge
>
> ● Reschedule → notify affected parties
>
> **5.** **Content** **Privacy** **Guard**
>
> ● If tutor or student tries to share contact info (email/phone), block
> message and return: _“It_ _is_ _not_ _permitted_ _to_ _share_ > _private_ _contact_ _information.”_
