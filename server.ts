import dotenv from "dotenv";
dotenv.config();

import app from "./src/app";
import connectDB from "./src/config/db";
import createServerSocket from "./src/config/socket/server-socket";
import { updateMissingFieldForAllUsers } from "./src/utils/db";
import { DEFAULT_AUTH_PROVIDER } from "./src/config/constants";

const PORT = process.env.PORT || 8000;

const server = createServerSocket(app);

connectDB().then(() => {
  server.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);

    // updateMissingFieldForAllUsers("provider", DEFAULT_AUTH_PROVIDER);
  });
});
