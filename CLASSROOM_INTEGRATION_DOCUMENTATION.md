# Classroom Integration Documentation

## Overview

The booking system now automatically creates virtual classrooms for each tutoring session using the `createClassroomHook` function. This provides a dedicated space for tutors and students to conduct their sessions with integrated chat and video capabilities.

## Integration Points

### **Enhanced Booking Endpoint**
**POST** `/api/bookings/enhanced`

### **Schedule Booking Endpoint**
**POST** `/api/schedule/book-session`

Both endpoints now automatically create a classroom when a session is successfully booked.

## Classroom Creation Process

### **When Classroom is Created**
1. After session events are created in both tutor and student calendars
2. After lesson record is created and saved
3. Before sending the final response to the client

### **Classroom Configuration**
```typescript
const classroomPayload = {
  chatGroup: {
    name: `${title} - ${tutorName} & ${studentName}`,
    desc: `Virtual classroom for ${title} session on ${startDate.toLocaleDateString()}`,
    maxUsers: 2
  },
  visibility: 'students-only' as const,
  expireAt: new Date(endDate.getTime() + 24 * 60 * 60 * 1000) // Expires 24 hours after session ends
};
```

### **Three Arguments Passed to createClassroomHook**

1. **classroomPayload**: Contains chat group configuration and settings
2. **tutorProfile**: Full tutor profile from database
3. **studentProfile**: Full student profile from database

## Classroom Features

### **Chat Group**
- **Name**: Automatically generated as `"Session Title - Tutor Name & Student Name"`
- **Description**: Includes session title and date
- **Max Users**: Set to 2 (tutor and student only)
- **Owner**: Tutor is set as the chat group owner

### **Visibility**
- **Type**: `'students-only'`
- **Access**: Only the enrolled student and tutor can access
- **Privacy**: Not publicly visible

### **Expiration**
- **Duration**: Classroom expires 24 hours after session end time
- **Cleanup**: Automatically removed from database after expiration
- **Purpose**: Prevents accumulation of old classroom data

## API Response Updates

### **Enhanced Booking Response**
```json
{
  "success": true,
  "message": "Session booked successfully in both calendars",
  "data": {
    "tutorEvent": { /* event details */ },
    "studentEvent": { /* event details */ },
    "lesson": { /* lesson details */ },
    "classroom": {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
      "tutor": "64f8a1b2c3d4e5f6a7b8c9d1",
      "chatGroup": {
        "id": "agora_group_id_123"
      },
      "visibility": "students-only",
      "students": ["64f8a1b2c3d4e5f6a7b8c9d2"],
      "expireAt": "2024-01-21T15:00:00.000Z",
      "createdAt": "2024-01-20T14:00:00.000Z"
    },
    "isFreeTrial": false,
    "subscriptionInfo": { /* subscription details */ },
    "consumeMessage": "Lesson consumed successfully"
  }
}
```

### **Schedule Booking Response**
```json
{
  "success": true,
  "message": "Session booked successfully in both calendars",
  "data": {
    "tutorEvent": { /* event details */ },
    "studentEvent": { /* event details */ },
    "lesson": { /* lesson details */ },
    "classroom": { /* classroom details */ },
    "isFreeTrial": false,
    "subscriptionInfo": { /* subscription details */ },
    "consumeMessage": "Lesson consumed successfully"
  }
}
```

## Error Handling

### **Graceful Degradation**
- If classroom creation fails, the booking process continues
- Session events and lesson records are still created
- Error is logged but doesn't affect the booking success

### **Error Scenarios**
1. **Tutor Profile Not Found**: Classroom creation skipped
2. **Student Profile Not Found**: Classroom creation skipped
3. **Agora API Issues**: Classroom creation fails gracefully
4. **Network Issues**: Logged but doesn't block booking

### **Error Logging**
```typescript
try {
  classroom = await createClassroomHook(classroomPayload, tutorProfile, studentProfile);
  console.log(`Classroom created for session ${lesson._id}: ${classroom._id}`);
} catch (classroomError: any) {
  console.error('Error creating classroom:', classroomError);
  // Don't fail the booking if classroom creation fails
}
```

## Classroom Access

### **Getting Classroom Data**
**GET** `/api/classroom/:id`

Returns classroom information including chat group details.

### **Getting RTC Data**
**POST** `/api/classroom/rtc`

```json
{
  "channelId": "64f8a1b2c3d4e5f6a7b8c9d0"
}
```

Returns RTC tokens and chat credentials for joining the session.

### **Access Control**
- Only the tutor and enrolled student can access the classroom
- Authentication required for all classroom endpoints
- Automatic token generation for video/chat access

## Integration with Agora

### **Chat Group Creation**
- Creates Agora chat group automatically
- Registers tutor and student as chat users
- Generates group ID for communication

### **RTC Token Generation**
- Provides video call capabilities
- Generates tokens for both tutor and student
- Handles authentication with Agora services

### **User Registration**
- Automatically registers users with Agora chat service
- Uses existing usernames from profiles
- Creates chat users if they don't exist

## Database Schema

### **Classroom Model**
```typescript
{
  tutor: ObjectId, // Reference to tutor
  chatGroup: {
    id: string // Agora chat group ID
  },
  visibility: "students-only",
  expireAt: Date, // Auto-expiration
  students: [string], // Array of student IDs
  createdAt: Date,
  updatedAt: Date
}
```

## Postman Testing

### **Enhanced Booking with Classroom**
```json
POST /api/bookings/enhanced
Headers: {
  "Authorization": "Bearer YOUR_JWT_TOKEN",
  "Content-Type": "application/json"
}
Body: {
  "tutorId": "64f8a1b2c3d4e5f6a7b8c9d0",
  "studentId": "64f8a1b2c3d4e5f6a7b8c9d1",
  "title": "Math Lesson",
  "description": "Algebra and geometry review",
  "startDateTime": "2024-01-25T14:00:00.000Z",
  "endDateTime": "2024-01-25T15:00:00.000Z",
  "subject": "Mathematics"
}
```

### **Schedule Booking with Classroom**
```json
POST /api/schedule/book-session
Headers: {
  "Authorization": "Bearer YOUR_JWT_TOKEN",
  "Content-Type": "application/json"
}
Body: {
  "tutorId": "64f8a1b2c3d4e5f6a7b8c9d0",
  "title": "Physics Lesson",
  "description": "Mechanics and thermodynamics",
  "startDateTime": "2024-01-26T16:00:00.000Z",
  "endDateTime": "2024-01-26T17:00:00.000Z",
  "subject": "Physics"
}
```

### **Access Classroom**
```json
GET /api/classroom/64f8a1b2c3d4e5f6a7b8c9d0
Headers: {
  "Authorization": "Bearer YOUR_JWT_TOKEN"
}
```

### **Get RTC Data**
```json
POST /api/classroom/rtc
Headers: {
  "Authorization": "Bearer YOUR_JWT_TOKEN",
  "Content-Type": "application/json"
}
Body: {
  "channelId": "64f8a1b2c3d4e5f6a7b8c9d0"
}
```

## Benefits

### **Seamless Integration**
- Automatic classroom creation with every booking
- No additional steps required from users
- Integrated with existing booking workflow

### **Enhanced User Experience**
- Dedicated space for each session
- Integrated chat and video capabilities
- Automatic cleanup after session completion

### **Scalability**
- Leverages Agora's infrastructure
- Automatic user registration and management
- Efficient resource cleanup with expiration

### **Security**
- Private classrooms for each session
- Access control based on enrollment
- Secure token generation for communication

## Monitoring and Maintenance

### **Logging**
- Classroom creation success/failure logged
- Session-classroom mapping tracked
- Error details captured for debugging

### **Cleanup**
- Automatic expiration 24 hours after session
- Database cleanup handled by MongoDB TTL
- No manual intervention required

### **Health Monitoring**
- Monitor classroom creation success rates
- Track Agora API response times
- Alert on repeated failures

## Best Practices

1. **Always handle classroom creation errors gracefully**
2. **Log classroom IDs for session tracking**
3. **Monitor Agora API quotas and limits**
4. **Test classroom access before session start**
5. **Verify expiration times are set correctly**

The classroom integration provides a complete virtual learning environment that's automatically provisioned for each tutoring session, enhancing the overall learning experience while maintaining security and scalability.
