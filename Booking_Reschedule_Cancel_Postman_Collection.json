{"info": {"name": "Booking Reschedule & Cancel API", "description": "Test collection for the new booking reschedule, cancel, and tutor availability endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api", "type": "string"}, {"key": "authToken", "value": "YOUR_JWT_TOKEN_HERE", "type": "string"}, {"key": "lessonId", "value": "LESSON_ID_HERE", "type": "string"}, {"key": "tutorId", "value": "TUTOR_ID_HERE", "type": "string"}], "item": [{"name": "1. Reschedule Booking", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"newStartDateTime\": \"2024-02-15T15:00:00.000Z\",\n  \"newEndDateTime\": \"2024-02-15T16:00:00.000Z\",\n  \"reason\": \"Student requested different time due to schedule conflict\"\n}"}, "url": {"raw": "{{baseUrl}}/bookings/reschedule/{{lessonId}}", "host": ["{{baseUrl}}"], "path": ["bookings", "reschedule", "{{lessonId}}"]}, "description": "Reschedule an existing lesson to a new date and time"}}, {"name": "2. <PERSON><PERSON> Booking", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Student is sick and cannot attend the lesson\"\n}"}, "url": {"raw": "{{baseUrl}}/bookings/cancel/{{lessonId}}", "host": ["{{baseUrl}}"], "path": ["bookings", "cancel", "{{lessonId}}"]}, "description": "Cancel an existing lesson with a required reason"}}, {"name": "3. <PERSON> Tutor Booked Slots", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/bookings/tutor-booked-slots/{{tutorId}}?startDate=2024-01-15&endDate=2024-02-15&timezone=UTC", "host": ["{{baseUrl}}"], "path": ["bookings", "tutor-booked-slots", "{{tutorId}}"], "query": [{"key": "startDate", "value": "2024-01-15"}, {"key": "endDate", "value": "2024-02-15"}, {"key": "timezone", "value": "UTC"}]}, "description": "Get tutor's booked time slots (date and time only) for students to see availability"}}, {"name": "4. Create Enhanced Booking (Setup)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"tutorId\": \"{{tutorId}}\",\n  \"title\": \"Math Tutoring Session\",\n  \"description\": \"Algebra and geometry review\",\n  \"startDateTime\": \"2024-02-10T14:00:00.000Z\",\n  \"endDateTime\": \"2024-02-10T15:00:00.000Z\",\n  \"subject\": \"Mathematics\"\n}"}, "url": {"raw": "{{baseUrl}}/bookings/enhanced", "host": ["{{baseUrl}}"], "path": ["bookings", "enhanced"]}, "description": "Create a booking first to test reschedule/cancel functionality"}}, {"name": "5. Get Student Bookings (Verify)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/bookings/student", "host": ["{{baseUrl}}"], "path": ["bookings", "student"]}, "description": "Get student's bookings to verify reschedule/cancel operations"}}, {"name": "6. Get Tutor Availability (Compare)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/bookings/tutor-availability/{{tutorId}}?startDate=2024-01-15&endDate=2024-02-15&duration=60&timezone=UTC", "host": ["{{baseUrl}}"], "path": ["bookings", "tutor-availability", "{{tutorId}}"], "query": [{"key": "startDate", "value": "2024-01-15"}, {"key": "endDate", "value": "2024-02-15"}, {"key": "duration", "value": "60"}, {"key": "timezone", "value": "UTC"}]}, "description": "Compare with tutor availability to see the difference between available and booked slots"}}]}